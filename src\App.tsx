/**
 * Main App Component with Authentication for PressureMax
 * Handles authentication flow and routing
 */

import React, { useState, useEffect } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginModal } from './components/LoginModal';
import { SignupModal } from './components/SignupModal';
import { PasswordResetModal } from './components/PasswordResetModal';
import { ProtectedRoute } from './components/ProtectedRoute';
import { Dashboard } from './components/Dashboard';
import { LandingPage } from './components/LandingPage';
import { AdminLogin, AdminAccessButton } from './components/AdminLogin';
import { useAdminAuth } from './services/adminAuth';

// Main App Content Component (inside AuthProvider)
const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { isAdmin } = useAdminAuth();
  const [showLandingPage, setShowLandingPage] = useState(false);
  const [authModalType, setAuthModalType] = useState<'login' | 'signup' | 'reset' | null>(null);
  const [showAdminLogin, setShowAdminLogin] = useState(false);

  useEffect(() => {
    // Check if user has seen landing page
    const hasSeenLanding = localStorage.getItem('pressuremax_seen_landing');
    if (!isAuthenticated && hasSeenLanding !== 'true') {
      setShowLandingPage(true);
    }
  }, [isAuthenticated]);

  const handleEnterDashboard = () => {
    setShowLandingPage(false);
    localStorage.setItem('pressuremax_seen_landing', 'true');
    
    // If not authenticated, show login modal
    if (!isAuthenticated) {
      setAuthModalType('login');
    }
  };

  const handleCloseAuthModal = () => {
    setAuthModalType(null);
  };

  const handleSwitchToSignup = () => {
    setAuthModalType('signup');
  };

  const handleSwitchToLogin = () => {
    setAuthModalType('login');
  };

  const handleSwitchToPasswordReset = () => {
    setAuthModalType('reset');
  };

  const handleAdminAccess = () => {
    setShowAdminLogin(true);
  };

  const handleAdminLoginSuccess = () => {
    setShowAdminLogin(false);
    // User will be redirected to admin panel automatically
  };

  const handleAdminLoginCancel = () => {
    setShowAdminLogin(false);
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500 mx-auto"></div>
          <p className="text-gray-300">Loading PressureMax...</p>
        </div>
      </div>
    );
  }

  // Show landing page if user hasn't seen it yet
  if (showLandingPage) {
    return <LandingPage onEnterDashboard={handleEnterDashboard} />;
  }

  // If authenticated, show the dashboard
  if (isAuthenticated) {
    return (
      <>
        <ProtectedRoute>
          <Dashboard />
        </ProtectedRoute>

        {/* Admin Access Button (only show if not already admin) */}
        {!isAdmin(user) && (
          <AdminAccessButton onClick={handleAdminAccess} />
        )}

        {/* Admin Login Modal */}
        <AdminLogin
          isOpen={showAdminLogin}
          onSuccess={handleAdminLoginSuccess}
          onCancel={handleAdminLoginCancel}
        />
      </>
    );
  }

  // If not authenticated and not showing landing page, show login prompt
  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center space-y-6 max-w-md mx-auto p-6">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold text-white font-orbitron">PressureMax</h1>
          <p className="text-gray-300">
            The ultimate marketing automation platform for pressure washing businesses.
          </p>
        </div>
        
        <div className="space-y-4">
          <button
            onClick={() => setAuthModalType('login')}
            className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 text-black py-3 px-6 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
          >
            Sign In
          </button>
          
          <button
            onClick={() => setAuthModalType('signup')}
            className="w-full border-2 border-cyan-500 text-cyan-400 py-3 px-6 font-bold hover:bg-cyan-500 hover:text-black transition-all duration-300"
          >
            Create Account
          </button>
          
          <button
            onClick={() => setShowLandingPage(true)}
            className="text-gray-400 hover:text-cyan-400 transition-colors text-sm"
          >
            ← Back to Landing Page
          </button>
        </div>
      </div>

      {/* Authentication Modals */}
      <LoginModal
        isOpen={authModalType === 'login'}
        onClose={handleCloseAuthModal}
        onSwitchToSignup={handleSwitchToSignup}
        onSwitchToPasswordReset={handleSwitchToPasswordReset}
      />

      <SignupModal
        isOpen={authModalType === 'signup'}
        onClose={handleCloseAuthModal}
        onSwitchToLogin={handleSwitchToLogin}
      />

      <PasswordResetModal
        isOpen={authModalType === 'reset'}
        onClose={handleCloseAuthModal}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </div>
  );
};

// Main App Component with AuthProvider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
