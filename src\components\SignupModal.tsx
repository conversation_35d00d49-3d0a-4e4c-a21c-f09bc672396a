/**
 * Signup Modal Component for PressureMax
 * Handles new user registration with business information
 */

import React, { useState } from 'react';
import { X, Mail, Lock, Eye, EyeOff, User, Building, Phone, Loader, AlertCircle, CheckCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { SignupData } from '../services/auth';

interface SignupModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToLogin: () => void;
}

export const SignupModal: React.FC<SignupModalProps> = ({
  isOpen,
  onClose,
  onSwitchToLogin
}) => {
  const { signup, isLoading } = useAuth();
  const [formData, setFormData] = useState<SignupData>({
    email: '',
    password: '',
    name: '',
    company_name: '',
    phone: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  });
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);

  const validatePassword = (password: string): string[] => {
    const errors: string[] = [];
    if (password.length < 8) errors.push('At least 8 characters');
    if (!/[A-Z]/.test(password)) errors.push('One uppercase letter');
    if (!/[a-z]/.test(password)) errors.push('One lowercase letter');
    if (!/\d/.test(password)) errors.push('One number');
    return errors;
  };

  const passwordErrors = validatePassword(formData.password);
  const passwordsMatch = formData.password === confirmPassword;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validation
    if (passwordErrors.length > 0) {
      setError('Password does not meet requirements');
      return;
    }

    if (!passwordsMatch) {
      setError('Passwords do not match');
      return;
    }

    if (!acceptTerms) {
      setError('Please accept the Terms of Service and Privacy Policy');
      return;
    }

    setIsSubmitting(true);

    try {
      await signup(formData);
      onClose();
      // Reset form
      setFormData({
        email: '',
        password: '',
        name: '',
        company_name: '',
        phone: '',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      });
      setConfirmPassword('');
      setAcceptTerms(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Signup failed');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof SignupData | 'confirmPassword', value: string) => {
    if (field === 'confirmPassword') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));
    }
    if (error) setError(''); // Clear error when user starts typing
  };

  const handleClose = () => {
    setFormData({
      email: '',
      password: '',
      name: '',
      company_name: '',
      phone: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    });
    setConfirmPassword('');
    setError('');
    setShowPassword(false);
    setShowConfirmPassword(false);
    setAcceptTerms(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 border border-gray-700 max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-bold text-white">Create Your Account</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isSubmitting}
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Error Message */}
            {error && (
              <div className="bg-red-900/20 border border-red-500 text-red-400 px-4 py-3 flex items-center space-x-2">
                <AlertCircle size={16} />
                <span className="text-sm">{error}</span>
              </div>
            )}

            {/* Name Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Full Name *
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="John Doe"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Company Name Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Company Name *
              </label>
              <div className="relative">
                <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  value={formData.company_name}
                  onChange={(e) => handleInputChange('company_name', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="Pressure Washing Pro"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Email Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email Address *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="<EMAIL>"
                  required
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Phone Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Phone Number
              </label>
              <div className="relative">
                <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="(*************"
                  disabled={isSubmitting}
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Password *
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-10 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="Create a strong password"
                  required
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                  disabled={isSubmitting}
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              
              {/* Password Requirements */}
              {formData.password && (
                <div className="mt-2 space-y-1">
                  {passwordErrors.map((error, index) => (
                    <div key={index} className="flex items-center space-x-2 text-xs">
                      <X className="text-red-400" size={12} />
                      <span className="text-red-400">{error}</span>
                    </div>
                  ))}
                  {passwordErrors.length === 0 && (
                    <div className="flex items-center space-x-2 text-xs">
                      <CheckCircle className="text-green-400" size={12} />
                      <span className="text-green-400">Password meets requirements</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Confirm Password Field */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Confirm Password *
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                  className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-10 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="Confirm your password"
                  required
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                  disabled={isSubmitting}
                >
                  {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              
              {/* Password Match Indicator */}
              {confirmPassword && (
                <div className="mt-2">
                  {passwordsMatch ? (
                    <div className="flex items-center space-x-2 text-xs">
                      <CheckCircle className="text-green-400" size={12} />
                      <span className="text-green-400">Passwords match</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 text-xs">
                      <X className="text-red-400" size={12} />
                      <span className="text-red-400">Passwords do not match</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Terms Acceptance */}
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="acceptTerms"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                className="mt-1 h-4 w-4 text-cyan-500 bg-gray-800 border-gray-600 focus:ring-cyan-500"
                disabled={isSubmitting}
              />
              <label htmlFor="acceptTerms" className="text-sm text-gray-300">
                I agree to the{' '}
                <a href="/terms" className="text-cyan-400 hover:text-cyan-300" target="_blank">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="/privacy" className="text-cyan-400 hover:text-cyan-300" target="_blank">
                  Privacy Policy
                </a>
              </label>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={
                isSubmitting || 
                !formData.email || 
                !formData.password || 
                !formData.name || 
                !formData.company_name ||
                passwordErrors.length > 0 ||
                !passwordsMatch ||
                !acceptTerms
              }
              className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 text-black py-3 px-6 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <Loader className="animate-spin" size={16} />
                  <span>Creating Account...</span>
                </>
              ) : (
                <span>Create Account</span>
              )}
            </button>
          </form>

          {/* Divider */}
          <div className="my-6 flex items-center">
            <div className="flex-1 border-t border-gray-600"></div>
            <span className="px-4 text-gray-400 text-sm">or</span>
            <div className="flex-1 border-t border-gray-600"></div>
          </div>

          {/* Login Link */}
          <div className="text-center">
            <span className="text-gray-400">Already have an account? </span>
            <button
              onClick={onSwitchToLogin}
              className="text-cyan-400 hover:text-cyan-300 transition-colors font-medium"
              disabled={isSubmitting}
            >
              Sign in
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
