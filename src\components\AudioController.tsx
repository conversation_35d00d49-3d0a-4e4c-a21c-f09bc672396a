import React, { useState, useRef, useEffect } from 'react';
import { Volume2, VolumeX } from 'lucide-react';

const AudioController: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Create ambient audio context
  useEffect(() => {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(55, audioContext.currentTime); // Low frequency drone
    oscillator.type = 'sine';
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);

    if (isPlaying && !isMuted) {
      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.5);
      oscillator.start();
    }

    return () => {
      try {
        oscillator.stop();
        audioContext.close();
      } catch (e) {
        // Audio context might already be closed
      }
    };
  }, [isPlaying, isMuted]);

  const toggleAudio = () => {
    setIsMuted(!isMuted);
    if (!isPlaying) {
      setIsPlaying(true);
    }
  };

  return (
    <div className="fixed bottom-4 left-4 z-40">
      <button
        onClick={toggleAudio}
        className={`p-3 border-2 transition-all duration-300 ${
          isMuted 
            ? 'border-gray-600 text-gray-600 hover:border-gray-400' 
            : 'border-red-500 text-red-500 hover:bg-red-500 hover:text-black'
        }`}
        title={isMuted ? 'Enable ambient audio' : 'Disable ambient audio'}
      >
        {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
      </button>
      
      {isPlaying && !isMuted && (
        <div className="absolute -top-2 -right-2 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
      )}
    </div>
  );
};

export default AudioController;