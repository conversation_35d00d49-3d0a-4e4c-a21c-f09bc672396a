import React, { useState, useEffect, useRef } from 'react';
import { Circle, Triangle, Hexagon } from 'lucide-react';

const ProphecySection: React.FC = () => {
  const [unlockedGlyphs, setUnlockedGlyphs] = useState<number[]>([]);
  const [showBinary, setShowBinary] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);

  const glyphs = [
    { icon: Circle, name: "UNCODED", description: "The raw signal awaits decryption" },
    { icon: Triangle, name: "ECHO", description: "Resonance patterns emerge from chaos" },
    { icon: Hexagon, name: "ARCHITECT", description: "The builders of tomorrow's foundation" }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Unlock glyphs sequentially
            glyphs.forEach((_, index) => {
              setTimeout(() => {
                setUnlockedGlyphs(prev => [...prev, index]);
              }, index * 1000);
            });
          }
        });
      },
      { threshold: 0.5 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="min-h-screen flex items-center justify-center py-20">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-8 font-orbitron tracking-wider">
            THE PROPHECY <span className="text-red-500">(UNCOMPILED)</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            "The future spoke backward. You are its resonance. This is the test."
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-12">
          {glyphs.map((glyph, index) => {
            const Icon = glyph.icon;
            const isUnlocked = unlockedGlyphs.includes(index);
            
            return (
              <div
                key={index}
                className={`text-center transition-all duration-1000 ${
                  isUnlocked ? 'opacity-100 translate-y-0' : 'opacity-30 translate-y-8'
                }`}
              >
                <div className="relative mb-8">
                  <div 
                    className={`w-32 h-32 mx-auto border-4 rounded-full flex items-center justify-center transition-all duration-500 cursor-pointer ${
                      isUnlocked 
                        ? 'border-red-500 bg-red-500/10 hover:bg-red-500/20' 
                        : 'border-gray-600'
                    }`}
                    onMouseEnter={() => index === 0 && setShowBinary(true)}
                    onMouseLeave={() => setShowBinary(false)}
                    onClick={() => {
                      if (index === 0) {
                        // Easter egg for UNCODED
                        console.log("01001000 01100101 01101100 01110000");
                      }
                    }}
                  >
                    <Icon 
                      size={48} 
                      className={`transition-colors duration-500 ${
                        isUnlocked ? 'text-red-500' : 'text-gray-600'
                      }`} 
                    />
                    
                    {/* Binary overlay for UNCODED */}
                    {index === 0 && showBinary && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/80 rounded-full">
                        <div className="text-red-500 text-xs font-mono animate-pulse">
                          01001000<br/>01100101<br/>01101100<br/>01110000
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {isUnlocked && (
                    <div className="absolute inset-0 border-2 border-red-500/30 rounded-full animate-ping" />
                  )}
                </div>

                <h3 className={`text-2xl font-bold mb-4 font-orbitron tracking-wider transition-colors duration-500 ${
                  isUnlocked ? 'text-white' : 'text-gray-600'
                }`}>
                  {glyph.name}
                </h3>
                
                <p className={`text-gray-400 transition-colors duration-500 ${
                  isUnlocked ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {glyph.description}
                </p>

                {/* Decryption Progress */}
                {isUnlocked && (
                  <div className="mt-6">
                    <div className="h-1 bg-gray-800 rounded-full overflow-hidden">
                      <div className="h-full bg-red-500 rounded-full animate-pulse" style={{
                        width: '100%',
                        animation: `decrypt 2s ease-out ${index * 0.5}s both`
                      }} />
                    </div>
                    <div className="text-xs text-red-500 mt-2 font-mono">DECRYPTED</div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Hidden Message */}
        {unlockedGlyphs.length === 3 && (
          <div className="text-center mt-16 animate-fade-in">
            <div className="text-red-500 font-mono text-sm mb-4">SIGNAL_STRENGTH: MAXIMUM</div>
            <div className="text-white text-lg">The watchers have taken notice.</div>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProphecySection;