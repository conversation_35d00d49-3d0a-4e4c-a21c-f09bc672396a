/**
 * Authentication Context for PressureMax
 * Provides authentication state and methods throughout the app
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { supabaseAuthService, AuthResponse, LoginCredentials, SignupData } from '../services/supabaseAuth';
import { User } from '../types/database';
import { sessionManager } from '../services/sessionManager';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  signup: (data: SignupData) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<User>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingAuth, setIsProcessingAuth] = useState(false);

  useEffect(() => {
    initializeAuth();

    // Fallback: if still loading after 1.5 seconds, force stop loading
    const fallbackTimeout = setTimeout(() => {
      if (isLoading) {
        console.log('Fallback: Stopping loading after 1.5 seconds');
        setIsLoading(false);
        // Don't set user to null - keep whatever user state we have
      }
    }, 1500);

    return () => clearTimeout(fallbackTimeout);
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Set a longer timeout to prevent premature fallbacks
      const timeoutId = setTimeout(() => {
        console.log('Auth initialization timeout - setting loading to false');
        setIsLoading(false);
        // Don't set user to null here - keep whatever user state we have
      }, 10000); // 10 second timeout (increased from 2 seconds)

      // Listen to auth state changes first
      supabaseAuthService.onAuthStateChange(async (event, session) => {
        console.log('Auth state change:', event, session?.user?.email);

        // Prevent multiple concurrent auth processing
        if (isProcessingAuth) {
          console.log('Skipping auth event - already processing');
          return;
        }

        if (event === 'SIGNED_IN' && session?.user) {
          setIsProcessingAuth(true);
          console.log('Processing SIGNED_IN event for:', session.user.email);
          try {
            // Refresh session manager
            sessionManager.refreshSession();

            // Load full user profile from database
            const fullUser = await supabaseAuthService.getCurrentUser();
            setUser(fullUser);
            console.log('Loaded full user profile with integrations on sign in');
          } catch (error) {
            console.error('Error loading user profile on sign in:', error);
            // Fallback to minimal user if profile fetch fails
            const minimalUser = {
              id: session.user.id,
              email: session.user.email || '',
              name: session.user.user_metadata?.name || 'User',
              company_name: session.user.user_metadata?.company_name || 'Company',
              phone: session.user.user_metadata?.phone || null,
              role: 'user' as const,
              permissions: [],
              plan: 'starter' as const,
              subscription_status: 'active' as const,
              facebook_access_token: null,
              facebook_ad_account_id: null,
              facebook_page_id: null,
              vapi_api_key: null,
              vapi_assistant_id: null,
              timezone: 'UTC',
              business_hours: {
                start: '09:00',
                end: '17:00',
                days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
              },
              created_at: new Date(),
              updated_at: new Date(),
              last_login_at: undefined
            };
            console.log('Created fallback user from session:', minimalUser.email);
            setUser(minimalUser);
          }
          setIsLoading(false);
          setIsProcessingAuth(false);
          clearTimeout(timeoutId);
        } else if (event === 'SIGNED_OUT') {
          setIsProcessingAuth(true);
          setUser(null);
          setIsLoading(false);
          setIsProcessingAuth(false);
          clearTimeout(timeoutId);
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          setIsProcessingAuth(true);
          console.log('Processing TOKEN_REFRESHED event for:', session.user.email);
          try {
            // Refresh user profile when token is refreshed
            const fullUser = await supabaseAuthService.getCurrentUser();
            setUser(fullUser);
            console.log('Refreshed user profile after token refresh');
          } catch (error) {
            console.error('Error refreshing user profile after token refresh:', error);
          }
          setIsProcessingAuth(false);
        } else if (event === 'INITIAL_SESSION') {
          setIsProcessingAuth(true);
          console.log('Processing INITIAL_SESSION event, user:', session?.user?.email);
          // Handle initial session load
          if (session?.user) {
            try {
              // Load full user profile from database
              const fullUser = await supabaseAuthService.getCurrentUser();
              setUser(fullUser);
              console.log('Loaded full user profile with integrations on initial session');
            } catch (error) {
              console.error('Error loading user profile on initial session:', error);
              // Fallback to minimal user if profile fetch fails
              const minimalUser = {
                id: session.user.id,
                email: session.user.email || '',
                name: session.user.user_metadata?.name || 'User',
                company_name: session.user.user_metadata?.company_name || 'Company',
                phone: session.user.user_metadata?.phone || null,
                role: 'user' as const,
                permissions: [],
                plan: 'starter' as const,
                subscription_status: 'active' as const,
                facebook_access_token: null,
                facebook_ad_account_id: null,
                facebook_page_id: null,
                vapi_api_key: null,
                vapi_assistant_id: null,
                timezone: 'UTC',
                business_hours: {
                  start: '09:00',
                  end: '17:00',
                  days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
                },
                created_at: new Date(),
                updated_at: new Date(),
                last_login_at: undefined
              };
              console.log('Created fallback user from initial session:', minimalUser.email);
              setUser(minimalUser);
            }
          } else {
            setUser(null);
          }
          setIsLoading(false);
          setIsProcessingAuth(false);
          clearTimeout(timeoutId);
        }
      });

      // Check if user is already authenticated and load full profile from database
      // Add a small delay to let the auth state listener initialize
      setTimeout(async () => {
        try {
          const { data: { session } } = await supabaseAuthService.getSession();
          if (session?.user) {
            console.log('Found existing session for:', session.user.email);

            try {
              // Load full user profile from database
              const fullUser = await supabaseAuthService.getCurrentUser();
              setUser(fullUser);
              console.log('Loaded full user profile with integrations');
            } catch (profileError) {
              console.error('Error loading user profile:', profileError);
              // Fallback to minimal user if profile fetch fails
              const minimalUser = {
                id: session.user.id,
                email: session.user.email || '',
                name: session.user.user_metadata?.name || 'User',
                company_name: session.user.user_metadata?.company_name || 'Company',
                phone: session.user.user_metadata?.phone || null,
                role: 'user' as const,
                permissions: [],
                plan: 'starter' as const,
                subscription_status: 'active' as const,
                facebook_access_token: null,
                facebook_ad_account_id: null,
                facebook_page_id: null,
                vapi_api_key: null,
                vapi_assistant_id: null,
                timezone: 'UTC',
                business_hours: {
                  start: '09:00',
                  end: '17:00',
                  days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
                },
                created_at: new Date(),
                updated_at: new Date(),
                last_login_at: undefined
              };
              setUser(minimalUser);
            }
          } else {
            console.log('No existing session found');
            setUser(null);
          }
          setIsLoading(false);
          clearTimeout(timeoutId);
        } catch (error) {
          // This is expected when no user is logged in
          console.log('No current user session (this is normal on first load)');
          setUser(null);
          setIsLoading(false);
          clearTimeout(timeoutId);
        }
      }, 100);
    } catch (error) {
      console.error('Auth initialization error:', error);
      setUser(null);
      setIsLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      setIsLoading(true);
      const authData = await supabaseAuthService.login(credentials);
      setUser(authData.user);
      return authData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (data: SignupData): Promise<AuthResponse> => {
    try {
      setIsLoading(true);
      const authData = await supabaseAuthService.signup(data);
      setUser(authData.user);
      return authData;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      // Use session manager for comprehensive logout
      await sessionManager.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout locally even if remote logout fails
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<User>): Promise<User> => {
    try {
      const updatedUser = await supabaseAuthService.updateProfile(updates);
      setUser(updatedUser);
      return updatedUser;
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      // Try to get the full profile, but fall back to session if it fails
      const { data: { session } } = await supabaseAuthService.getSession();
      if (session?.user) {
        try {
          const currentUser = await supabaseAuthService.getCurrentUser();
          if (currentUser) {
            setUser(currentUser);
            return;
          }
        } catch (error) {
          console.log('Failed to get full profile, using session data');
        }

        // Fallback to session data
        const minimalUser = {
          id: session.user.id,
          email: session.user.email || '',
          name: session.user.user_metadata?.name || 'User',
          company_name: session.user.user_metadata?.company_name || 'Company',
          phone: session.user.user_metadata?.phone || null,
          role: 'user' as const,
          permissions: [],
          plan: 'starter' as const,
          subscription_status: 'active' as const,
          facebook_access_token: null,
          facebook_ad_account_id: null,
          facebook_page_id: null,
          vapi_api_key: null,
          vapi_assistant_id: null,
          timezone: 'UTC',
          business_hours: {
            start: '09:00',
            end: '17:00',
            days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
          },
          created_at: new Date(),
          updated_at: new Date(),
          last_login_at: undefined
        };
        setUser(minimalUser);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    signup,
    logout,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
