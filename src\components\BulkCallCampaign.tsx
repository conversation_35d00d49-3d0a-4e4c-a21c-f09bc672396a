/**
 * Bulk Call Campaign Component for PressureMax
 * Handles bulk calling for lead reactivation campaigns with scheduling and batch processing
 */

import React, { useState, useEffect } from 'react';
import {
  Phone,
  PhoneCall,
  Users,
  Clock,
  Play,
  Pause,
  Square,
  Calendar,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  Download,
  Upload,
  Settings,
  Target,
  Plus
} from 'lucide-react';
import { Lead } from '../types/database';
import { db } from '../services/database';

interface BulkCampaign {
  id: string;
  name: string;
  description: string;
  leads: Lead[];
  status: 'draft' | 'scheduled' | 'running' | 'paused' | 'completed' | 'cancelled';
  scheduled_start?: Date;
  started_at?: Date;
  completed_at?: Date;
  calls_made: number;
  calls_successful: number;
  appointments_booked: number;
  total_cost: number;
  assistant_id?: string;
  call_script?: string;
  settings: {
    call_delay_seconds: number;
    max_attempts_per_lead: number;
    business_hours_only: boolean;
    retry_no_answer: boolean;
    retry_delay_hours: number;
  };
}

interface BulkCallCampaignProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedLeads?: Lead[];
}

export const BulkCallCampaign: React.FC<BulkCallCampaignProps> = ({
  isOpen,
  onClose,
  preselectedLeads = []
}) => {
  const [campaigns, setCampaigns] = useState<BulkCampaign[]>([]);
  const [availableLeads, setAvailableLeads] = useState<Lead[]>([]);
  const [selectedLeads, setSelectedLeads] = useState<Lead[]>(preselectedLeads);
  const [currentCampaign, setCurrentCampaign] = useState<BulkCampaign | null>(null);
  const [isCreatingCampaign, setIsCreatingCampaign] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [filterCriteria, setFilterCriteria] = useState({
    status: 'all',
    source: 'all',
    quality: 'all',
    last_contact: 'all'
  });

  // Campaign form data
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    scheduled_start: '',
    call_script: '',
    settings: {
      call_delay_seconds: 5,
      max_attempts_per_lead: 3,
      business_hours_only: true,
      retry_no_answer: true,
      retry_delay_hours: 24
    }
  });

  useEffect(() => {
    if (isOpen) {
      loadCampaigns();
      loadAvailableLeads();
    }
  }, [isOpen]);

  const loadCampaigns = async () => {
    try {
      // Load real bulk call campaigns from backend
      // For now, start with empty array - campaigns will be created when users initiate bulk calls
      const campaigns: BulkCampaign[] = [];
      setCampaigns(campaigns);
      console.log('📞 Loaded bulk call campaigns');
    } catch (error) {
      console.error('❌ Error loading bulk call campaigns:', error);
    }
  };

  const loadAvailableLeads = async () => {
    try {
      const leads = await db.getLeads();
      // Filter for leads suitable for reactivation (not new, not recently contacted)
      const reactivationLeads = leads.filter(lead => 
        lead.status !== 'new' && 
        (!lead.last_contact_attempt || 
         Date.now() - new Date(lead.last_contact_attempt).getTime() > 7 * 24 * 60 * 60 * 1000) // 7 days
      );
      setAvailableLeads(reactivationLeads);
    } catch (error) {
      console.error('Error loading leads:', error);
    }
  };

  const createCampaign = async () => {
    if (!campaignForm.name || selectedLeads.length === 0) {
      alert('Please provide campaign name and select leads');
      return;
    }

    setIsLoading(true);
    try {
      const newCampaign: BulkCampaign = {
        id: `campaign_${Date.now()}`,
        name: campaignForm.name,
        description: campaignForm.description,
        leads: selectedLeads,
        status: campaignForm.scheduled_start ? 'scheduled' : 'draft',
        scheduled_start: campaignForm.scheduled_start ? new Date(campaignForm.scheduled_start) : undefined,
        calls_made: 0,
        calls_successful: 0,
        appointments_booked: 0,
        total_cost: 0,
        call_script: campaignForm.call_script,
        settings: campaignForm.settings
      };

      // Save campaign
      setCampaigns(prev => [...prev, newCampaign]);
      
      // Reset form
      setCampaignForm({
        name: '',
        description: '',
        scheduled_start: '',
        call_script: '',
        settings: {
          call_delay_seconds: 5,
          max_attempts_per_lead: 3,
          business_hours_only: true,
          retry_no_answer: true,
          retry_delay_hours: 24
        }
      });
      setSelectedLeads([]);
      setIsCreatingCampaign(false);

      alert('Campaign created successfully!');
    } catch (error) {
      console.error('Error creating campaign:', error);
      alert('Failed to create campaign');
    } finally {
      setIsLoading(false);
    }
  };

  const startCampaign = async (campaign: BulkCampaign) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/vapi/bulk-call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_token')}`
        },
        body: JSON.stringify({
          leads: campaign.leads.map(lead => ({
            id: lead.id,
            name: lead.name,
            phone: lead.phone,
            service: lead.service,
            source: lead.source
          })),
          campaign_id: campaign.id,
          settings: campaign.settings
        })
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update campaign status
        setCampaigns(prev => prev.map(c => 
          c.id === campaign.id 
            ? { ...c, status: 'running', started_at: new Date() }
            : c
        ));

        alert(`Campaign started! ${result.calls_initiated} calls initiated.`);
      } else {
        throw new Error('Failed to start campaign');
      }
    } catch (error) {
      console.error('Error starting campaign:', error);
      alert('Failed to start campaign');
    } finally {
      setIsLoading(false);
    }
  };

  const pauseCampaign = async (campaignId: string) => {
    setCampaigns(prev => prev.map(c => 
      c.id === campaignId ? { ...c, status: 'paused' } : c
    ));
  };

  const stopCampaign = async (campaignId: string) => {
    setCampaigns(prev => prev.map(c => 
      c.id === campaignId ? { ...c, status: 'cancelled' } : c
    ));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-400';
      case 'scheduled':
        return 'text-blue-400';
      case 'paused':
        return 'text-yellow-400';
      case 'completed':
        return 'text-gray-400';
      case 'cancelled':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Play size={16} />;
      case 'scheduled':
        return <Clock size={16} />;
      case 'paused':
        return <Pause size={16} />;
      case 'completed':
        return <CheckCircle size={16} />;
      case 'cancelled':
        return <XCircle size={16} />;
      default:
        return <AlertCircle size={16} />;
    }
  };

  const filteredLeads = availableLeads.filter(lead => {
    if (filterCriteria.status !== 'all' && lead.status !== filterCriteria.status) return false;
    if (filterCriteria.source !== 'all' && lead.source !== filterCriteria.source) return false;
    if (filterCriteria.quality !== 'all' && lead.quality !== filterCriteria.quality) return false;
    
    if (filterCriteria.last_contact !== 'all') {
      const daysSinceContact = lead.last_contact_attempt 
        ? (Date.now() - new Date(lead.last_contact_attempt).getTime()) / (1000 * 60 * 60 * 24)
        : Infinity;
      
      switch (filterCriteria.last_contact) {
        case 'week':
          if (daysSinceContact < 7) return false;
          break;
        case 'month':
          if (daysSinceContact < 30) return false;
          break;
        case 'quarter':
          if (daysSinceContact < 90) return false;
          break;
      }
    }
    
    return true;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-900 border border-gray-700 max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-white">Bulk Call Campaigns</h2>
            <p className="text-sm text-gray-400">Reactivate existing leads with automated calling</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <XCircle size={24} />
          </button>
        </div>

        <div className="p-6">
          {!isCreatingCampaign ? (
            <div className="space-y-6">
              {/* Campaign Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-gray-800 border border-gray-600 p-4">
                  <h3 className="text-sm font-medium text-gray-400">Total Campaigns</h3>
                  <p className="text-2xl font-bold text-white">{campaigns.length}</p>
                </div>
                <div className="bg-gray-800 border border-gray-600 p-4">
                  <h3 className="text-sm font-medium text-gray-400">Active Campaigns</h3>
                  <p className="text-2xl font-bold text-green-400">
                    {campaigns.filter(c => c.status === 'running').length}
                  </p>
                </div>
                <div className="bg-gray-800 border border-gray-600 p-4">
                  <h3 className="text-sm font-medium text-gray-400">Total Calls Made</h3>
                  <p className="text-2xl font-bold text-cyan-400">
                    {campaigns.reduce((sum, c) => sum + c.calls_made, 0)}
                  </p>
                </div>
                <div className="bg-gray-800 border border-gray-600 p-4">
                  <h3 className="text-sm font-medium text-gray-400">Appointments Booked</h3>
                  <p className="text-2xl font-bold text-yellow-400">
                    {campaigns.reduce((sum, c) => sum + c.appointments_booked, 0)}
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">Campaign Management</h3>
                <button
                  onClick={() => setIsCreatingCampaign(true)}
                  className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-4 py-2 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
                >
                  <Plus size={16} />
                  <span>Create Campaign</span>
                </button>
              </div>

              {/* Campaigns List */}
              <div className="space-y-4">
                {campaigns.length === 0 ? (
                  <div className="text-center py-12">
                    <PhoneCall className="mx-auto text-gray-500 mb-4" size={48} />
                    <h3 className="text-lg font-medium text-gray-400 mb-2">No campaigns yet</h3>
                    <p className="text-gray-500">Create your first bulk calling campaign to reactivate leads</p>
                  </div>
                ) : (
                  campaigns.map((campaign) => (
                    <div key={campaign.id} className="bg-gray-800 border border-gray-600 p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h4 className="text-lg font-semibold text-white">{campaign.name}</h4>
                          <p className="text-sm text-gray-400">{campaign.description}</p>
                        </div>
                        <div className={`flex items-center space-x-2 ${getStatusColor(campaign.status)}`}>
                          {getStatusIcon(campaign.status)}
                          <span className="font-medium capitalize">{campaign.status}</span>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-gray-400">Leads</p>
                          <p className="text-lg font-bold text-white">{campaign.leads.length}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-400">Calls Made</p>
                          <p className="text-lg font-bold text-cyan-400">{campaign.calls_made}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-400">Success Rate</p>
                          <p className="text-lg font-bold text-green-400">
                            {campaign.calls_made > 0 
                              ? Math.round((campaign.calls_successful / campaign.calls_made) * 100)
                              : 0}%
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-400">Appointments</p>
                          <p className="text-lg font-bold text-yellow-400">{campaign.appointments_booked}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-400">Cost</p>
                          <p className="text-lg font-bold text-white">${campaign.total_cost.toFixed(2)}</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {campaign.status === 'draft' && (
                          <button
                            onClick={() => startCampaign(campaign)}
                            disabled={isLoading}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 transition-colors flex items-center space-x-2 disabled:opacity-50"
                          >
                            <Play size={16} />
                            <span>Start Campaign</span>
                          </button>
                        )}
                        
                        {campaign.status === 'running' && (
                          <>
                            <button
                              onClick={() => pauseCampaign(campaign.id)}
                              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 transition-colors flex items-center space-x-2"
                            >
                              <Pause size={16} />
                              <span>Pause</span>
                            </button>
                            <button
                              onClick={() => stopCampaign(campaign.id)}
                              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 transition-colors flex items-center space-x-2"
                            >
                              <Square size={16} />
                              <span>Stop</span>
                            </button>
                          </>
                        )}
                        
                        {campaign.status === 'paused' && (
                          <button
                            onClick={() => startCampaign(campaign)}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 transition-colors flex items-center space-x-2"
                          >
                            <Play size={16} />
                            <span>Resume</span>
                          </button>
                        )}
                        
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 transition-colors flex items-center space-x-2">
                          <BarChart3 size={16} />
                          <span>View Report</span>
                        </button>
                        
                        <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 transition-colors flex items-center space-x-2">
                          <Download size={16} />
                          <span>Export</span>
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          ) : (
            /* Campaign Creation Form - This would be implemented in the next part */
            <div className="text-center py-12">
              <p className="text-gray-400">Campaign creation form would go here...</p>
              <button
                onClick={() => setIsCreatingCampaign(false)}
                className="mt-4 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 transition-colors"
              >
                Back to Campaigns
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
