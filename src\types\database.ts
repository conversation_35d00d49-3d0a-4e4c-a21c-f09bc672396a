// Database schema types for PressureMax Facebook Ad Template System

export interface ServiceType {
  id: string;
  name: string; // "Residential", "Commercial", "Specialty"
  icon: string;
  color: string;
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

export interface PressureService {
  id: string;
  service_type_id: string;
  name: string; // "House Washing", "Driveway Cleaning", "Deck Restoration"
  description: string;
  typical_pricing: string; // "$200-400", "$0.15/sqft"
  season_preference: string; // "Spring/Summer", "Year-round"
  equipment_needed: string[]; // ["Surface Cleaner", "Hot Water"]
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

export interface AdTargeting {
  location_radius: string; // "15_miles"
  age_range: {
    min: number;
    max: number;
  };
  home_value_range: string; // "$200k_plus"
  interests: string[]; // ["home_improvement", "property_maintenance"]
  exclude_competitors: boolean;
  custom_audiences: string[];
  lookalike_audiences: string[];
}

export interface AdCreative {
  primary_text: string;
  headline: string;
  description: string;
  call_to_action: string;
  media_requirements: {
    before_after_photos: boolean;
    action_video: boolean;
    equipment_shots: boolean;
    image_specs: {
      width: number;
      height: number;
      format: string;
    };
  };
  image_path?: string;
}

export interface AdTemplate {
  id: string;
  name: string;
  service_type_id: string;
  pressure_service_id: string;
  category: string; // "Residential", "Commercial", "Specialty"
  service: string; // "House Washing", "Driveway Cleaning", etc.
  
  // Ad Content
  creative: AdCreative;
  targeting: AdTargeting;
  
  // Campaign Settings
  budget_range: {
    min: number;
    max: number;
    suggested: number;
  };
  
  // Template Metadata
  seasonal_timing: string[]; // ["spring", "summer", "fall", "winter"]
  target_customer: string; // "homeowner", "property_manager", "business_owner"
  pricing_strategy: string; // "discount", "premium", "competitive"
  
  // Performance Data
  performance: {
    ctr: string; // "3.2%"
    cpl: string; // "$12"
    conversions: number;
    total_spend: number;
    total_leads: number;
    last_updated: Date;
  };
  
  // Template Type & Access Control
  template_type: 'global' | 'custom'; // global = admin-created, custom = user-created
  is_public: boolean; // global templates are public, custom can be private
  parent_template_id?: string; // if customized from a global template

  // Status
  status: 'draft' | 'active' | 'paused' | 'archived';
  is_featured: boolean;

  // Timestamps
  created_at: Date;
  updated_at: Date;
  created_by: string; // user_id
}

export interface Campaign {
  id: string;
  template_id: string;
  name: string;
  facebook_campaign_id?: string;
  
  // Campaign Settings
  budget: number;
  start_date: Date;
  end_date?: Date;
  
  // Customized Content (overrides template)
  custom_creative?: Partial<AdCreative>;
  custom_targeting?: Partial<AdTargeting>;
  
  // Performance Tracking
  metrics: {
    impressions: number;
    clicks: number;
    ctr: number;
    cpc: number;
    cpl: number;
    leads_generated: number;
    spend: number;
    last_sync: Date;
  };
  
  // Status
  status: 'draft' | 'active' | 'paused' | 'completed' | 'error';
  
  // Timestamps
  created_at: Date;
  updated_at: Date;
  launched_at?: Date;
}

export interface Lead {
  id: string;
  campaign_id: string;
  source: 'facebook' | 'google' | 'website' | 'referral';
  
  // Lead Information
  name: string;
  phone: string;
  email?: string;
  address?: string;
  
  // Service Details
  service_interest: string;
  budget_range?: string;
  urgency: 'asap' | 'this_week' | 'this_month' | 'next_month' | 'just_browsing';
  property_type: 'residential' | 'commercial';
  
  // Lead Scoring
  score: number; // 0-100
  quality: 'hot' | 'warm' | 'cold';
  
  // VAPI Integration
  vapi_call_id?: string;
  call_status: 'pending' | 'calling' | 'completed' | 'failed' | 'no_answer';
  call_attempts: number;
  last_call_at?: Date;
  next_call_at?: Date;
  
  // Appointment
  appointment_scheduled: boolean;
  appointment_date?: Date;
  appointment_notes?: string;
  
  // Status
  status: 'new' | 'contacted' | 'qualified' | 'appointment' | 'converted' | 'lost';
  
  // Timestamps
  created_at: Date;
  updated_at: Date;
  last_contacted_at?: Date;
  
  // Notes
  notes: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  company_name: string;
  phone?: string;

  // Role-based access control
  role: 'admin' | 'user';
  permissions: string[]; // ['template_create', 'template_edit', 'template_delete', 'user_manage']

  // Subscription
  plan: 'starter' | 'growth' | 'scale';
  subscription_status: 'active' | 'cancelled' | 'past_due';
  
  // Facebook Integration
  facebook_access_token?: string;
  facebook_ad_account_id?: string;
  facebook_page_id?: string;
  
  // VAPI Integration
  vapi_api_key?: string;
  vapi_assistant_id?: string;
  
  // Settings
  timezone: string;
  business_hours: {
    start: string; // "09:00"
    end: string; // "17:00"
    days: string[]; // ["monday", "tuesday", ...]
  };
  
  // Timestamps
  created_at: Date;
  updated_at: Date;
  last_login_at?: Date;
}

// Database relationships
export interface ServiceTypeWithServices extends ServiceType {
  services: PressureService[];
}

export interface AdTemplateWithRelations extends AdTemplate {
  service_type: ServiceType;
  pressure_service: PressureService;
  campaigns: Campaign[];
}

export interface CampaignWithTemplate extends Campaign {
  template: AdTemplate;
  leads: Lead[];
}

export interface LeadWithCampaign extends Lead {
  campaign: CampaignWithTemplate;
}
