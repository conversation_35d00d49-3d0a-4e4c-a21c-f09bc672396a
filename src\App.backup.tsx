import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LoginModal } from './components/LoginModal';
import { SignupModal } from './components/SignupModal';
import { PasswordResetModal } from './components/PasswordResetModal';
import { ProtectedRoute } from './components/ProtectedRoute';
import { Dashboard } from './components/Dashboard';
import { LandingPage } from './components/LandingPage';

function App() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentView, setCurrentView] = useState('dashboard');
  const [adTemplates, setAdTemplates] = useState<AdTemplate[]>([]);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<AdTemplate | null>(null);
  const [isDeployModalOpen, setIsDeployModalOpen] = useState(false);
  const [deployingTemplate, setDeployingTemplate] = useState<AdTemplate | null>(null);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [isLeadDetailsOpen, setIsLeadDetailsOpen] = useState(false);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [analyticsSummary, setAnalyticsSummary] = useState<AnalyticsSummary | null>(null);
  const [leadQualityMetrics, setLeadQualityMetrics] = useState<LeadQualityMetrics | null>(null);
  const [performanceByService, setPerformanceByService] = useState<PerformanceByService[]>([]);
  const [isOnboardingOpen, setIsOnboardingOpen] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const [showLandingPage, setShowLandingPage] = useState(true);
  const [isLaunchingCampaign, setIsLaunchingCampaign] = useState(false);
  const [isBulkCalling, setIsBulkCalling] = useState(false);
  const [bulkCallProgress, setBulkCallProgress] = useState({ current: 0, total: 0 });

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 500);

    // Check onboarding completion and landing page preference
    const onboardingCompleted = localStorage.getItem('pressuremax_onboarding_completed');
    const hasSeenLanding = localStorage.getItem('pressuremax_seen_landing');
    setHasCompletedOnboarding(onboardingCompleted === 'true');
    setShowLandingPage(hasSeenLanding !== 'true');

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Load data from database
    const loadData = async () => {
      try {
        const [templates, leadsData] = await Promise.all([
          db.getAdTemplates(),
          db.getLeads()
        ]);
        setAdTemplates(templates);
        setLeads(leadsData);

        // Load analytics data
        loadAnalyticsData();
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      const [summary, qualityMetrics, servicePerformance] = await Promise.all([
        analyticsService.getAnalyticsSummary(),
        analyticsService.getLeadQualityMetrics(),
        analyticsService.getPerformanceByService()
      ]);
      setAnalyticsSummary(summary);
      setLeadQualityMetrics(qualityMetrics);
      setPerformanceByService(servicePerformance);
    } catch (error) {
      console.error('Error loading analytics:', error);
    }
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setIsTemplateModalOpen(true);
  };

  const handleEditTemplate = (template: AdTemplate) => {
    setEditingTemplate(template);
    setIsTemplateModalOpen(true);
  };

  const handleSaveTemplate = async () => {
    // Refresh the templates list
    const templates = await db.getAdTemplates();
    setAdTemplates(templates);
  };

  const handleCloseModal = () => {
    setIsTemplateModalOpen(false);
    setEditingTemplate(null);
  };

  const handleDeployTemplate = (template: AdTemplate) => {
    setDeployingTemplate(template);
    setIsDeployModalOpen(true);
  };

  const handleDeploySuccess = (campaignId: string) => {
    console.log('Campaign deployed successfully:', campaignId);
    // Optionally refresh campaigns list or show success message
  };

  const handleCloseDeployModal = () => {
    setIsDeployModalOpen(false);
    setDeployingTemplate(null);
  };

  const handleViewLead = (lead: Lead) => {
    setSelectedLead(lead);
    setIsLeadDetailsOpen(true);
  };

  const handleLeadUpdate = (updatedLead: Lead) => {
    setLeads(prev => prev.map(lead =>
      lead.id === updatedLead.id ? updatedLead : lead
    ));
    setSelectedLead(updatedLead);
  };

  const handleCloseLeadDetails = () => {
    setIsLeadDetailsOpen(false);
    setSelectedLead(null);
  };

  const handleEnterDashboard = () => {
    setShowLandingPage(false);
    localStorage.setItem('pressuremax_seen_landing', 'true');
  };

  const handleLaunchFacebookCampaign = async (template: AdTemplate) => {
    if (isLaunchingCampaign) return; // Prevent multiple launches

    setIsLaunchingCampaign(true);
    try {
      // Check if Facebook API is available
      const isHealthy = await facebookApi.healthCheck();
      if (!isHealthy) {
        alert('Facebook API is not available. Please start the backend server first:\n\ncd backend\npython app.py');
        return;
      }

      // Get user's ad accounts
      const accounts = await facebookApi.getAdAccounts();
      if (accounts.length === 0) {
        alert('No Facebook ad accounts found. Please ensure you have access to a Facebook ad account.');
        return;
      }

      // Use the first account (in a real app, let user choose)
      const account = accounts[0];

      // Create basic targeting (you can enhance this with a form)
      const targeting = FacebookTargeting.createHomeownerTargeting(
        FacebookTargeting.createLocationTargeting(40.7128, -74.0060, 25) // NYC example - customize for user's location
      );

      // Launch campaign with default budget
      const campaignData = {
        account_id: account.account_id,
        template: template,
        targeting: targeting,
        budget: {
          daily_budget: 50 // $50/day default - can be customized
        },
        page_id: '***************', // Your Facebook page ID
        landing_page_url: 'https://pressuremax.ai'
      };

      const result = await facebookApi.launchCampaignFromTemplate(campaignData);

      if (result.success) {
        alert(`Campaign "${template.name}" created successfully on Facebook! Campaign ID: ${result.campaign.id}`);
      } else {
        alert('Failed to create campaign. Please try again.');
      }

    } catch (error) {
      console.error('Error launching Facebook campaign:', error);
      alert(`Error launching campaign: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLaunchingCampaign(false);
    }
  };

  const handleBulkReactivationCalls = async () => {
    if (isBulkCalling) return;

    // Filter leads for reactivation (contacted but no appointment, or old leads)
    const reactivationLeads = leads.filter(lead =>
      (lead.status === 'contacted' || lead.status === 'qualified') &&
      !lead.appointment_scheduled &&
      lead.call_attempts > 0 &&
      lead.call_status !== 'calling'
    );

    if (reactivationLeads.length === 0) {
      alert('No leads available for reactivation campaign. All leads are either new (auto-called) or already have appointments.');
      return;
    }

    const confirmed = confirm(
      `Start REACTIVATION calling campaign for ${reactivationLeads.length} leads?\n\n` +
      `This will call leads who were previously contacted but didn't book appointments. ` +
      `New leads are automatically called when they come in - this is for follow-up calls. ` +
      `Calls will be spaced 3 seconds apart.`
    );

    if (!confirmed) return;

    setIsBulkCalling(true);
    setBulkCallProgress({ current: 0, total: reactivationLeads.length });

    try {
      // Create appointment booking assistant
      const assistant = await vapiService.createAppointmentBookingAssistant();

      // Run reactivation call campaign
      const calls = await vapiService.runBulkCallCampaign(reactivationLeads, assistant.id);

      // Update all leads with reactivation status
      for (let i = 0; i < reactivationLeads.length; i++) {
        const lead = reactivationLeads[i];
        setBulkCallProgress({ current: i + 1, total: reactivationLeads.length });

        await db.updateLead(lead.id, {
          call_status: 'calling',
          call_attempts: lead.call_attempts + 1,
          last_call_at: new Date(),
          notes: `${lead.notes || ''}\n\n🔄 Reactivation VAPI call initiated at ${new Date().toLocaleString()}`
        });
      }

      // Refresh leads data
      const updatedLeads = await db.getLeads();
      setLeads(updatedLeads);

      alert(
        `Reactivation calling campaign completed!\n\n` +
        `Successfully initiated: ${calls.length}/${reactivationLeads.length} calls\n` +
        `Failed: ${reactivationLeads.length - calls.length} calls\n\n` +
        `These are follow-up calls for leads who were previously contacted. ` +
        `New leads are automatically called when they come in.`
      );

    } catch (error) {
      console.error('Error in bulk calling campaign:', error);
      alert(`Error starting bulk calling campaign: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsBulkCalling(false);
      setBulkCallProgress({ current: 0, total: 0 });
    }
  };

  // Test function to create a new lead and trigger automatic calling
  const handleCreateTestLead = async () => {
    const testLead: Omit<Lead, 'id' | 'created_at' | 'updated_at'> = {
      campaign_id: 'test-campaign',
      source: 'website',
      name: `Test Lead ${Date.now()}`,
      phone: `555-${Math.floor(Math.random() * 9000) + 1000}`,
      email: `test${Date.now()}@example.com`,
      service_interest: 'house washing',
      urgency: 'this_week',
      property_type: 'residential',
      score: 85,
      quality: 'warm',
      call_status: 'pending',
      call_attempts: 0,
      appointment_scheduled: false,
      status: 'new',
      notes: 'Test lead created to demonstrate automatic VAPI calling'
    };

    try {
      console.log('🧪 Creating test lead to demonstrate automatic calling...');
      const newLead = await db.createLead(testLead);

      // Refresh leads data
      const updatedLeads = await db.getLeads();
      setLeads(updatedLeads);

      alert(`Test lead "${newLead.name}" created!\n\nThe automatic VAPI calling system should trigger immediately. Check the console for logs and the leads list for status updates.`);
    } catch (error) {
      console.error('Error creating test lead:', error);
      alert('Error creating test lead. Check console for details.');
    }
  };

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Layout },
    { id: 'templates', label: 'Ad Templates', icon: Target },
    { id: 'leads', label: 'Leads', icon: MessageSquare },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  // Show landing page if user hasn't seen it yet
  if (showLandingPage) {
    return <LandingPage onEnterDashboard={handleEnterDashboard} />;
  }

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden relative">
      {/* Background Pattern */}
      <div className="fixed inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-cyan-500/5" />
        <div className="circuit-pattern" />
      </div>

      {/* Main Content */}
      <div className={`transition-opacity duration-1000 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>

        {/* Header */}
        <header className="fixed w-full z-50 bg-black/80 backdrop-blur-sm border-b border-cyan-500/20">
          <div className="w-full px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center relative">
                  <Zap className="text-black" size={24} />
                </div>
                <span className="text-2xl font-bold font-orbitron tracking-wider text-cyan-400">
                  PressureMax
                </span>
              </div>

              <nav className="hidden md:flex items-center space-x-8">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  return (
                    <button
                      key={item.id}
                      onClick={() => setCurrentView(item.id)}
                      className={`flex items-center space-x-2 px-4 py-2 transition-colors ${
                        currentView === item.id
                          ? 'text-cyan-400 bg-cyan-500/10 border border-cyan-500/30'
                          : 'text-gray-300 hover:text-cyan-400'
                      }`}
                    >
                      <Icon size={18} />
                      <span>{item.label}</span>
                    </button>
                  );
                })}

                {/* Help and Onboarding */}
                <div className="flex items-center space-x-4 ml-8 pl-8 border-l border-gray-700">
                  {!hasCompletedOnboarding && (
                    <button
                      onClick={() => setIsOnboardingOpen(true)}
                      className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-2 text-sm font-bold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
                    >
                      Quick Start
                    </button>
                  )}
                  <button
                    onClick={() => setCurrentView('help')}
                    className={`flex items-center space-x-2 px-4 py-2 transition-colors ${
                      currentView === 'help'
                        ? 'text-cyan-400 bg-cyan-500/10 border border-cyan-500/30'
                        : 'text-gray-300 hover:text-cyan-400'
                    }`}
                    title="Help Center"
                  >
                    <MessageSquare size={18} />
                    <span>Help</span>
                  </button>
                  <button
                    onClick={() => {
                      setShowLandingPage(true);
                      localStorage.removeItem('pressuremax_seen_landing');
                    }}
                    className="text-gray-300 hover:text-cyan-400 px-4 py-2 transition-colors"
                    title="Back to Landing Page"
                  >
                    ← Landing
                  </button>
                </div>
              </nav>
            </div>
          </div>
        </header>

        {/* Main Dashboard Content */}
        <main className="pt-24 min-h-screen">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

            {/* Dashboard View */}
            {currentView === 'dashboard' && (
              <div className="space-y-8">
                <div className="flex items-center justify-between">
                  <h1 className="text-3xl font-bold font-orbitron text-white">Dashboard</h1>
                  <button className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-3 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2">
                    <Plus size={20} />
                    <span>Create New Campaign</span>
                  </button>
                </div>

                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Active Campaigns</p>
                        <p className="text-2xl font-bold text-cyan-400">8</p>
                      </div>
                      <Target className="text-cyan-400" size={24} />
                    </div>
                  </div>
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Leads Today</p>
                        <p className="text-2xl font-bold text-green-400">24</p>
                      </div>
                      <MessageSquare className="text-green-400" size={24} />
                    </div>
                  </div>
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Calls Made</p>
                        <p className="text-2xl font-bold text-yellow-400">18</p>
                      </div>
                      <Phone className="text-yellow-400" size={24} />
                    </div>
                  </div>
                  <div className="bg-[#414244] border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Conversion Rate</p>
                        <p className="text-2xl font-bold text-cyan-400">32%</p>
                      </div>
                      <BarChart3 className="text-cyan-400" size={24} />
                    </div>
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="bg-black border border-cyan-500/30 p-6">
                  <h2 className="text-xl font-bold text-white mb-4">Recent Activity</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between py-3 border-b border-gray-800">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span className="text-gray-300">New lead from "House Washing Spring Special"</span>
                      </div>
                      <span className="text-gray-500 text-sm">2 min ago</span>
                    </div>
                    <div className="flex items-center justify-between py-3 border-b border-gray-800">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                        <span className="text-gray-300">AI called lead - Appointment scheduled</span>
                      </div>
                      <span className="text-gray-500 text-sm">5 min ago</span>
                    </div>
                    <div className="flex items-center justify-between py-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                        <span className="text-gray-300">Campaign "Driveway Transformation" paused</span>
                      </div>
                      <span className="text-gray-500 text-sm">1 hour ago</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Templates View */}
            {currentView === 'templates' && (
              <div className="space-y-8">
                <div className="flex items-center justify-between">
                  <h1 className="text-3xl font-bold font-orbitron text-white">Ad Templates</h1>
                  <button
                    onClick={handleCreateTemplate}
                    className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-3 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
                  >
                    <Plus size={20} />
                    <span>Create Template</span>
                  </button>
                </div>

                {/* Template Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {adTemplates.map((template) => (
                    <div key={template.id} className="bg-black border border-cyan-500/30 p-6 hover:border-cyan-500 transition-all duration-300">
                      <div className="flex items-center justify-between mb-4">
                        <span className={`px-3 py-1 text-xs font-bold ${
                          template.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {template.status.toUpperCase()}
                        </span>
                        <div className="flex space-x-2">
                          <button
                            className="text-gray-400 hover:text-cyan-400"
                            title="Preview Template"
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            onClick={() => handleEditTemplate(template)}
                            className="text-gray-400 hover:text-cyan-400"
                            title="Edit Template"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            className="text-gray-400 hover:text-cyan-400"
                            title="Duplicate Template"
                          >
                            <Copy size={16} />
                          </button>
                        </div>
                      </div>

                      {/* Template Image */}
                      {template.creative.image_path && (
                        <div className="mb-4 relative overflow-hidden rounded border border-cyan-500/20">
                          <img
                            src={template.creative.image_path}
                            alt={template.name}
                            className="w-full h-32 object-cover hover:scale-105 transition-transform duration-300"
                            onError={(e) => {
                              // Hide image if it fails to load
                              e.currentTarget.style.display = 'none';
                            }}
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                        </div>
                      )}

                      <h3 className="text-lg font-bold text-white mb-2">{template.name}</h3>
                      <p className="text-gray-400 text-sm mb-3">{template.category} • {template.service}</p>
                      <p className="text-gray-300 text-sm mb-4">{template.creative.headline}</p>

                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-400">CTR:</span>
                          <span className="text-cyan-400">{template.performance.ctr}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Cost/Lead:</span>
                          <span className="text-green-400">{template.performance.cpl}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Conversions:</span>
                          <span className="text-white">{template.performance.conversions}</span>
                        </div>
                      </div>

                      <div className="flex space-x-2 mt-4">
                        <button
                          onClick={() => handleLaunchFacebookCampaign(template)}
                          disabled={isLaunchingCampaign}
                          className={`flex-1 py-2 transition-all duration-300 flex items-center justify-center space-x-2 font-bold ${
                            isLaunchingCampaign
                              ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                              : 'bg-cyan-500 text-black hover:bg-cyan-400'
                          }`}
                        >
                          {isLaunchingCampaign ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-transparent"></div>
                              <span>Launching...</span>
                            </>
                          ) : (
                            <>
                              <Play size={16} />
                              <span>Launch</span>
                            </>
                          )}
                        </button>
                        <button
                          onClick={() => handleDeployTemplate(template)}
                          className="flex-1 bg-cyan-500/10 border border-cyan-500/30 text-cyan-400 py-2 hover:bg-cyan-500 hover:text-black transition-all duration-300 flex items-center justify-center space-x-2"
                        >
                          <span>Preview</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Leads View */}
            {currentView === 'leads' && (
              <div className="space-y-8">
                <div className="flex items-center justify-between">
                  <h1 className="text-3xl font-bold font-orbitron text-white">Leads</h1>
                  <div className="flex items-center space-x-4">
                    {/* New Lead Auto-Call Status */}
                    <div className="flex items-center space-x-2 px-4 py-2 bg-green-900/30 border border-green-500/30 rounded">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400 text-sm font-medium">Auto-Call: ON</span>
                      <span className="text-gray-400 text-xs">New leads called automatically</span>
                    </div>

                    {/* Reactivation Call Button */}
                    <button
                      onClick={handleBulkReactivationCalls}
                      disabled={isBulkCalling}
                      className={`flex items-center space-x-2 px-6 py-3 font-bold transition-all duration-300 ${
                        isBulkCalling
                          ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                          : 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:shadow-lg hover:shadow-orange-500/25'
                      }`}
                      title="Reactivation campaign for previously contacted leads"
                    >
                      {isBulkCalling ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-transparent"></div>
                          <span>Calling... ({bulkCallProgress.current}/{bulkCallProgress.total})</span>
                        </>
                      ) : (
                        <>
                          <Phone size={20} />
                          <span>Reactivation Campaign</span>
                        </>
                      )}
                    </button>

                    {/* Test New Lead Button */}
                    <button
                      onClick={handleCreateTestLead}
                      className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-2 font-bold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 flex items-center space-x-2"
                      title="Create a test lead to demonstrate automatic calling"
                    >
                      <Plus size={16} />
                      <span>Test Auto-Call</span>
                    </button>

                    {/* Filter Dropdown */}
                    <select className="bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none">
                      <option value="all">All Leads</option>
                      <option value="new">New</option>
                      <option value="contacted">Contacted</option>
                      <option value="qualified">Qualified</option>
                      <option value="appointment">Appointment</option>
                    </select>
                  </div>
                </div>

                {/* Leads Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Total Leads</p>
                        <p className="text-2xl font-bold text-white">{leads.length}</p>
                      </div>
                      <MessageSquare className="text-cyan-400" size={24} />
                    </div>
                  </div>
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Hot Leads</p>
                        <p className="text-2xl font-bold text-red-400">{leads.filter(l => l.quality === 'hot').length}</p>
                      </div>
                      <Phone className="text-red-400" size={24} />
                    </div>
                  </div>
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Appointments</p>
                        <p className="text-2xl font-bold text-green-400">{leads.filter(l => l.appointment_scheduled).length}</p>
                      </div>
                      <BarChart3 className="text-green-400" size={24} />
                    </div>
                  </div>
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">Conversion Rate</p>
                        <p className="text-2xl font-bold text-cyan-400">
                          {leads.length > 0 ? Math.round((leads.filter(l => l.status === 'converted').length / leads.length) * 100) : 0}%
                        </p>
                      </div>
                      <Target className="text-cyan-400" size={24} />
                    </div>
                  </div>
                </div>

                {/* Leads Table */}
                <div className="bg-black border border-cyan-500/30">
                  <div className="p-6">
                    <h2 className="text-xl font-bold text-white mb-4">Recent Leads</h2>
                    {leads.length === 0 ? (
                      <div className="text-center py-8">
                        <MessageSquare className="text-gray-600 mx-auto mb-4" size={48} />
                        <p className="text-gray-400">No leads yet. Deploy some campaigns to start generating leads!</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b border-gray-700">
                              <th className="text-left py-3 text-gray-400">Name</th>
                              <th className="text-left py-3 text-gray-400">Phone</th>
                              <th className="text-left py-3 text-gray-400">Service</th>
                              <th className="text-left py-3 text-gray-400">Quality</th>
                              <th className="text-left py-3 text-gray-400">Status</th>
                              <th className="text-left py-3 text-gray-400">Created</th>
                              <th className="text-left py-3 text-gray-400">Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {leads.slice(0, 10).map((lead) => (
                              <tr key={lead.id} className="border-b border-gray-800 hover:bg-gray-900/50">
                                <td className="py-3 text-white">{lead.name}</td>
                                <td className="py-3 text-gray-300">{lead.phone}</td>
                                <td className="py-3 text-gray-300">{lead.service_interest || 'Not specified'}</td>
                                <td className="py-3">
                                  <span className={`px-2 py-1 text-xs font-bold ${
                                    lead.quality === 'hot' ? 'bg-red-500/20 text-red-400' :
                                    lead.quality === 'warm' ? 'bg-yellow-500/20 text-yellow-400' :
                                    'bg-blue-500/20 text-blue-400'
                                  }`}>
                                    {lead.quality.toUpperCase()}
                                  </span>
                                </td>
                                <td className="py-3">
                                  <span className={`px-2 py-1 text-xs font-bold ${
                                    lead.status === 'new' ? 'bg-cyan-500/20 text-cyan-400' :
                                    lead.status === 'contacted' ? 'bg-yellow-500/20 text-yellow-400' :
                                    lead.status === 'qualified' ? 'bg-green-500/20 text-green-400' :
                                    lead.status === 'converted' ? 'bg-purple-500/20 text-purple-400' :
                                    'bg-gray-500/20 text-gray-400'
                                  }`}>
                                    {lead.status.toUpperCase()}
                                  </span>
                                </td>
                                <td className="py-3 text-gray-400">
                                  {new Date(lead.created_at).toLocaleDateString()}
                                </td>
                                <td className="py-3">
                                  <div className="flex space-x-2">
                                    <button
                                      onClick={() => handleViewLead(lead)}
                                      className="text-cyan-400 hover:text-cyan-300"
                                      title="Call Lead"
                                    >
                                      <Phone size={16} />
                                    </button>
                                    <button
                                      onClick={() => handleViewLead(lead)}
                                      className="text-gray-400 hover:text-white"
                                      title="View Details"
                                    >
                                      <Eye size={16} />
                                    </button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>

                {/* Lead Capture Form Demo */}
                <div className="bg-black border border-cyan-500/30 p-6">
                  <h2 className="text-xl font-bold text-white mb-4">Lead Capture Form Preview</h2>
                  <p className="text-gray-400 mb-6">This is how your lead capture form will appear on your website:</p>
                  <LeadCaptureForm
                    campaignId="demo-campaign"
                    source="website"
                    onSuccess={(leadId) => {
                      console.log('Demo lead created:', leadId);
                      // Refresh leads
                      db.getLeads().then(setLeads);
                    }}
                    className="max-w-2xl"
                  />
                </div>
              </div>
            )}

            {/* Analytics View */}
            {currentView === 'analytics' && (
              <div className="space-y-8">
                <div className="flex items-center justify-between">
                  <h1 className="text-3xl font-bold font-orbitron text-white">Analytics</h1>
                  <button
                    onClick={() => loadAnalyticsData()}
                    className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-3 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
                  >
                    Refresh Data
                  </button>
                </div>

                {/* Analytics Summary */}
                {analyticsSummary && (
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-black border border-cyan-500/30 p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-400 text-sm">Total Campaigns</p>
                          <p className="text-2xl font-bold text-white">{analyticsSummary.totalCampaigns}</p>
                          <p className="text-xs text-gray-500">{analyticsSummary.activeCampaigns} active</p>
                        </div>
                        <Target className="text-cyan-400" size={24} />
                      </div>
                    </div>
                    <div className="bg-black border border-cyan-500/30 p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-400 text-sm">Total Spend</p>
                          <p className="text-2xl font-bold text-white">${analyticsSummary.totalSpend.toFixed(0)}</p>
                          <p className="text-xs text-gray-500">Avg CPL: ${analyticsSummary.avgCPL.toFixed(2)}</p>
                        </div>
                        <BarChart3 className="text-green-400" size={24} />
                      </div>
                    </div>
                    <div className="bg-black border border-cyan-500/30 p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-400 text-sm">Total Leads</p>
                          <p className="text-2xl font-bold text-white">{analyticsSummary.totalLeads}</p>
                          <p className="text-xs text-gray-500">{analyticsSummary.totalAppointments} appointments</p>
                        </div>
                        <MessageSquare className="text-cyan-400" size={24} />
                      </div>
                    </div>
                    <div className="bg-black border border-cyan-500/30 p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-400 text-sm">ROI</p>
                          <p className="text-2xl font-bold text-green-400">{analyticsSummary.overallROI.toFixed(1)}%</p>
                          <p className="text-xs text-gray-500">Est. Revenue: ${analyticsSummary.estimatedRevenue.toFixed(0)}</p>
                        </div>
                        <BarChart3 className="text-green-400" size={24} />
                      </div>
                    </div>
                  </div>
                )}

                {/* Lead Quality Breakdown */}
                {leadQualityMetrics && (
                  <div className="bg-black border border-cyan-500/30 p-6">
                    <h2 className="text-xl font-bold text-white mb-4">Lead Quality Analysis</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-red-400 mb-2">{leadQualityMetrics.hotLeads}</div>
                        <div className="text-sm text-gray-400">Hot Leads</div>
                        <div className="text-xs text-gray-500">
                          {leadQualityMetrics.conversionByQuality.hot.rate.toFixed(1)}% conversion
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-yellow-400 mb-2">{leadQualityMetrics.warmLeads}</div>
                        <div className="text-sm text-gray-400">Warm Leads</div>
                        <div className="text-xs text-gray-500">
                          {leadQualityMetrics.conversionByQuality.warm.rate.toFixed(1)}% conversion
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-400 mb-2">{leadQualityMetrics.coldLeads}</div>
                        <div className="text-sm text-gray-400">Cold Leads</div>
                        <div className="text-xs text-gray-500">
                          {leadQualityMetrics.conversionByQuality.cold.rate.toFixed(1)}% conversion
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 text-center">
                      <div className="text-lg font-semibold text-white">
                        Average Lead Score: {leadQualityMetrics.avgScore.toFixed(1)}/100
                      </div>
                    </div>
                  </div>
                )}

                {/* Performance by Service */}
                <div className="bg-black border border-cyan-500/30 p-6">
                  <h2 className="text-xl font-bold text-white mb-4">Performance by Service Type</h2>
                  {performanceByService.length === 0 ? (
                    <div className="text-center py-8 text-gray-400">
                      <BarChart3 className="mx-auto mb-2" size={32} />
                      <p>No service performance data available yet</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b border-gray-700">
                            <th className="text-left py-3 text-gray-400">Service Type</th>
                            <th className="text-left py-3 text-gray-400">Campaigns</th>
                            <th className="text-left py-3 text-gray-400">Spend</th>
                            <th className="text-left py-3 text-gray-400">Leads</th>
                            <th className="text-left py-3 text-gray-400">CPL</th>
                            <th className="text-left py-3 text-gray-400">Conv. Rate</th>
                            <th className="text-left py-3 text-gray-400">ROI</th>
                          </tr>
                        </thead>
                        <tbody>
                          {performanceByService.map((service) => (
                            <tr key={service.serviceType} className="border-b border-gray-800 hover:bg-gray-900/50">
                              <td className="py-3 text-white font-medium">{service.serviceType}</td>
                              <td className="py-3 text-gray-300">{service.campaigns}</td>
                              <td className="py-3 text-gray-300">${service.spend.toFixed(0)}</td>
                              <td className="py-3 text-gray-300">{service.leads}</td>
                              <td className="py-3 text-gray-300">${service.cpl.toFixed(2)}</td>
                              <td className="py-3 text-gray-300">{service.conversionRate.toFixed(1)}%</td>
                              <td className="py-3">
                                <span className={`font-semibold ${
                                  service.roi > 0 ? 'text-green-400' : 'text-red-400'
                                }`}>
                                  {service.roi.toFixed(1)}%
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>

                {/* A/B Testing Section */}
                <ABTestManager
                  templates={adTemplates}
                  onTestCreated={(test) => {
                    console.log('A/B test created:', test);
                  }}
                />
              </div>
            )}

            {/* Integrations View */}
            {currentView === 'integrations' && (
              <IntegrationsHub
                leads={leads}
                onLeadSync={(leadId, integrationId) => {
                  console.log(`Lead ${leadId} synced to integration ${integrationId}`);
                }}
              />
            )}

            {/* Help Center View */}
            {currentView === 'help' && (
              <HelpCenter />
            )}

            {/* Other Views Placeholder */}
            {currentView !== 'dashboard' && currentView !== 'templates' && currentView !== 'leads' && currentView !== 'analytics' && currentView !== 'integrations' && currentView !== 'help' && (
              <div className="text-center py-20">
                <h1 className="text-3xl font-bold font-orbitron text-white mb-4">
                  {navigationItems.find(item => item.id === currentView)?.label}
                </h1>
                <p className="text-gray-400">This section is coming soon...</p>
              </div>
            )}

          </div>
        </main>
        {/* Template Modal */}
        <TemplateModal
          isOpen={isTemplateModalOpen}
          onClose={handleCloseModal}
          template={editingTemplate}
          onSave={handleSaveTemplate}
        />

        {/* Campaign Deploy Modal */}
        <CampaignDeployModal
          isOpen={isDeployModalOpen}
          onClose={handleCloseDeployModal}
          template={deployingTemplate}
          onSuccess={handleDeploySuccess}
        />

        {/* Lead Details Modal */}
        <LeadDetailsModal
          isOpen={isLeadDetailsOpen}
          onClose={handleCloseLeadDetails}
          lead={selectedLead}
          onLeadUpdate={handleLeadUpdate}
        />

        {/* Onboarding Flow */}
        <OnboardingFlow
          isOpen={isOnboardingOpen}
          onClose={() => setIsOnboardingOpen(false)}
          onComplete={() => {
            setIsOnboardingOpen(false);
            setHasCompletedOnboarding(true);
            // Store completion in localStorage
            localStorage.setItem('pressuremax_onboarding_completed', 'true');
          }}
        />






      </div>
    </div>
  );
}

export default App;