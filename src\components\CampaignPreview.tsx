/**
 * Campaign Preview Component for PressureMax
 * Shows real-time preview of how ads will appear on different platforms
 */

import React, { useState } from 'react';
import { Monitor, Smartphone, Instagram, Facebook, Eye, ExternalLink } from 'lucide-react';
import { AdTemplate } from '../types/database';
import { useAuth } from '../contexts/AuthContext';

interface CampaignPreviewProps {
  template: AdTemplate;
  customData: {
    headline: string;
    description: string;
    cta: string;
    websiteUrl: string;
    campaignName: string;
  };
  className?: string;
}

type PreviewPlatform = 'facebook-feed' | 'facebook-mobile' | 'instagram-feed' | 'instagram-stories';

export const CampaignPreview: React.FC<CampaignPreviewProps> = ({
  template,
  customData,
  className = ''
}) => {
  const { user } = useAuth();
  const [selectedPlatform, setSelectedPlatform] = useState<PreviewPlatform>('facebook-feed');

  const platforms = [
    {
      id: 'facebook-feed' as PreviewPlatform,
      name: 'Facebook Feed',
      icon: Facebook,
      description: 'Desktop News Feed'
    },
    {
      id: 'facebook-mobile' as PreviewPlatform,
      name: 'Facebook Mobile',
      icon: Smartphone,
      description: 'Mobile News Feed'
    },
    {
      id: 'instagram-feed' as PreviewPlatform,
      name: 'Instagram Feed',
      icon: Instagram,
      description: 'Instagram Posts'
    },
    {
      id: 'instagram-stories' as PreviewPlatform,
      name: 'Instagram Stories',
      icon: Monitor,
      description: 'Instagram Stories'
    }
  ];

  const renderFacebookFeedPreview = () => (
    <div className="bg-white text-black max-w-md mx-auto shadow-lg">
      {/* Header */}
      <div className="flex items-center space-x-3 p-4 border-b">
        <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
          {user?.company_name?.charAt(0) || 'B'}
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <p className="font-semibold text-sm">{user?.company_name || 'Your Business'}</p>
            <span className="text-blue-500 text-xs">✓</span>
          </div>
          <div className="flex items-center space-x-2 text-xs text-gray-500">
            <span>Sponsored</span>
            <span>•</span>
            <span>🌍</span>
          </div>
        </div>
        <button className="text-gray-400 hover:text-gray-600">
          <span className="text-lg">⋯</span>
        </button>
      </div>

      {/* Content */}
      <div className="p-4">
        <p className="text-sm mb-3">{customData.description}</p>
      </div>

      {/* Image */}
      <div className="bg-gradient-to-br from-blue-100 to-cyan-100 h-64 flex items-center justify-center">
        <div className="text-center text-gray-600">
          <Eye size={48} className="mx-auto mb-2 opacity-50" />
          <p className="text-sm">Campaign Image</p>
          <p className="text-xs opacity-75">{template.creative.media_requirements.image_specs[0]}</p>
        </div>
      </div>

      {/* Link Preview */}
      <div className="border-t bg-gray-50 p-4">
        <div className="flex items-start space-x-3">
          <div className="w-16 h-16 bg-gray-200 flex items-center justify-center text-xs text-gray-500">
            LOGO
          </div>
          <div className="flex-1">
            <p className="font-semibold text-sm text-gray-900">{customData.headline}</p>
            <p className="text-xs text-gray-500 mb-2">{customData.websiteUrl}</p>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 text-sm font-medium w-full transition-colors">
              {customData.cta.replace('_', ' ')}
            </button>
          </div>
        </div>
      </div>

      {/* Engagement */}
      <div className="border-t p-4">
        <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
          <div className="flex items-center space-x-4">
            <span>👍 ❤️ 😮 12</span>
          </div>
          <span>2 comments • 1 share</span>
        </div>
        
        <div className="flex items-center justify-between border-t pt-2">
          <button className="flex-1 flex items-center justify-center space-x-2 py-2 text-gray-600 hover:bg-gray-50">
            <span>👍</span>
            <span className="text-sm font-medium">Like</span>
          </button>
          <button className="flex-1 flex items-center justify-center space-x-2 py-2 text-gray-600 hover:bg-gray-50">
            <span>💬</span>
            <span className="text-sm font-medium">Comment</span>
          </button>
          <button className="flex-1 flex items-center justify-center space-x-2 py-2 text-gray-600 hover:bg-gray-50">
            <span>↗️</span>
            <span className="text-sm font-medium">Share</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderFacebookMobilePreview = () => (
    <div className="bg-white text-black max-w-sm mx-auto shadow-lg">
      {/* Header */}
      <div className="flex items-center space-x-3 p-3 border-b">
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
          {user?.company_name?.charAt(0) || 'B'}
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-1">
            <p className="font-semibold text-sm">{user?.company_name || 'Your Business'}</p>
            <span className="text-blue-500 text-xs">✓</span>
          </div>
          <p className="text-xs text-gray-500">Sponsored • 🌍</p>
        </div>
        <button className="text-gray-400">⋯</button>
      </div>

      {/* Content */}
      <div className="p-3">
        <p className="text-sm mb-3">{customData.description}</p>
      </div>

      {/* Image */}
      <div className="bg-gradient-to-br from-blue-100 to-cyan-100 h-48 flex items-center justify-center">
        <div className="text-center text-gray-600">
          <Eye size={32} className="mx-auto mb-1 opacity-50" />
          <p className="text-xs">Campaign Image</p>
        </div>
      </div>

      {/* Link Preview */}
      <div className="border-t bg-gray-50 p-3">
        <p className="font-semibold text-sm text-gray-900 mb-1">{customData.headline}</p>
        <p className="text-xs text-gray-500 mb-2">{customData.websiteUrl}</p>
        <button className="bg-blue-500 text-white px-4 py-2 text-sm font-medium w-full">
          {customData.cta.replace('_', ' ')}
        </button>
      </div>

      {/* Engagement */}
      <div className="border-t p-3">
        <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
          <span>👍 ❤️ 😮 12</span>
          <span>2 comments</span>
        </div>
        <div className="flex items-center justify-between border-t pt-2">
          <button className="flex-1 text-center py-2 text-gray-600 text-sm">👍 Like</button>
          <button className="flex-1 text-center py-2 text-gray-600 text-sm">💬 Comment</button>
          <button className="flex-1 text-center py-2 text-gray-600 text-sm">↗️ Share</button>
        </div>
      </div>
    </div>
  );

  const renderInstagramFeedPreview = () => (
    <div className="bg-white text-black max-w-sm mx-auto shadow-lg">
      {/* Header */}
      <div className="flex items-center space-x-3 p-3 border-b">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
          {user?.company_name?.charAt(0) || 'B'}
        </div>
        <div className="flex-1">
          <p className="font-semibold text-sm">{user?.company_name?.toLowerCase().replace(/\s+/g, '') || 'yourbusiness'}</p>
          <p className="text-xs text-gray-500">Sponsored</p>
        </div>
        <button className="text-gray-400">⋯</button>
      </div>

      {/* Image */}
      <div className="bg-gradient-to-br from-purple-100 to-pink-100 aspect-square flex items-center justify-center">
        <div className="text-center text-gray-600">
          <Eye size={48} className="mx-auto mb-2 opacity-50" />
          <p className="text-sm">Campaign Image</p>
          <p className="text-xs opacity-75">1080x1080</p>
        </div>
      </div>

      {/* Engagement */}
      <div className="p-3">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4">
            <button>❤️</button>
            <button>💬</button>
            <button>↗️</button>
          </div>
          <button>🔖</button>
        </div>
        
        <p className="text-sm font-semibold mb-1">12 likes</p>
        
        <div className="text-sm">
          <span className="font-semibold">{user?.company_name?.toLowerCase().replace(/\s+/g, '') || 'yourbusiness'}</span>
          <span className="ml-2">{customData.description}</span>
        </div>
        
        <button className="text-blue-500 text-sm mt-2 block">
          {customData.cta.replace('_', ' ')} - {customData.websiteUrl}
        </button>
      </div>
    </div>
  );

  const renderInstagramStoriesPreview = () => (
    <div className="bg-black text-white max-w-xs mx-auto shadow-lg aspect-[9/16] relative overflow-hidden">
      {/* Header */}
      <div className="absolute top-4 left-4 right-4 flex items-center space-x-2 z-10">
        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
          {user?.company_name?.charAt(0) || 'B'}
        </div>
        <p className="font-semibold text-sm">{user?.company_name?.toLowerCase().replace(/\s+/g, '') || 'yourbusiness'}</p>
        <span className="text-xs bg-gray-800 px-2 py-1 rounded">Sponsored</span>
      </div>

      {/* Background Image */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-cyan-600 flex items-center justify-center">
        <div className="text-center text-white">
          <Eye size={64} className="mx-auto mb-4 opacity-75" />
          <p className="text-lg font-bold mb-2">{customData.headline}</p>
          <p className="text-sm opacity-90 px-4">{customData.description}</p>
        </div>
      </div>

      {/* CTA Button */}
      <div className="absolute bottom-8 left-4 right-4">
        <button className="w-full bg-white text-black py-3 px-4 font-bold text-center">
          {customData.cta.replace('_', ' ')}
        </button>
        <p className="text-center text-xs mt-2 opacity-75">{customData.websiteUrl}</p>
      </div>

      {/* Story Progress */}
      <div className="absolute top-2 left-4 right-4 flex space-x-1">
        <div className="flex-1 h-0.5 bg-white opacity-75"></div>
        <div className="flex-1 h-0.5 bg-white opacity-25"></div>
        <div className="flex-1 h-0.5 bg-white opacity-25"></div>
      </div>
    </div>
  );

  const renderPreview = () => {
    switch (selectedPlatform) {
      case 'facebook-feed':
        return renderFacebookFeedPreview();
      case 'facebook-mobile':
        return renderFacebookMobilePreview();
      case 'instagram-feed':
        return renderInstagramFeedPreview();
      case 'instagram-stories':
        return renderInstagramStoriesPreview();
      default:
        return renderFacebookFeedPreview();
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Platform Selector */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Preview Platforms</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {platforms.map((platform) => (
            <button
              key={platform.id}
              onClick={() => setSelectedPlatform(platform.id)}
              className={`p-3 border text-left transition-all ${
                selectedPlatform === platform.id
                  ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                  : 'border-gray-600 text-gray-300 hover:border-gray-500 hover:text-white'
              }`}
            >
              <platform.icon size={20} className="mb-2" />
              <p className="font-medium text-sm">{platform.name}</p>
              <p className="text-xs opacity-75">{platform.description}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Preview */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Live Preview</h3>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Eye size={16} />
            <span>Real-time preview</span>
          </div>
        </div>
        
        <div className="bg-gray-800 p-6 border border-gray-600">
          {renderPreview()}
        </div>
      </div>

      {/* Preview Notes */}
      <div className="bg-gray-800 border border-gray-600 p-4">
        <h4 className="font-semibold text-white mb-2">Preview Notes</h4>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• Images will be automatically optimized for each platform</li>
          <li>• Text may be truncated on mobile devices</li>
          <li>• Actual engagement metrics will vary based on audience</li>
          <li>• Colors may appear differently on various devices</li>
        </ul>
      </div>
    </div>
  );
};
