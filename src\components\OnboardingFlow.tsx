import React, { useState } from 'react';
import { ChevronRight, ChevronLeft, Play, CheckCircle, BookOpen, Video, Users, Zap } from 'lucide-react';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface OnboardingFlowProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  isOpen,
  onClose,
  onComplete
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to PressureMax!',
      description: 'Your complete Facebook ad and lead management system for pressure washing businesses',
      content: (
        <div className="text-center space-y-6">
          <div className="text-6xl mb-4">🚀</div>
          <div className="space-y-4">
            <p className="text-lg text-gray-300">
              PressureMax helps you generate more leads and grow your pressure washing business with:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div className="flex items-center space-x-3">
                <CheckCircle className="text-green-400" size={20} />
                <span>Ready-to-use Facebook ad templates</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="text-green-400" size={20} />
                <span>AI-powered lead calling system</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="text-green-400" size={20} />
                <span>Automated follow-up sequences</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="text-green-400" size={20} />
                <span>CRM/FSM integrations</span>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'templates',
      title: 'Step 1: Choose Your Ad Templates',
      description: 'Browse our library of 20+ proven pressure washing ad templates',
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="text-4xl mb-4">📋</div>
            <p className="text-gray-300 mb-6">
              Our templates are specifically designed for pressure washing services and have been tested to generate high-quality leads.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-900 border border-gray-700 p-4 text-center">
              <h4 className="text-white font-semibold mb-2">Residential</h4>
              <p className="text-sm text-gray-400">House washing, driveways, decks</p>
            </div>
            <div className="bg-gray-900 border border-gray-700 p-4 text-center">
              <h4 className="text-white font-semibold mb-2">Commercial</h4>
              <p className="text-sm text-gray-400">Buildings, parking lots, storefronts</p>
            </div>
            <div className="bg-gray-900 border border-gray-700 p-4 text-center">
              <h4 className="text-white font-semibold mb-2">Seasonal</h4>
              <p className="text-sm text-gray-400">Spring cleaning, winter prep</p>
            </div>
          </div>
          <div className="bg-cyan-500/10 border border-cyan-500/30 p-4">
            <p className="text-cyan-400 text-sm">
              💡 <strong>Pro Tip:</strong> Start with our "House Washing Special" template - it's our highest converting ad for residential customers.
            </p>
          </div>
        </div>
      ),
      action: {
        label: 'Browse Templates',
        onClick: () => console.log('Navigate to templates')
      }
    },
    {
      id: 'deployment',
      title: 'Step 2: Deploy Your Campaigns',
      description: 'One-click deployment to Facebook with optimized targeting',
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="text-4xl mb-4">🎯</div>
            <p className="text-gray-300 mb-6">
              Deploy your ads with pre-configured targeting for homeowners in your service area.
            </p>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="bg-cyan-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
              <div>
                <h4 className="text-white font-semibold">Set Your Budget</h4>
                <p className="text-gray-400 text-sm">Choose daily budget ($20-200 recommended)</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="bg-cyan-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
              <div>
                <h4 className="text-white font-semibold">Select Service Area</h4>
                <p className="text-gray-400 text-sm">Target customers within your service radius</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="bg-cyan-500 text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
              <div>
                <h4 className="text-white font-semibold">Launch Campaign</h4>
                <p className="text-gray-400 text-sm">Your ads go live within 24 hours</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'leads',
      title: 'Step 3: AI Calls Your Leads',
      description: 'Our AI assistant calls every lead within 5 minutes to qualify and book appointments',
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="text-4xl mb-4">🤖</div>
            <p className="text-gray-300 mb-6">
              Sarah, our AI assistant, handles the initial contact so you can focus on the work.
            </p>
          </div>
          <div className="bg-gray-900 border border-gray-700 p-4">
            <h4 className="text-white font-semibold mb-3">What Sarah Does:</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Calls leads within 5 minutes</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Qualifies their needs and budget</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Books appointments in your calendar</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="text-green-400" size={16} />
                <span>Sends follow-up messages</span>
              </div>
            </div>
          </div>
          <div className="bg-green-500/10 border border-green-500/30 p-4">
            <p className="text-green-400 text-sm">
              ⚡ <strong>Result:</strong> 3x more appointments booked compared to manual follow-up.
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'integrations',
      title: 'Step 4: Connect Your Systems',
      description: 'Sync leads with your existing CRM or FSM software',
      content: (
        <div className="space-y-6">
          <div className="text-center">
            <div className="text-4xl mb-4">🔗</div>
            <p className="text-gray-300 mb-6">
              Keep using the tools you love - we'll sync your leads automatically.
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['Jobber', 'HousecallPro', 'ServiceTitan', 'QuoteIQ'].map((system) => (
              <div key={system} className="bg-gray-900 border border-gray-700 p-3 text-center">
                <div className="text-2xl mb-2">🔧</div>
                <p className="text-sm text-white">{system}</p>
              </div>
            ))}
          </div>
          <p className="text-center text-gray-400 text-sm">
            Plus Salesforce, HubSpot, and custom integrations available
          </p>
        </div>
      ),
      action: {
        label: 'View Integrations',
        onClick: () => console.log('Navigate to integrations')
      }
    },
    {
      id: 'success',
      title: 'You\'re All Set!',
      description: 'Start generating leads and growing your business today',
      content: (
        <div className="text-center space-y-6">
          <div className="text-6xl mb-4">🎉</div>
          <div className="space-y-4">
            <h3 className="text-xl text-white font-semibold">Ready to Get Started?</h3>
            <p className="text-gray-300">
              You now have everything you need to generate high-quality leads for your pressure washing business.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-gray-900 border border-gray-700 p-4">
                <BookOpen className="text-cyan-400 mx-auto mb-2" size={24} />
                <h4 className="text-white font-semibold mb-1">Documentation</h4>
                <p className="text-xs text-gray-400">Step-by-step guides</p>
              </div>
              <div className="bg-gray-900 border border-gray-700 p-4">
                <Video className="text-cyan-400 mx-auto mb-2" size={24} />
                <h4 className="text-white font-semibold mb-1">Video Tutorials</h4>
                <p className="text-xs text-gray-400">Watch and learn</p>
              </div>
              <div className="bg-gray-900 border border-gray-700 p-4">
                <Users className="text-cyan-400 mx-auto mb-2" size={24} />
                <h4 className="text-white font-semibold mb-1">Support</h4>
                <p className="text-xs text-gray-400">Get help anytime</p>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentStepData = steps[currentStep];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-black border border-cyan-500/30 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-white">{currentStepData.title}</h2>
              <p className="text-gray-400">{currentStepData.description}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white text-xl"
            >
              ✕
            </button>
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-400">Step {currentStep + 1} of {steps.length}</span>
              <span className="text-sm text-gray-400">{Math.round(((currentStep + 1) / steps.length) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-800 h-2">
              <div 
                className="bg-gradient-to-r from-cyan-500 to-cyan-600 h-2 transition-all duration-300"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="mb-8">
            {currentStepData.content}
          </div>

          {/* Action Button */}
          {currentStepData.action && (
            <div className="mb-6">
              <button
                onClick={currentStepData.action.onClick}
                className="bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 font-bold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 flex items-center space-x-2"
              >
                <Play size={16} />
                <span>{currentStepData.action.label}</span>
              </button>
            </div>
          )}

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <button
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={16} />
              <span>Previous</span>
            </button>

            <div className="flex space-x-2">
              {steps.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentStep(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentStep 
                      ? 'bg-cyan-500' 
                      : index < currentStep 
                        ? 'bg-green-500' 
                        : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>

            <button
              onClick={nextStep}
              className="flex items-center space-x-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-3 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
            >
              <span>{currentStep === steps.length - 1 ? 'Get Started' : 'Next'}</span>
              <ChevronRight size={16} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
