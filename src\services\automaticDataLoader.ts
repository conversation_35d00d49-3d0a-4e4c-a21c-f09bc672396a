/**
 * Automatic Data Loader for PressureMax
 * Handles automatic loading and persistence of real Facebook campaign data
 */

import { facebookDataService } from './facebookDataService';
import { facebookIntegration } from './facebookIntegration';
import { supabaseDb } from './supabaseDatabase';
import { dataCache, CacheKeys } from './dataCache';
import { Campaign } from '../types/database';
import type { FacebookCampaignData } from './facebookDataService';

export interface DataLoadResult {
  success: boolean;
  campaignsLoaded: number;
  leadsLoaded: number;
  errors: string[];
  fromCache: boolean;
}

class AutomaticDataLoader {
  private isLoading = false;
  private lastLoadTime = 0;
  private readonly LOAD_INTERVAL = 10 * 60 * 1000; // 10 minutes
  private autoLoadTimer: NodeJS.Timeout | null = null;

  /**
   * Initialize automatic data loading
   */
  async initialize(): Promise<void> {
    console.log('🚀 Initializing automatic data loader...');
    
    // Load data immediately if Facebook is connected
    const status = facebookIntegration.getIntegrationStatus();
    if (status.isConnected) {
      await this.loadAllData();
    }

    // Start automatic refresh timer
    this.startAutoRefresh();
  }

  /**
   * Load all Facebook data automatically
   */
  async loadAllData(forceRefresh = false): Promise<DataLoadResult> {
    if (this.isLoading) {
      console.log('⏳ Data loading already in progress...');
      return { success: false, campaignsLoaded: 0, leadsLoaded: 0, errors: ['Loading already in progress'], fromCache: false };
    }

    try {
      this.isLoading = true;
      console.log('📊 Starting automatic data load...');

      const status = facebookIntegration.getIntegrationStatus();
      if (!status.isConnected) {
        console.log('⚠️ Facebook not connected - skipping data load');
        return { success: false, campaignsLoaded: 0, leadsLoaded: 0, errors: ['Facebook not connected'], fromCache: false };
      }

      // Check if we should use cached data
      const shouldUseCache = !forceRefresh && (Date.now() - this.lastLoadTime) < this.LOAD_INTERVAL;
      
      if (shouldUseCache) {
        const cachedCampaigns = dataCache.get<FacebookCampaignData[]>(CacheKeys.FACEBOOK_CAMPAIGNS);
        if (cachedCampaigns && cachedCampaigns.length > 0) {
          console.log(`📦 Using cached campaign data (${cachedCampaigns.length} campaigns)`);
          await this.persistCampaignsToDatabase(cachedCampaigns);
          return { 
            success: true, 
            campaignsLoaded: cachedCampaigns.length, 
            leadsLoaded: 0, 
            errors: [], 
            fromCache: true 
          };
        }
      }

      // Load fresh data from Facebook
      console.log('🔄 Loading fresh data from Facebook API...');
      
      const [campaigns, leads] = await Promise.all([
        facebookDataService.getAllCampaignData(),
        facebookDataService.getAllLeads()
      ]);

      console.log(`✅ Loaded ${campaigns.length} campaigns and ${leads.length} leads from Facebook`);

      // Persist campaigns to database
      await this.persistCampaignsToDatabase(campaigns);

      // Persist leads to database
      await this.persistLeadsToDatabase(leads);

      this.lastLoadTime = Date.now();

      return {
        success: true,
        campaignsLoaded: campaigns.length,
        leadsLoaded: leads.length,
        errors: [],
        fromCache: false
      };

    } catch (error) {
      console.error('❌ Error in automatic data load:', error);
      return {
        success: false,
        campaignsLoaded: 0,
        leadsLoaded: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        fromCache: false
      };
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Persist Facebook campaigns to Supabase database
   */
  private async persistCampaignsToDatabase(facebookCampaigns: FacebookCampaignData[]): Promise<void> {
    try {
      console.log(`💾 Persisting ${facebookCampaigns.length} campaigns to database...`);

      for (const fbCampaign of facebookCampaigns) {
        try {
          // Check if campaign already exists
          const existingCampaigns = await supabaseDb.getCampaigns();
          const exists = existingCampaigns.some(c => c.facebook_campaign_id === fbCampaign.id);

          if (!exists) {
            // Convert Facebook campaign to our Campaign format
            const campaign: Omit<Campaign, 'id' | 'created_at' | 'updated_at'> = {
              template_id: 'imported',
              name: fbCampaign.name,
              facebook_campaign_id: fbCampaign.id,
              budget: fbCampaign.daily_budget ? fbCampaign.daily_budget / 100 : 0,
              start_date: new Date(fbCampaign.created_time),
              end_date: fbCampaign.end_time ? new Date(fbCampaign.end_time) : undefined,
              custom_creative: {
                headline: fbCampaign.ads[0]?.creative?.title || fbCampaign.name,
                description: fbCampaign.ads[0]?.creative?.body || `Imported campaign: ${fbCampaign.name}`,
                primary_text: fbCampaign.ads[0]?.creative?.body || '',
                call_to_action: fbCampaign.ads[0]?.creative?.call_to_action_type || 'LEARN_MORE'
              },
              metrics: {
                impressions: fbCampaign.metrics.impressions,
                clicks: fbCampaign.metrics.clicks,
                ctr: fbCampaign.metrics.ctr,
                cpc: fbCampaign.metrics.cpc,
                cpl: fbCampaign.metrics.cpl,
                leads_generated: fbCampaign.metrics.leads,
                spend: fbCampaign.metrics.spend,
                last_sync: new Date()
              },
              status: this.mapFacebookStatus(fbCampaign.status),
              launched_at: fbCampaign.start_time ? new Date(fbCampaign.start_time) : new Date(fbCampaign.created_time)
            };

            await supabaseDb.createCampaign(campaign);
            console.log(`✅ Persisted campaign: ${fbCampaign.name}`);
          } else {
            // Update existing campaign metrics
            await this.updateExistingCampaign(fbCampaign);
          }
        } catch (error) {
          console.error(`❌ Error persisting campaign ${fbCampaign.name}:`, error);
        }
      }

      console.log('✅ Campaign persistence complete');
    } catch (error) {
      console.error('❌ Error in campaign persistence:', error);
      throw error;
    }
  }

  /**
   * Update existing campaign with fresh metrics
   */
  private async updateExistingCampaign(fbCampaign: FacebookCampaignData): Promise<void> {
    try {
      const campaigns = await supabaseDb.getCampaigns();
      const existingCampaign = campaigns.find(c => c.facebook_campaign_id === fbCampaign.id);
      
      if (existingCampaign) {
        const updatedCampaign: Campaign = {
          ...existingCampaign,
          metrics: {
            impressions: fbCampaign.metrics.impressions,
            clicks: fbCampaign.metrics.clicks,
            ctr: fbCampaign.metrics.ctr,
            cpc: fbCampaign.metrics.cpc,
            cpl: fbCampaign.metrics.cpl,
            leads_generated: fbCampaign.metrics.leads,
            spend: fbCampaign.metrics.spend,
            last_sync: new Date()
          },
          status: this.mapFacebookStatus(fbCampaign.status),
          updated_at: new Date()
        };

        await supabaseDb.updateCampaign(existingCampaign.id, updatedCampaign);
        console.log(`🔄 Updated campaign metrics: ${fbCampaign.name}`);
      }
    } catch (error) {
      console.error(`❌ Error updating campaign ${fbCampaign.name}:`, error);
    }
  }

  /**
   * Persist Facebook leads to database
   */
  private async persistLeadsToDatabase(leads: any[]): Promise<void> {
    try {
      console.log(`💾 Persisting ${leads.length} leads to database...`);
      
      for (const lead of leads) {
        try {
          // Convert Facebook lead to our Lead format and persist
          // Implementation would depend on your Lead interface
          console.log(`📝 Processing lead: ${lead.id}`);
        } catch (error) {
          console.error(`❌ Error persisting lead ${lead.id}:`, error);
        }
      }
    } catch (error) {
      console.error('❌ Error in lead persistence:', error);
    }
  }

  /**
   * Map Facebook campaign status to our status format
   */
  private mapFacebookStatus(fbStatus: string): 'active' | 'paused' | 'completed' {
    switch (fbStatus.toLowerCase()) {
      case 'active':
        return 'active';
      case 'paused':
        return 'paused';
      case 'archived':
      case 'deleted':
        return 'completed';
      default:
        return 'paused';
    }
  }

  /**
   * Start automatic refresh timer
   */
  private startAutoRefresh(): void {
    if (this.autoLoadTimer) {
      clearInterval(this.autoLoadTimer);
    }

    this.autoLoadTimer = setInterval(async () => {
      const status = facebookIntegration.getIntegrationStatus();
      if (status.isConnected) {
        console.log('⏰ Automatic data refresh triggered');
        await this.loadAllData();
      }
    }, this.LOAD_INTERVAL);

    console.log(`⏰ Started automatic data refresh every ${this.LOAD_INTERVAL / 60000} minutes`);
  }

  /**
   * Stop automatic refresh
   */
  stopAutoRefresh(): void {
    if (this.autoLoadTimer) {
      clearInterval(this.autoLoadTimer);
      this.autoLoadTimer = null;
      console.log('⏹️ Stopped automatic data refresh');
    }
  }

  /**
   * Get loading status
   */
  getLoadingStatus(): { isLoading: boolean; lastLoadTime: number } {
    return {
      isLoading: this.isLoading,
      lastLoadTime: this.lastLoadTime
    };
  }
}

export const automaticDataLoader = new AutomaticDataLoader();
