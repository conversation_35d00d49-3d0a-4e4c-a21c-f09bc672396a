import React, { useState, useEffect } from 'react';
import { Plus, Settings, Trash2, <PERSON><PERSON>ircle, XCircle, AlertCircle, Zap, Download, Upload } from 'lucide-react';
import { integrationsService, Integration, IntegrationProvider } from '../services/integrations';
import { Lead } from '../types/database';

interface IntegrationsHubProps {
  leads?: Lead[];
  onLeadSync?: (leadId: string, integrationId: string) => void;
  className?: string;
}

export const IntegrationsHub: React.FC<IntegrationsHubProps> = ({
  leads = [],
  onLeadSync,
  className = ''
}) => {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [providers, setProviders] = useState<IntegrationProvider[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<IntegrationProvider | null>(null);
  const [newIntegrationConfig, setNewIntegrationConfig] = useState({
    name: '',
    apiKey: '',
    apiUrl: ''
  });
  const [syncingLeads, setSyncingLeads] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    setIntegrations(integrationsService.getIntegrations());
    setProviders(integrationsService.getProviders());
  };

  const handleCreateIntegration = async () => {
    if (!selectedProvider || !newIntegrationConfig.name) {
      return;
    }

    try {
      await integrationsService.createIntegration({
        providerId: selectedProvider.id,
        name: newIntegrationConfig.name,
        apiKey: newIntegrationConfig.apiKey,
        apiUrl: newIntegrationConfig.apiUrl
      });

      loadData();
      setIsAddModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error creating integration:', error);
    }
  };

  const handleDeleteIntegration = async (integrationId: string) => {
    if (window.confirm('Are you sure you want to delete this integration?')) {
      try {
        await integrationsService.deleteIntegration(integrationId);
        loadData();
      } catch (error) {
        console.error('Error deleting integration:', error);
      }
    }
  };

  const handleSyncLead = async (leadId: string, integrationId: string) => {
    setSyncingLeads(prev => new Set(prev).add(`${leadId}-${integrationId}`));
    
    try {
      const result = await integrationsService.syncLead(leadId, integrationId);
      console.log('Sync result:', result);
      
      if (onLeadSync) {
        onLeadSync(leadId, integrationId);
      }
      
      loadData(); // Refresh stats
    } catch (error) {
      console.error('Error syncing lead:', error);
    } finally {
      setSyncingLeads(prev => {
        const newSet = new Set(prev);
        newSet.delete(`${leadId}-${integrationId}`);
        return newSet;
      });
    }
  };

  const handleBulkSync = async (integrationId: string) => {
    if (leads.length === 0) {
      alert('No leads available to sync');
      return;
    }

    const leadIds = leads.map(lead => lead.id);
    setSyncingLeads(prev => new Set(prev).add(`bulk-${integrationId}`));

    try {
      const result = await integrationsService.bulkSyncLeads(leadIds, integrationId);
      console.log('Bulk sync result:', result);
      alert(`Synced ${result.recordsSuccessful}/${result.recordsProcessed} leads successfully`);
      loadData();
    } catch (error) {
      console.error('Error bulk syncing:', error);
      alert('Bulk sync failed');
    } finally {
      setSyncingLeads(prev => {
        const newSet = new Set(prev);
        newSet.delete(`bulk-${integrationId}`);
        return newSet;
      });
    }
  };

  const resetForm = () => {
    setNewIntegrationConfig({
      name: '',
      apiKey: '',
      apiUrl: ''
    });
    setSelectedProvider(null);
  };

  const getStatusIcon = (status: Integration['status']) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="text-green-400" size={20} />;
      case 'error':
        return <XCircle className="text-red-400" size={20} />;
      default:
        return <AlertCircle className="text-yellow-400" size={20} />;
    }
  };

  const getStatusColor = (status: Integration['status']) => {
    switch (status) {
      case 'connected': return 'text-green-400 bg-green-500/20';
      case 'error': return 'text-red-400 bg-red-500/20';
      default: return 'text-yellow-400 bg-yellow-500/20';
    }
  };

  const getTypeIcon = (type: IntegrationProvider['type']) => {
    switch (type) {
      case 'crm': return '👥';
      case 'fsm': return '🔧';
      case 'email': return '📧';
      case 'sms': return '💬';
      case 'calendar': return '📅';
      default: return '🔗';
    }
  };

  const stats = integrationsService.getIntegrationStats();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Integration Hub</h2>
          <p className="text-gray-400">Connect with your favorite CRM and FSM systems</p>
        </div>
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-3 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>Add Integration</span>
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Integrations</p>
              <p className="text-2xl font-bold text-white">{stats.totalIntegrations}</p>
            </div>
            <Zap className="text-cyan-400" size={24} />
          </div>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Connected</p>
              <p className="text-2xl font-bold text-green-400">{stats.connectedIntegrations}</p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Syncs</p>
              <p className="text-2xl font-bold text-white">{stats.totalSyncs}</p>
            </div>
            <Upload className="text-cyan-400" size={24} />
          </div>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-2xl font-bold text-green-400">
                {stats.totalSyncs > 0 ? Math.round((stats.successfulSyncs / stats.totalSyncs) * 100) : 0}%
              </p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </div>
      </div>

      {/* Active Integrations */}
      <div className="bg-black border border-cyan-500/30">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Active Integrations</h3>
          {integrations.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <Zap className="mx-auto mb-2" size={32} />
              <p>No integrations configured yet</p>
              <p className="text-sm">Connect your CRM or FSM system to start syncing leads</p>
            </div>
          ) : (
            <div className="space-y-4">
              {integrations.map((integration) => (
                <div key={integration.id} className="border border-gray-700 p-4 hover:border-gray-600 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getTypeIcon(integration.type)}</span>
                      <div>
                        <h4 className="text-white font-semibold">{integration.name}</h4>
                        <p className="text-sm text-gray-400 capitalize">{integration.provider} • {integration.type}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(integration.status)}
                        <span className={`px-2 py-1 text-xs font-bold ${getStatusColor(integration.status)}`}>
                          {integration.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleBulkSync(integration.id)}
                          disabled={integration.status !== 'connected' || syncingLeads.has(`bulk-${integration.id}`)}
                          className="text-cyan-400 hover:text-cyan-300 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Bulk Sync All Leads"
                        >
                          {syncingLeads.has(`bulk-${integration.id}`) ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
                          ) : (
                            <Upload size={16} />
                          )}
                        </button>
                        <button
                          className="text-gray-400 hover:text-white"
                          title="Settings"
                        >
                          <Settings size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteIntegration(integration.id)}
                          className="text-red-400 hover:text-red-300"
                          title="Delete"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-400">Sync Settings</p>
                      <p className="text-white">
                        {integration.config.syncSettings.autoSync ? 'Auto' : 'Manual'} • 
                        {integration.config.syncSettings.syncFrequency}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400">Sync Stats</p>
                      <p className="text-white">
                        {integration.syncStats.successfulSyncs}/{integration.syncStats.totalSynced} successful
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400">Last Sync</p>
                      <p className="text-white">
                        {integration.lastSync ? integration.lastSync.toLocaleDateString() : 'Never'}
                      </p>
                    </div>
                  </div>

                  {integration.syncStats.lastError && (
                    <div className="mt-3 p-2 bg-red-500/10 border border-red-500/30 text-red-400 text-sm">
                      <AlertCircle className="inline mr-2" size={14} />
                      {integration.syncStats.lastError}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Available Providers */}
      <div className="bg-black border border-cyan-500/30">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Available Integrations</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {providers.map((provider) => (
              <div key={provider.id} className="border border-gray-700 p-4 hover:border-cyan-500 transition-colors cursor-pointer">
                <div className="flex items-center space-x-3 mb-3">
                  <span className="text-2xl">{getTypeIcon(provider.type)}</span>
                  <div>
                    <h4 className="text-white font-semibold">{provider.name}</h4>
                    <p className="text-xs text-gray-400 capitalize">{provider.type}</p>
                  </div>
                </div>
                <p className="text-sm text-gray-300 mb-3">{provider.description}</p>
                <div className="flex flex-wrap gap-1 mb-3">
                  {provider.features.slice(0, 3).map((feature) => (
                    <span key={feature} className="px-2 py-1 bg-gray-800 text-xs text-gray-300">
                      {feature}
                    </span>
                  ))}
                  {provider.features.length > 3 && (
                    <span className="px-2 py-1 bg-gray-800 text-xs text-gray-400">
                      +{provider.features.length - 3} more
                    </span>
                  )}
                </div>
                <button
                  onClick={() => {
                    setSelectedProvider(provider);
                    setNewIntegrationConfig(prev => ({ ...prev, name: `${provider.name} Integration` }));
                    setIsAddModalOpen(true);
                  }}
                  className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 text-black py-2 px-4 text-sm font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
                >
                  Connect
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Add Integration Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-black border border-cyan-500/30 max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">
                  {selectedProvider ? `Connect ${selectedProvider.name}` : 'Add Integration'}
                </h3>
                <button
                  onClick={() => setIsAddModalOpen(false)}
                  className="text-gray-400 hover:text-white"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Integration Name
                  </label>
                  <input
                    type="text"
                    value={newIntegrationConfig.name}
                    onChange={(e) => setNewIntegrationConfig(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="My CRM Integration"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    API Key
                  </label>
                  <input
                    type="password"
                    value={newIntegrationConfig.apiKey}
                    onChange={(e) => setNewIntegrationConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="Enter your API key"
                  />
                </div>

                {selectedProvider?.authType === 'api_key' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      API URL (Optional)
                    </label>
                    <input
                      type="url"
                      value={newIntegrationConfig.apiUrl}
                      onChange={(e) => setNewIntegrationConfig(prev => ({ ...prev, apiUrl: e.target.value }))}
                      className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                      placeholder="https://api.example.com"
                    />
                  </div>
                )}

                <div className="flex justify-end space-x-4 pt-4">
                  <button
                    onClick={() => setIsAddModalOpen(false)}
                    className="px-6 py-2 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateIntegration}
                    disabled={!selectedProvider || !newIntegrationConfig.name}
                    className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Connect
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
