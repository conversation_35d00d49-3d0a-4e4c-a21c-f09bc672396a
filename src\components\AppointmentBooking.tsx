import React, { useState, useEffect } from 'react';
import { Calendar, Clock, MapPin, User, CheckCircle, X } from 'lucide-react';
import { Lead } from '../types/database';
import { calendarService, TimeSlot, CalendarEvent } from '../services/calendar';
import { db } from '../services/database';

interface AppointmentBookingProps {
  lead: Lead;
  onAppointmentBooked?: (appointment: CalendarEvent) => void;
  className?: string;
}

export const AppointmentBooking: React.FC<AppointmentBookingProps> = ({
  lead,
  onAppointmentBooked,
  className = ''
}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [serviceType, setServiceType] = useState(lead.service_interest || 'House Washing');
  const [duration, setDuration] = useState(60);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingStatus, setBookingStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const serviceOptions = [
    { value: 'House Washing', duration: 60 },
    { value: 'Driveway Cleaning', duration: 45 },
    { value: 'Deck Restoration', duration: 90 },
    { value: 'Roof Cleaning', duration: 120 },
    { value: 'Commercial Building Washing', duration: 120 },
    { value: 'Parking Lot Cleaning', duration: 90 }
  ];

  useEffect(() => {
    loadAvailableSlots();
  }, [selectedDate, duration]);

  const loadAvailableSlots = async () => {
    try {
      const startDate = new Date(selectedDate);
      startDate.setHours(0, 0, 0, 0);
      
      const endDate = new Date(selectedDate);
      endDate.setHours(23, 59, 59, 999);

      const slots = await calendarService.getAvailableSlots(startDate, endDate, duration);
      setAvailableSlots(slots);
    } catch (error) {
      console.error('Error loading available slots:', error);
    }
  };

  const handleServiceChange = (service: string) => {
    setServiceType(service);
    const serviceOption = serviceOptions.find(opt => opt.value === service);
    if (serviceOption) {
      setDuration(serviceOption.duration);
    }
    setSelectedSlot(null);
  };

  const handleBookAppointment = async () => {
    if (!selectedSlot) {
      setErrorMessage('Please select a time slot');
      return;
    }

    setIsBooking(true);
    setErrorMessage('');

    try {
      // Book the appointment
      const appointment = await calendarService.bookAppointment(
        lead,
        selectedSlot.start,
        serviceType,
        duration
      );

      // Update lead in database
      await db.updateLead(lead.id, {
        appointment_scheduled: true,
        appointment_date: selectedSlot.start,
        appointment_notes: `${serviceType} - ${duration} minutes`,
        status: 'appointment'
      });

      setBookingStatus('success');
      
      if (onAppointmentBooked) {
        onAppointmentBooked(appointment);
      }

      // Reset form after success
      setTimeout(() => {
        setBookingStatus('idle');
        setSelectedSlot(null);
      }, 3000);

    } catch (error) {
      console.error('Error booking appointment:', error);
      setBookingStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Failed to book appointment');
    } finally {
      setIsBooking(false);
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getNextWeekDays = () => {
    const days = [];
    const today = new Date();
    
    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      days.push(date);
    }
    
    return days;
  };

  if (bookingStatus === 'success') {
    return (
      <div className={`bg-green-500/10 border border-green-500/30 p-6 text-center ${className}`}>
        <CheckCircle className="text-green-400 mx-auto mb-4" size={48} />
        <h3 className="text-xl font-bold text-white mb-2">Appointment Booked!</h3>
        <p className="text-gray-300 mb-2">
          {formatDate(selectedSlot!.start)} at {formatTime(selectedSlot!.start)}
        </p>
        <p className="text-gray-400 text-sm">
          A calendar invite has been sent to {lead.email || lead.phone}
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-xl font-bold text-white mb-2">Schedule Free Estimate</h3>
        <p className="text-gray-300">Book an appointment for {lead.name}</p>
      </div>

      {/* Service Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Service Type
        </label>
        <select
          value={serviceType}
          onChange={(e) => handleServiceChange(e.target.value)}
          className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
        >
          {serviceOptions.map(option => (
            <option key={option.value} value={option.value}>
              {option.value} ({option.duration} min)
            </option>
          ))}
        </select>
      </div>

      {/* Date Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Select Date
        </label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {getNextWeekDays().map((date) => (
            <button
              key={date.toISOString()}
              onClick={() => setSelectedDate(date)}
              className={`p-3 text-sm border transition-colors ${
                selectedDate.toDateString() === date.toDateString()
                  ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                  : 'border-gray-700 text-gray-300 hover:border-gray-600'
              }`}
            >
              <div className="font-semibold">
                {date.toLocaleDateString('en-US', { weekday: 'short' })}
              </div>
              <div className="text-xs">
                {date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Time Slot Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Available Times for {formatDate(selectedDate)}
        </label>
        
        {availableSlots.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <Clock className="mx-auto mb-2" size={32} />
            <p>No available slots for this date</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto">
            {availableSlots.filter(slot => slot.available).map((slot, index) => (
              <button
                key={index}
                onClick={() => setSelectedSlot(slot)}
                className={`p-3 text-sm border transition-colors ${
                  selectedSlot === slot
                    ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                    : 'border-gray-700 text-gray-300 hover:border-gray-600'
                }`}
              >
                {formatTime(slot.start)}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Appointment Summary */}
      {selectedSlot && (
        <div className="bg-gray-900 border border-gray-700 p-4">
          <h4 className="text-sm font-semibold text-white mb-3">Appointment Summary</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <User className="text-gray-400" size={16} />
              <span className="text-gray-300">{lead.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="text-gray-400" size={16} />
              <span className="text-gray-300">{formatDate(selectedSlot.start)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="text-gray-400" size={16} />
              <span className="text-gray-300">
                {formatTime(selectedSlot.start)} - {formatTime(selectedSlot.end)} ({duration} min)
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="text-gray-400" size={16} />
              <span className="text-gray-300">{lead.address || 'Customer Location'}</span>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 border border-red-500/30 p-3">
          <X size={16} />
          <span className="text-sm">{errorMessage}</span>
        </div>
      )}

      {/* Book Button */}
      <button
        onClick={handleBookAppointment}
        disabled={!selectedSlot || isBooking}
        className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-6 font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isBooking ? 'Booking Appointment...' : 'Book Free Estimate'}
      </button>

      <div className="text-center text-xs text-gray-500">
        <p>Free estimates • No obligation • Professional service</p>
      </div>
    </div>
  );
};
