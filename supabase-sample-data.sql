-- Sample Data for PressureMax
-- Run this after the schema is created to populate with sample data

-- First, create a system admin user for global templates
-- Note: This creates a profile without an auth user (for system templates only)
INSERT INTO profiles (
    id,
    email,
    name,
    company_name,
    role,
    permissions,
    plan,
    subscription_status,
    timezone,
    business_hours
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    'System Admin',
    'PressureMax',
    'admin',
    ARRAY['template_create', 'template_edit', 'template_delete', 'user_manage'],
    'scale',
    'active',
    'UTC',
    '{"start": "00:00", "end": "23:59", "days": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}'
) ON CONFLICT (id) DO NOTHING;

-- Insert service types
INSERT INTO service_types (id, name, icon, color, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Residential', 'home', '#3B82F6', 1),
('550e8400-e29b-41d4-a716-446655440002', 'Commercial', 'building', '#10B981', 2),
('550e8400-e29b-41d4-a716-446655440003', 'Specialty', 'star', '#F59E0B', 3)
ON CONFLICT (id) DO NOTHING;

-- Insert pressure services
INSERT INTO pressure_services (id, service_type_id, name, description, typical_pricing, season_preference, equipment_needed, sort_order) VALUES
-- Residential Services
('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440001', 'House Washing', 'Complete exterior house cleaning', '$200-400', 'Spring/Summer', ARRAY['Surface Cleaner', 'Soft Wash System'], 1),
('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440001', 'Driveway Cleaning', 'Concrete and asphalt driveway cleaning', '$100-250', 'Year-round', ARRAY['Surface Cleaner', 'Hot Water'], 2),
('550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440001', 'Deck Restoration', 'Wood deck cleaning and restoration', '$150-350', 'Spring/Fall', ARRAY['Deck Brush', 'Wood Cleaner'], 3),
('550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440001', 'Fence Cleaning', 'Vinyl, wood, and metal fence cleaning', '$100-200', 'Spring/Summer', ARRAY['Soft Wash System'], 4),
('550e8400-e29b-41d4-a716-446655440015', '550e8400-e29b-41d4-a716-446655440001', 'Patio Cleaning', 'Stone, concrete, and brick patio cleaning', '$75-175', 'Spring/Summer', ARRAY['Surface Cleaner'], 5),

-- Commercial Services
('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-446655440002', 'Building Washing', 'Commercial building exterior cleaning', '$500-2000', 'Year-round', ARRAY['Lift Equipment', 'Soft Wash System'], 1),
('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-446655440002', 'Parking Lot Cleaning', 'Large area concrete cleaning', '$0.15-0.30/sqft', 'Year-round', ARRAY['Surface Cleaner', 'Hot Water'], 2),
('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-446655440002', 'Fleet Washing', 'Commercial vehicle cleaning', '$25-75/vehicle', 'Year-round', ARRAY['Mobile Unit'], 3),

-- Specialty Services
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440003', 'Graffiti Removal', 'Paint and graffiti removal', '$100-300', 'Year-round', ARRAY['Chemical Strippers', 'Hot Water'], 1),
('550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440003', 'Rust Removal', 'Metal surface rust treatment', '$150-400', 'Year-round', ARRAY['Chemical Treatment', 'Sandblasting'], 2)
ON CONFLICT (id) DO NOTHING;

-- Insert sample ad templates (these will be created by the system/admin)
-- Note: In a real scenario, you'd need actual user IDs from the profiles table
-- For now, we'll create these as placeholders that can be updated later

-- Sample House Washing Template
INSERT INTO ad_templates (
    id, name, service_type_id, pressure_service_id, category, service,
    creative, targeting, budget_range, seasonal_timing, target_customer, pricing_strategy,
    performance, template_type, is_public, status, is_featured, created_by
) VALUES (
    '550e8400-e29b-41d4-a716-446655440101',
    'Spring House Washing Special',
    '550e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440011',
    'Residential',
    'House Washing',
    '{
        "primary_text": "Transform your home''s exterior with our professional house washing service! Remove years of dirt, grime, and mildew to reveal your home''s true beauty. Book now and save 20% on your first service!",
        "headline": "Professional House Washing - 20% Off First Service",
        "description": "Expert exterior cleaning that makes your home shine like new",
        "call_to_action": "GET_QUOTE",
        "media_requirements": {
            "before_after_photos": true,
            "action_video": false,
            "equipment_shots": true,
            "image_specs": {
                "width": 1200,
                "height": 628,
                "format": "JPG"
            }
        },
        "image_path": "/images/templates/house_washing_spring_special.png"
    }',
    '{
        "location_radius": "25_miles",
        "age_range": {"min": 30, "max": 65},
        "home_value_range": "$200k_plus",
        "interests": ["home_improvement", "property_maintenance", "homeowner"],
        "exclude_competitors": true,
        "custom_audiences": [],
        "lookalike_audiences": []
    }',
    '{"min": 30, "max": 100, "suggested": 50}',
    ARRAY['spring', 'summer'],
    'homeowner',
    'discount',
    '{"ctr": "3.2%", "cpl": "$12", "conversions": 45, "total_spend": 540, "total_leads": 45, "last_updated": "2024-01-15T10:00:00Z"}',
    'global',
    true,
    'active',
    true,
    '00000000-0000-0000-0000-000000000000'
) ON CONFLICT (id) DO NOTHING;

-- Sample Driveway Cleaning Template
INSERT INTO ad_templates (
    id, name, service_type_id, pressure_service_id, category, service,
    creative, targeting, budget_range, seasonal_timing, target_customer, pricing_strategy,
    performance, template_type, is_public, status, is_featured, created_by
) VALUES (
    '550e8400-e29b-41d4-a716-446655440102',
    'Driveway Deep Clean',
    '550e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440012',
    'Residential',
    'Driveway Cleaning',
    '{
        "primary_text": "Is your driveway an embarrassment? Our professional pressure washing will remove oil stains, dirt, and grime, making your driveway look brand new. Fast, affordable, and guaranteed results!",
        "headline": "Driveway Pressure Washing - Like New Again",
        "description": "Remove stubborn stains and restore your driveway''s appearance",
        "call_to_action": "CONTACT_US",
        "media_requirements": {
            "before_after_photos": true,
            "action_video": true,
            "equipment_shots": false,
            "image_specs": {
                "width": 1200,
                "height": 628,
                "format": "JPG"
            }
        }
    }',
    '{
        "location_radius": "20_miles",
        "age_range": {"min": 25, "max": 70},
        "home_value_range": "$150k_plus",
        "interests": ["home_improvement", "property_maintenance", "curb_appeal"],
        "exclude_competitors": true,
        "custom_audiences": [],
        "lookalike_audiences": []
    }',
    '{"min": 25, "max": 75, "suggested": 40}',
    ARRAY['spring', 'summer', 'fall'],
    'homeowner',
    'competitive',
    '{"ctr": "2.8%", "cpl": "$15", "conversions": 32, "total_spend": 480, "total_leads": 32, "last_updated": "2024-01-15T10:00:00Z"}',
    'global',
    true,
    'active',
    false,
    '00000000-0000-0000-0000-000000000000'
) ON CONFLICT (id) DO NOTHING;

-- Sample Commercial Building Template
INSERT INTO ad_templates (
    id, name, service_type_id, pressure_service_id, category, service,
    creative, targeting, budget_range, seasonal_timing, target_customer, pricing_strategy,
    performance, template_type, is_public, status, is_featured, created_by
) VALUES (
    '550e8400-e29b-41d4-a716-446655440103',
    'Commercial Building Maintenance',
    '550e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440021',
    'Commercial',
    'Building Washing',
    '{
        "primary_text": "Maintain your business''s professional appearance with our commercial building washing services. Regular cleaning protects your investment and creates a positive impression for customers and tenants.",
        "headline": "Professional Commercial Building Washing",
        "description": "Maintain your property''s value and professional appearance",
        "call_to_action": "LEARN_MORE",
        "media_requirements": {
            "before_after_photos": true,
            "action_video": false,
            "equipment_shots": true,
            "image_specs": {
                "width": 1200,
                "height": 628,
                "format": "JPG"
            }
        }
    }',
    '{
        "location_radius": "50_miles",
        "age_range": {"min": 30, "max": 65},
        "home_value_range": "all",
        "interests": ["business_owner", "property_management", "commercial_real_estate"],
        "exclude_competitors": true,
        "custom_audiences": [],
        "lookalike_audiences": []
    }',
    '{"min": 100, "max": 500, "suggested": 200}',
    ARRAY['spring', 'summer', 'fall', 'winter'],
    'business_owner',
    'premium',
    '{"ctr": "1.9%", "cpl": "$35", "conversions": 18, "total_spend": 630, "total_leads": 18, "last_updated": "2024-01-15T10:00:00Z"}',
    'global',
    true,
    'active',
    false,
    '00000000-0000-0000-0000-000000000000'
) ON CONFLICT (id) DO NOTHING;
