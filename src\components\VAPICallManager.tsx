import React, { useState, useEffect } from 'react';
import { Phone, PhoneCall, Clock, CheckCircle, XCircle, Play, Pause } from 'lucide-react';
import { Lead } from '../types/database';
import { vapiService, VAPICall } from '../services/vapi';
import { db } from '../services/database';
import { followUpService } from '../services/followUp';

interface VAPICallManagerProps {
  lead: Lead;
  onCallComplete?: (callResult: VAPICall) => void;
  className?: string;
}

export const VAPICallManager: React.FC<VAPICallManagerProps> = ({
  lead,
  onCallComplete,
  className = ''
}) => {
  const [isInitiatingCall, setIsInitiatingCall] = useState(false);
  const [currentCall, setCurrentCall] = useState<VAPICall | null>(null);
  const [assistantId, setAssistantId] = useState<string>('');
  const [callHistory, setCallHistory] = useState<VAPICall[]>([]);

  useEffect(() => {
    // Initialize assistant on component mount
    initializeAssistant();
    loadCallHistory();
  }, []);

  useEffect(() => {
    // Poll for call updates if there's an active call
    if (currentCall && currentCall.status !== 'ended') {
      const interval = setInterval(async () => {
        try {
          const updatedCall = await vapiService.getCall(currentCall.id);
          setCurrentCall(updatedCall);
          
          if (updatedCall.status === 'ended') {
            handleCallComplete(updatedCall);
            clearInterval(interval);
          }
        } catch (error) {
          console.error('Error polling call status:', error);
        }
      }, 3000); // Poll every 3 seconds

      return () => clearInterval(interval);
    }
  }, [currentCall]);

  const initializeAssistant = async () => {
    try {
      // Use the enhanced appointment booking assistant
      const assistant = await vapiService.createAppointmentBookingAssistant();
      setAssistantId(assistant.id);
      console.log('VAPI appointment booking assistant initialized:', assistant.id);
    } catch (error) {
      console.error('Error initializing VAPI assistant:', error);
      // Fallback to basic assistant
      try {
        const fallbackAssistant = await vapiService.createPressureWashingAssistant();
        setAssistantId(fallbackAssistant.id);
        console.log('Fallback VAPI assistant initialized:', fallbackAssistant.id);
      } catch (fallbackError) {
        console.error('Error initializing fallback assistant:', fallbackError);
      }
    }
  };

  const loadCallHistory = async () => {
    try {
      const calls = await vapiService.getCalls();
      // Filter calls for this lead (in a real app, you'd have lead metadata)
      const leadCalls = calls.filter(call => 
        call.phoneNumber === lead.phone
      );
      setCallHistory(leadCalls);
    } catch (error) {
      console.error('Error loading call history:', error);
    }
  };

  const initiateCall = async () => {
    if (!assistantId) {
      console.error('Assistant not initialized');
      return;
    }

    setIsInitiatingCall(true);

    try {
      // Enhanced call with metadata for appointment booking
      const callRequest = {
        assistantId,
        phoneNumber: lead.phone,
        customerName: lead.name,
        metadata: {
          leadId: lead.id,
          serviceInterest: lead.service_interest || 'pressure washing',
          urgency: lead.urgency || 'this_month',
          propertyType: lead.property_type || 'residential',
          address: lead.address || '',
          customerName: lead.name,
          customerPhone: lead.phone,
          appointmentBooking: 'enabled'
        }
      };

      const call = await vapiService.callLead(lead, assistantId);
      setCurrentCall(call);

      // Update lead status with enhanced tracking
      await db.updateLead(lead.id, {
        call_status: 'calling',
        call_attempts: lead.call_attempts + 1,
        last_call_at: new Date(),
        vapi_call_id: call.id,
        notes: `${lead.notes || ''}\n\nVAPI call initiated at ${new Date().toLocaleString()} with appointment booking enabled.`
      });

      console.log('VAPI call initiated with appointment booking:', call.id);

    } catch (error) {
      console.error('Error initiating call:', error);
      // Update lead with error status
      await db.updateLead(lead.id, {
        call_status: 'failed',
        notes: `${lead.notes || ''}\n\nCall failed at ${new Date().toLocaleString()}: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsInitiatingCall(false);
    }
  };

  const handleCallComplete = async (call: VAPICall) => {
    try {
      // Analyze call outcome
      const analysis = await vapiService.analyzeCallOutcome(call.id);
      
      // Update lead based on call analysis
      await db.updateLead(lead.id, {
        call_status: 'completed',
        quality: analysis.leadQuality,
        appointment_scheduled: analysis.appointmentScheduled,
        status: analysis.appointmentScheduled ? 'appointment' : 
                analysis.followUpNeeded ? 'contacted' : 'qualified',
        notes: `${lead.notes}\n\nCall completed: ${analysis.intent}. Sentiment: ${analysis.sentiment}.${
          analysis.appointmentScheduled ? ' Appointment scheduled!' : ''
        }`
      });

      // Schedule follow-up if needed
      if (analysis.followUpNeeded && !analysis.appointmentScheduled) {
        await vapiService.scheduleFollowUpCall(lead.id, assistantId, 60); // 1 hour follow-up

        // Check for automated follow-up sequences
        const qualifyingSequences = await followUpService.checkLeadForSequences({
          ...lead,
          quality: analysis.leadQuality,
          status: analysis.appointmentScheduled ? 'appointment' :
                  analysis.followUpNeeded ? 'contacted' : 'qualified'
        });

        // Start the most appropriate sequence
        if (qualifyingSequences.length > 0) {
          await followUpService.startSequence(lead.id, qualifyingSequences[0].id);
        }
      }

      setCallHistory(prev => [...prev, call]);
      
      if (onCallComplete) {
        onCallComplete(call);
      }

    } catch (error) {
      console.error('Error handling call completion:', error);
    }
  };

  const getCallStatusIcon = (status: VAPICall['status']) => {
    switch (status) {
      case 'queued':
        return <Clock className="text-yellow-400" size={16} />;
      case 'ringing':
        return <Phone className="text-blue-400 animate-pulse" size={16} />;
      case 'in-progress':
        return <PhoneCall className="text-green-400" size={16} />;
      case 'ended':
        return <CheckCircle className="text-gray-400" size={16} />;
      default:
        return <XCircle className="text-red-400" size={16} />;
    }
  };

  const getCallStatusText = (status: VAPICall['status']) => {
    switch (status) {
      case 'queued':
        return 'Queued';
      case 'ringing':
        return 'Ringing...';
      case 'in-progress':
        return 'In Progress';
      case 'ended':
        return 'Completed';
      default:
        return 'Failed';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Call Action Button */}
      <div className="flex items-center space-x-4">
        <button
          onClick={initiateCall}
          disabled={isInitiatingCall || (currentCall && currentCall.status !== 'ended')}
          className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isInitiatingCall ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Initiating Call...</span>
            </>
          ) : (
            <>
              <Phone size={16} />
              <span>Call Lead</span>
            </>
          )}
        </button>

        {currentCall && currentCall.status !== 'ended' && (
          <div className="flex items-center space-x-2 text-sm">
            {getCallStatusIcon(currentCall.status)}
            <span className="text-gray-300">{getCallStatusText(currentCall.status)}</span>
            {currentCall.status === 'in-progress' && (
              <span className="text-green-400">
                {Math.floor((Date.now() - new Date(currentCall.startedAt!).getTime()) / 1000)}s
              </span>
            )}
          </div>
        )}
      </div>

      {/* Current Call Status */}
      {currentCall && (
        <div className="bg-gray-900 border border-gray-700 p-4">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-semibold text-white">Current Call</h4>
            <div className="flex items-center space-x-2">
              {getCallStatusIcon(currentCall.status)}
              <span className="text-sm text-gray-300">{getCallStatusText(currentCall.status)}</span>
            </div>
          </div>
          
          <div className="text-xs text-gray-400 space-y-1">
            <div>Call ID: {currentCall.id}</div>
            <div>Phone: {currentCall.phoneNumber}</div>
            {currentCall.startedAt && (
              <div>Started: {new Date(currentCall.startedAt).toLocaleTimeString()}</div>
            )}
            {currentCall.cost && (
              <div>Cost: ${currentCall.cost.toFixed(2)}</div>
            )}
          </div>

          {currentCall.transcript && (
            <div className="mt-3 p-3 bg-black border border-gray-600">
              <h5 className="text-xs font-semibold text-gray-300 mb-2">Transcript:</h5>
              <div className="text-xs text-gray-400 max-h-32 overflow-y-auto">
                {currentCall.transcript}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Call History */}
      {callHistory.length > 0 && (
        <div className="bg-gray-900 border border-gray-700 p-4">
          <h4 className="text-sm font-semibold text-white mb-3">Call History</h4>
          <div className="space-y-2">
            {callHistory.slice(-3).map((call) => (
              <div key={call.id} className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  {getCallStatusIcon(call.status)}
                  <span className="text-gray-300">
                    {call.startedAt ? new Date(call.startedAt).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-gray-400">
                  {call.cost && <span>${call.cost.toFixed(2)}</span>}
                  <span>{getCallStatusText(call.status)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Lead Call Settings */}
      <div className="bg-gray-900 border border-gray-700 p-4">
        <h4 className="text-sm font-semibold text-white mb-3">Call Settings</h4>
        <div className="space-y-2 text-xs text-gray-400">
          <div className="flex justify-between">
            <span>Call Attempts:</span>
            <span className="text-white">{lead.call_attempts}</span>
          </div>
          <div className="flex justify-between">
            <span>Last Call:</span>
            <span className="text-white">
              {lead.last_call_at ? new Date(lead.last_call_at).toLocaleDateString() : 'Never'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Next Call:</span>
            <span className="text-white">
              {lead.next_call_at ? new Date(lead.next_call_at).toLocaleDateString() : 'Not scheduled'}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Call Status:</span>
            <span className={`font-semibold ${
              lead.call_status === 'pending' ? 'text-yellow-400' :
              lead.call_status === 'calling' ? 'text-blue-400' :
              lead.call_status === 'completed' ? 'text-green-400' :
              lead.call_status === 'failed' ? 'text-red-400' :
              'text-gray-400'
            }`}>
              {lead.call_status.toUpperCase()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
