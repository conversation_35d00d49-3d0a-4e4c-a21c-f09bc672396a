/**
 * Appointment Scheduling System for PressureMax
 * Integrates with Google Calendar and Outlook for real-time availability
 */

import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  CheckCircle, 
  AlertCircle,
  Plus,
  Edit,
  Trash2,
  ExternalLink,
  RefreshCw
} from 'lucide-react';

interface TimeSlot {
  id: string;
  date: string;
  time: string;
  duration: number; // in minutes
  available: boolean;
  service_type?: string;
}

interface Appointment {
  id: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  service_type: string;
  appointment_date: string;
  appointment_time: string;
  duration: number;
  address?: string;
  notes?: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no_show';
  lead_id?: string;
  created_at: Date;
  calendar_event_id?: string;
}

interface AppointmentSchedulerProps {
  leadId?: string;
  prefilledData?: {
    name?: string;
    phone?: string;
    email?: string;
    serviceType?: string;
  };
  onAppointmentBooked?: (appointment: Appointment) => void;
  className?: string;
}

export const AppointmentScheduler: React.FC<AppointmentSchedulerProps> = ({
  leadId,
  prefilledData,
  onAppointmentBooked,
  className = ''
}) => {
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCalendarConnected, setIsCalendarConnected] = useState(false);
  const [calendarProvider, setCalendarProvider] = useState<'google' | 'outlook' | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    customer_name: prefilledData?.name || '',
    customer_phone: prefilledData?.phone || '',
    customer_email: prefilledData?.email || '',
    service_type: prefilledData?.serviceType || 'house_washing',
    address: '',
    notes: '',
    duration: 60 // Default 1 hour
  });

  useEffect(() => {
    loadAppointments();
    checkCalendarConnection();
    if (selectedDate) {
      loadAvailableSlots(selectedDate);
    }
  }, [selectedDate]);

  const loadAppointments = async () => {
    try {
      // Mock appointments - in production, fetch from backend
      const mockAppointments: Appointment[] = [
        {
          id: 'apt_1',
          customer_name: 'John Smith',
          customer_phone: '(*************',
          customer_email: '<EMAIL>',
          service_type: 'house_washing',
          appointment_date: '2024-01-15',
          appointment_time: '10:00',
          duration: 60,
          address: '123 Main St, Anytown, ST 12345',
          status: 'scheduled',
          lead_id: 'lead_1',
          created_at: new Date(),
          calendar_event_id: 'google_event_123'
        }
      ];
      setAppointments(mockAppointments);
    } catch (error) {
      console.error('Error loading appointments:', error);
    }
  };

  const checkCalendarConnection = async () => {
    try {
      // Check if user has connected calendar
      const response = await fetch('/api/calendar/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setIsCalendarConnected(data.connected);
        setCalendarProvider(data.provider);
      }
    } catch (error) {
      console.error('Error checking calendar connection:', error);
    }
  };

  const loadAvailableSlots = async (date: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/calendar/availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_token')}`
        },
        body: JSON.stringify({
          date,
          service_type: formData.service_type,
          duration: formData.duration
        })
      });

      if (response.ok) {
        const data = await response.json();
        setAvailableSlots(data.available_slots);
      } else {
        // Fallback to mock data
        generateMockAvailableSlots(date);
      }
    } catch (error) {
      console.error('Error loading available slots:', error);
      generateMockAvailableSlots(date);
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockAvailableSlots = (date: string) => {
    const slots: TimeSlot[] = [];
    const times = ['09:00', '10:00', '11:00', '13:00', '14:00', '15:00', '16:00'];
    
    times.forEach((time, index) => {
      slots.push({
        id: `slot_${date}_${time}`,
        date,
        time,
        duration: formData.duration,
        available: Math.random() > 0.3 // 70% chance of being available
      });
    });
    
    setAvailableSlots(slots);
  };

  const bookAppointment = async () => {
    if (!selectedDate || !selectedTime) {
      alert('Please select a date and time');
      return;
    }

    if (!formData.customer_name || !formData.customer_phone) {
      alert('Please provide customer name and phone number');
      return;
    }

    setIsLoading(true);
    try {
      const appointmentData = {
        ...formData,
        appointment_date: selectedDate,
        appointment_time: selectedTime,
        lead_id: leadId,
        status: 'scheduled'
      };

      const response = await fetch('/api/appointments/book', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_token')}`
        },
        body: JSON.stringify(appointmentData)
      });

      if (response.ok) {
        const newAppointment = await response.json();
        setAppointments(prev => [...prev, newAppointment]);
        
        // Reset form
        setSelectedDate('');
        setSelectedTime('');
        setFormData({
          customer_name: '',
          customer_phone: '',
          customer_email: '',
          service_type: 'house_washing',
          address: '',
          notes: '',
          duration: 60
        });

        if (onAppointmentBooked) {
          onAppointmentBooked(newAppointment);
        }

        alert('Appointment booked successfully!');
      } else {
        throw new Error('Failed to book appointment');
      }
    } catch (error) {
      console.error('Error booking appointment:', error);
      alert('Failed to book appointment. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const connectCalendar = async (provider: 'google' | 'outlook') => {
    try {
      // Redirect to OAuth flow
      window.location.href = `/api/calendar/connect/${provider}`;
    } catch (error) {
      console.error('Error connecting calendar:', error);
    }
  };

  const getNextWeekDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date.toISOString().split('T')[0]);
    }
    
    return dates;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'text-blue-400';
      case 'confirmed':
        return 'text-green-400';
      case 'completed':
        return 'text-gray-400';
      case 'cancelled':
        return 'text-red-400';
      case 'no_show':
        return 'text-orange-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Appointment Scheduling</h2>
          <p className="text-gray-400">Manage appointments and calendar integration</p>
        </div>
        
        {!isCalendarConnected && (
          <div className="flex items-center space-x-2">
            <button
              onClick={() => connectCalendar('google')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 transition-colors flex items-center space-x-2"
            >
              <ExternalLink size={16} />
              <span>Connect Google Calendar</span>
            </button>
            <button
              onClick={() => connectCalendar('outlook')}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 transition-colors flex items-center space-x-2"
            >
              <ExternalLink size={16} />
              <span>Connect Outlook</span>
            </button>
          </div>
        )}
      </div>

      {/* Calendar Connection Status */}
      {isCalendarConnected && (
        <div className="bg-green-900/20 border border-green-500/30 p-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="text-green-400" size={20} />
            <span className="text-green-400 font-medium">
              Calendar Connected ({calendarProvider})
            </span>
            <button
              onClick={checkCalendarConnection}
              className="ml-auto text-green-400 hover:text-green-300"
            >
              <RefreshCw size={16} />
            </button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Booking Form */}
        <div className="bg-gray-900 border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Book New Appointment</h3>
          
          <div className="space-y-4">
            {/* Customer Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Customer Name *</label>
                <input
                  type="text"
                  value={formData.customer_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                  className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="John Smith"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Phone Number *</label>
                <input
                  type="tel"
                  value={formData.customer_phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, customer_phone: e.target.value }))}
                  className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="(*************"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Email</label>
              <input
                type="email"
                value={formData.customer_email}
                onChange={(e) => setFormData(prev => ({ ...prev, customer_email: e.target.value }))}
                className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Service Type</label>
                <select
                  value={formData.service_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, service_type: e.target.value }))}
                  className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                >
                  <option value="house_washing">House Washing</option>
                  <option value="driveway_cleaning">Driveway Cleaning</option>
                  <option value="deck_restoration">Deck Restoration</option>
                  <option value="roof_cleaning">Roof Cleaning</option>
                  <option value="commercial_building">Commercial Building</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Duration (minutes)</label>
                <select
                  value={formData.duration}
                  onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                  className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                >
                  <option value={30}>30 minutes</option>
                  <option value={60}>1 hour</option>
                  <option value={90}>1.5 hours</option>
                  <option value={120}>2 hours</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Address</label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                placeholder="123 Main St, City, State 12345"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Notes</label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                rows={3}
                placeholder="Special instructions or notes..."
              />
            </div>

            {/* Date Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Select Date</label>
              <div className="grid grid-cols-7 gap-2">
                {getNextWeekDates().map((date) => (
                  <button
                    key={date}
                    onClick={() => setSelectedDate(date)}
                    className={`p-2 text-sm border transition-colors ${
                      selectedDate === date
                        ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                        : 'border-gray-600 text-gray-300 hover:border-gray-500'
                    }`}
                  >
                    {formatDate(date)}
                  </button>
                ))}
              </div>
            </div>

            {/* Time Selection */}
            {selectedDate && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Select Time</label>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-2 border-cyan-500 border-t-transparent"></div>
                  </div>
                ) : (
                  <div className="grid grid-cols-4 gap-2">
                    {availableSlots.map((slot) => (
                      <button
                        key={slot.id}
                        onClick={() => setSelectedTime(slot.time)}
                        disabled={!slot.available}
                        className={`p-2 text-sm border transition-colors ${
                          !slot.available
                            ? 'border-gray-700 bg-gray-800 text-gray-500 cursor-not-allowed'
                            : selectedTime === slot.time
                            ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                            : 'border-gray-600 text-gray-300 hover:border-gray-500'
                        }`}
                      >
                        {slot.time}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            <button
              onClick={bookAppointment}
              disabled={isLoading || !selectedDate || !selectedTime || !formData.customer_name || !formData.customer_phone}
              className="w-full bg-gradient-to-r from-green-500 to-green-600 text-black py-3 font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-black border-t-transparent"></div>
                  <span>Booking...</span>
                </>
              ) : (
                <>
                  <Calendar size={16} />
                  <span>Book Appointment</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Upcoming Appointments */}
        <div className="bg-gray-900 border border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Upcoming Appointments</h3>
          
          <div className="space-y-4">
            {appointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="mx-auto text-gray-500 mb-4" size={48} />
                <p className="text-gray-400">No appointments scheduled</p>
              </div>
            ) : (
              appointments.map((appointment) => (
                <div key={appointment.id} className="bg-gray-800 border border-gray-600 p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-semibold text-white">{appointment.customer_name}</h4>
                      <p className="text-sm text-gray-400">{appointment.service_type.replace('_', ' ')}</p>
                    </div>
                    <span className={`text-sm font-medium capitalize ${getStatusColor(appointment.status)}`}>
                      {appointment.status}
                    </span>
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-300">
                    <div className="flex items-center space-x-2">
                      <Calendar size={14} />
                      <span>{formatDate(appointment.appointment_date)} at {appointment.appointment_time}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone size={14} />
                      <span>{appointment.customer_phone}</span>
                    </div>
                    {appointment.address && (
                      <div className="flex items-center space-x-2">
                        <MapPin size={14} />
                        <span>{appointment.address}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 mt-3">
                    <button className="p-1 bg-blue-600 hover:bg-blue-700 text-white transition-colors">
                      <Edit size={14} />
                    </button>
                    <button className="p-1 bg-red-600 hover:bg-red-700 text-white transition-colors">
                      <Trash2 size={14} />
                    </button>
                    {appointment.calendar_event_id && (
                      <button className="p-1 bg-green-600 hover:bg-green-700 text-white transition-colors">
                        <ExternalLink size={14} />
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
