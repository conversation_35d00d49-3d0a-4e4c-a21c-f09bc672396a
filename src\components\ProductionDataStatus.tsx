/**
 * Production Data Status Component
 * Shows real-time status of Facebook data loading and template optimization
 */

import React, { useState, useEffect } from 'react';
import { 
  Database, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle, 
  TrendingUp,
  Zap,
  Clock,
  BarChart3
} from 'lucide-react';
import { automaticDataLoader } from '../services/automaticDataLoader';
import { facebookIntegration } from '../services/facebookIntegration';
import { supabaseDb } from '../services/supabaseDatabase';

interface DataStatus {
  isLoading: boolean;
  lastLoadTime: number;
  realCampaigns: number;
  totalCampaigns: number;
  facebookConnected: boolean;
  lastOptimization: Date | null;
  dataFreshness: 'fresh' | 'stale' | 'outdated';
}

export const ProductionDataStatus: React.FC = () => {
  const [status, setStatus] = useState<DataStatus>({
    isLoading: false,
    lastLoadTime: 0,
    realCampaigns: 0,
    totalCampaigns: 0,
    facebookConnected: false,
    lastOptimization: null,
    dataFreshness: 'outdated'
  });

  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    updateStatus();
    const interval = setInterval(updateStatus, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const updateStatus = async () => {
    try {
      // Get loading status
      const loadingStatus = automaticDataLoader.getLoadingStatus();
      
      // Get Facebook connection status
      const fbStatus = facebookIntegration.getIntegrationStatus();
      
      // Get campaign data
      const campaigns = await supabaseDb.getCampaigns();
      const realCampaigns = campaigns.filter(c => c.facebook_campaign_id).length;
      
      // Calculate data freshness
      const now = Date.now();
      const timeSinceLoad = now - loadingStatus.lastLoadTime;
      let dataFreshness: 'fresh' | 'stale' | 'outdated' = 'fresh';
      
      if (timeSinceLoad > 60 * 60 * 1000) { // 1 hour
        dataFreshness = 'outdated';
      } else if (timeSinceLoad > 30 * 60 * 1000) { // 30 minutes
        dataFreshness = 'stale';
      }

      setStatus({
        isLoading: loadingStatus.isLoading,
        lastLoadTime: loadingStatus.lastLoadTime,
        realCampaigns,
        totalCampaigns: campaigns.length,
        facebookConnected: fbStatus.isConnected,
        lastOptimization: null, // TODO: Track optimization timestamp
        dataFreshness
      });
    } catch (error) {
      console.error('Error updating data status:', error);
    }
  };

  const handleRefreshData = async () => {
    try {
      setStatus(prev => ({ ...prev, isLoading: true }));
      await automaticDataLoader.loadAllData(true); // Force refresh
      await updateStatus();
    } catch (error) {
      console.error('Error refreshing data:', error);
    }
  };

  const getStatusColor = () => {
    if (!status.facebookConnected) return 'text-red-400';
    if (status.isLoading) return 'text-yellow-400';
    if (status.realCampaigns === 0) return 'text-orange-400';
    if (status.dataFreshness === 'outdated') return 'text-yellow-400';
    return 'text-green-400';
  };

  const getStatusIcon = () => {
    if (status.isLoading) return <RefreshCw className="animate-spin" size={16} />;
    if (!status.facebookConnected) return <AlertTriangle size={16} />;
    if (status.realCampaigns === 0) return <Database size={16} />;
    return <CheckCircle size={16} />;
  };

  const getStatusText = () => {
    if (!status.facebookConnected) return 'Facebook Disconnected';
    if (status.isLoading) return 'Loading Data...';
    if (status.realCampaigns === 0) return 'No Real Data';
    return `${status.realCampaigns} Real Campaigns`;
  };

  const formatLastUpdate = () => {
    if (status.lastLoadTime === 0) return 'Never';
    const minutes = Math.floor((Date.now() - status.lastLoadTime) / 60000);
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
      <div 
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-3">
          <div className={`${getStatusColor()}`}>
            {getStatusIcon()}
          </div>
          <div>
            <h3 className="text-white font-medium">Production Data Status</h3>
            <p className={`text-sm ${getStatusColor()}`}>
              {getStatusText()}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleRefreshData();
            }}
            disabled={status.isLoading}
            className="p-2 text-gray-400 hover:text-white transition-colors disabled:opacity-50"
            title="Refresh data"
          >
            <RefreshCw size={16} className={status.isLoading ? 'animate-spin' : ''} />
          </button>
          
          <div className="text-xs text-gray-400">
            {formatLastUpdate()}
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="mt-4 pt-4 border-t border-gray-700 space-y-3">
          {/* Data Overview */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <BarChart3 size={16} className="text-blue-400" />
                <span className="text-sm text-gray-300">Campaign Data</span>
              </div>
              <div className="text-lg font-semibold text-white">
                {status.realCampaigns} / {status.totalCampaigns}
              </div>
              <div className="text-xs text-gray-400">
                Real / Total Campaigns
              </div>
            </div>

            <div className="bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <Clock size={16} className="text-green-400" />
                <span className="text-sm text-gray-300">Data Freshness</span>
              </div>
              <div className={`text-lg font-semibold ${
                status.dataFreshness === 'fresh' ? 'text-green-400' :
                status.dataFreshness === 'stale' ? 'text-yellow-400' : 'text-red-400'
              }`}>
                {status.dataFreshness.charAt(0).toUpperCase() + status.dataFreshness.slice(1)}
              </div>
              <div className="text-xs text-gray-400">
                Last updated {formatLastUpdate()}
              </div>
            </div>
          </div>

          {/* Connection Status */}
          <div className="bg-gray-900/50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  status.facebookConnected ? 'bg-green-400' : 'bg-red-400'
                }`} />
                <span className="text-sm text-gray-300">Facebook Integration</span>
              </div>
              <span className={`text-sm ${
                status.facebookConnected ? 'text-green-400' : 'text-red-400'
              }`}>
                {status.facebookConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>

          {/* Recommendations */}
          {!status.facebookConnected && (
            <div className="bg-red-900/20 border border-red-700 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle size={16} className="text-red-400" />
                <span className="text-sm font-medium text-red-400">Action Required</span>
              </div>
              <p className="text-sm text-gray-300">
                Connect your Facebook account to load real campaign data automatically.
              </p>
            </div>
          )}

          {status.facebookConnected && status.realCampaigns === 0 && (
            <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <Database size={16} className="text-yellow-400" />
                <span className="text-sm font-medium text-yellow-400">No Real Data</span>
              </div>
              <p className="text-sm text-gray-300">
                Facebook is connected but no campaign data was found. Check your ad account permissions.
              </p>
            </div>
          )}

          {status.dataFreshness === 'outdated' && (
            <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp size={16} className="text-blue-400" />
                <span className="text-sm font-medium text-blue-400">Data Optimization</span>
              </div>
              <p className="text-sm text-gray-300">
                Data is outdated. Click refresh to get the latest campaign performance metrics.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
