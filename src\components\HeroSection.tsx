import React, { useState, useEffect } from 'react';
import { Eye } from 'lucide-react';

const HeroSection: React.FC = () => {
  const [textIndex, setTextIndex] = useState(0);
  const [showCTA, setShowCTA] = useState(false);

  const messages = [
    "WE SEE YOU",
    "THE SIGNAL IS NEARING", 
    "ALIGN OR BE REMEMBERED POORLY"
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setTextIndex((prev) => (prev + 1) % messages.length);
    }, 3000);

    const ctaTimer = setTimeout(() => setShowCTA(true), 4000);

    return () => {
      clearInterval(interval);
      clearTimeout(ctaTimer);
    };
  }, []);

  return (
    <section className="min-h-screen flex items-center justify-center relative">
      {/* Animated Background Grid */}
      <div className="absolute inset-0 opacity-10">
        <div className="grid-pattern animate-pulse" />
      </div>

      <div className="text-center z-10">
        {/* AP3X Logo */}
        <div className="relative mb-12">
          <img 
            src="/EyePex.png" 
            alt="AP3X Eye" 
            className="w-64 h-64 mx-auto animate-pulse-slow filter drop-shadow-2xl"
          />
          <div className="absolute inset-0 bg-red-500/20 rounded-full blur-3xl animate-pulse" />
        </div>

        {/* Dynamic Text */}
        <div className="mb-8 h-32 flex items-center justify-center">
          <h1 className="text-4xl md:text-6xl font-bold tracking-wider font-orbitron">
            <span className="block text-white transition-all duration-1000 transform">
              {messages[textIndex]}
            </span>
          </h1>
        </div>

        {/* Glitch Separator */}
        <div className="flex items-center justify-center mb-8">
          <div className="h-px bg-gradient-to-r from-transparent via-red-500 to-transparent w-64" />
          <div className="mx-4 text-red-500 animate-pulse">⸺</div>
          <div className="h-px bg-gradient-to-r from-transparent via-red-500 to-transparent w-64" />
        </div>

        {/* Call to Action */}
        <div className={`transition-all duration-1000 ${showCTA ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="space-y-4">
            <button className="block mx-auto px-8 py-4 border-2 border-white text-white hover:bg-white hover:text-black transition-all duration-300 tracking-widest font-orbitron text-lg">
              ▸ ENTER THE NODE
            </button>
            <div className="text-sm text-gray-400 space-y-2">
              <div className="hover:text-red-500 cursor-pointer transition-colors">▸ OBSERVE FIRST | OATH LATER</div>
              <div className="hover:text-red-500 cursor-pointer transition-colors">▸ Ξ VERIFY LINEAGE</div>
            </div>
          </div>
        </div>

        {/* Breathing Effect */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border border-red-500/20 rounded-full animate-ping" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 border border-red-500/30 rounded-full animate-ping animation-delay-1000" />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;