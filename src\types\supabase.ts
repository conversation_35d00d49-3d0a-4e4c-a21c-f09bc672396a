/**
 * Supabase Database Types for PressureMax
 * Generated types for type-safe database operations
 */

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          name: string;
          company_name: string;
          phone: string | null;
          role: 'admin' | 'user';
          permissions: string[];
          plan: 'starter' | 'growth' | 'scale';
          subscription_status: 'active' | 'cancelled' | 'past_due';
          facebook_access_token: string | null;
          facebook_ad_account_id: string | null;
          facebook_page_id: string | null;
          vapi_api_key: string | null;
          vapi_assistant_id: string | null;
          timezone: string;
          business_hours: {
            start: string;
            end: string;
            days: string[];
          };
          created_at: string;
          updated_at: string;
          last_login_at: string | null;
        };
        Insert: {
          id: string;
          email: string;
          name: string;
          company_name: string;
          phone?: string | null;
          role?: 'admin' | 'user';
          permissions?: string[];
          plan?: 'starter' | 'growth' | 'scale';
          subscription_status?: 'active' | 'cancelled' | 'past_due';
          facebook_access_token?: string | null;
          facebook_ad_account_id?: string | null;
          facebook_page_id?: string | null;
          vapi_api_key?: string | null;
          vapi_assistant_id?: string | null;
          timezone?: string;
          business_hours?: {
            start: string;
            end: string;
            days: string[];
          };
          created_at?: string;
          updated_at?: string;
          last_login_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          company_name?: string;
          phone?: string | null;
          role?: 'admin' | 'user';
          permissions?: string[];
          plan?: 'starter' | 'growth' | 'scale';
          subscription_status?: 'active' | 'cancelled' | 'past_due';
          facebook_access_token?: string | null;
          facebook_ad_account_id?: string | null;
          facebook_page_id?: string | null;
          vapi_api_key?: string | null;
          vapi_assistant_id?: string | null;
          timezone?: string;
          business_hours?: {
            start: string;
            end: string;
            days: string[];
          };
          created_at?: string;
          updated_at?: string;
          last_login_at?: string | null;
        };
      };
      service_types: {
        Row: {
          id: string;
          name: string;
          icon: string;
          color: string;
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          icon: string;
          color: string;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          icon?: string;
          color?: string;
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      pressure_services: {
        Row: {
          id: string;
          service_type_id: string;
          name: string;
          description: string;
          typical_pricing: string;
          season_preference: string;
          equipment_needed: string[];
          sort_order: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          service_type_id: string;
          name: string;
          description: string;
          typical_pricing: string;
          season_preference: string;
          equipment_needed: string[];
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          service_type_id?: string;
          name?: string;
          description?: string;
          typical_pricing?: string;
          season_preference?: string;
          equipment_needed?: string[];
          sort_order?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      ad_templates: {
        Row: {
          id: string;
          name: string;
          service_type_id: string;
          pressure_service_id: string;
          category: string;
          service: string;
          creative: {
            primary_text: string;
            headline: string;
            description: string;
            call_to_action: string;
            media_requirements: {
              before_after_photos: boolean;
              action_video: boolean;
              equipment_shots: boolean;
              image_specs: {
                width: number;
                height: number;
                format: string;
              };
            };
            image_path?: string;
          };
          targeting: {
            location_radius: string;
            age_range: {
              min: number;
              max: number;
            };
            home_value_range: string;
            interests: string[];
            exclude_competitors: boolean;
            custom_audiences: string[];
            lookalike_audiences: string[];
          };
          budget_range: {
            min: number;
            max: number;
            suggested: number;
          };
          seasonal_timing: string[];
          target_customer: string;
          pricing_strategy: string;
          performance: {
            ctr: string;
            cpl: string;
            conversions: number;
            total_spend: number;
            total_leads: number;
            last_updated: string;
          };
          template_type: 'global' | 'custom';
          is_public: boolean;
          parent_template_id: string | null;
          status: 'draft' | 'active' | 'paused' | 'archived';
          is_featured: boolean;
          created_at: string;
          updated_at: string;
          created_by: string;
        };
        Insert: {
          id?: string;
          name: string;
          service_type_id: string;
          pressure_service_id: string;
          category: string;
          service: string;
          creative: {
            primary_text: string;
            headline: string;
            description: string;
            call_to_action: string;
            media_requirements: {
              before_after_photos: boolean;
              action_video: boolean;
              equipment_shots: boolean;
              image_specs: {
                width: number;
                height: number;
                format: string;
              };
            };
            image_path?: string;
          };
          targeting: {
            location_radius: string;
            age_range: {
              min: number;
              max: number;
            };
            home_value_range: string;
            interests: string[];
            exclude_competitors: boolean;
            custom_audiences: string[];
            lookalike_audiences: string[];
          };
          budget_range: {
            min: number;
            max: number;
            suggested: number;
          };
          seasonal_timing: string[];
          target_customer: string;
          pricing_strategy: string;
          performance: {
            ctr: string;
            cpl: string;
            conversions: number;
            total_spend: number;
            total_leads: number;
            last_updated: string;
          };
          template_type?: 'global' | 'custom';
          is_public?: boolean;
          parent_template_id?: string | null;
          status?: 'draft' | 'active' | 'paused' | 'archived';
          is_featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by: string;
        };
        Update: {
          id?: string;
          name?: string;
          service_type_id?: string;
          pressure_service_id?: string;
          category?: string;
          service?: string;
          creative?: {
            primary_text: string;
            headline: string;
            description: string;
            call_to_action: string;
            media_requirements: {
              before_after_photos: boolean;
              action_video: boolean;
              equipment_shots: boolean;
              image_specs: {
                width: number;
                height: number;
                format: string;
              };
            };
            image_path?: string;
          };
          targeting?: {
            location_radius: string;
            age_range: {
              min: number;
              max: number;
            };
            home_value_range: string;
            interests: string[];
            exclude_competitors: boolean;
            custom_audiences: string[];
            lookalike_audiences: string[];
          };
          budget_range?: {
            min: number;
            max: number;
            suggested: number;
          };
          seasonal_timing?: string[];
          target_customer?: string;
          pricing_strategy?: string;
          performance?: {
            ctr: string;
            cpl: string;
            conversions: number;
            total_spend: number;
            total_leads: number;
            last_updated: string;
          };
          template_type?: 'global' | 'custom';
          is_public?: boolean;
          parent_template_id?: string | null;
          status?: 'draft' | 'active' | 'paused' | 'archived';
          is_featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
        };
      };
      campaigns: {
        Row: {
          id: string;
          template_id: string;
          name: string;
          facebook_campaign_id: string | null;
          budget: number;
          start_date: string;
          end_date: string | null;
          custom_creative: any | null;
          custom_targeting: any | null;
          metrics: {
            impressions: number;
            clicks: number;
            ctr: number;
            cpc: number;
            cpl: number;
            leads_generated: number;
            spend: number;
            last_sync: string;
          };
          status: 'draft' | 'active' | 'paused' | 'completed' | 'error';
          created_at: string;
          updated_at: string;
          launched_at: string | null;
          created_by: string;
        };
        Insert: {
          id?: string;
          template_id: string;
          name: string;
          facebook_campaign_id?: string | null;
          budget: number;
          start_date: string;
          end_date?: string | null;
          custom_creative?: any | null;
          custom_targeting?: any | null;
          metrics: {
            impressions: number;
            clicks: number;
            ctr: number;
            cpc: number;
            cpl: number;
            leads_generated: number;
            spend: number;
            last_sync: string;
          };
          status?: 'draft' | 'active' | 'paused' | 'completed' | 'error';
          created_at?: string;
          updated_at?: string;
          launched_at?: string | null;
          created_by: string;
        };
        Update: {
          id?: string;
          template_id?: string;
          name?: string;
          facebook_campaign_id?: string | null;
          budget?: number;
          start_date?: string;
          end_date?: string | null;
          custom_creative?: any | null;
          custom_targeting?: any | null;
          metrics?: {
            impressions: number;
            clicks: number;
            ctr: number;
            cpc: number;
            cpl: number;
            leads_generated: number;
            spend: number;
            last_sync: string;
          };
          status?: 'draft' | 'active' | 'paused' | 'completed' | 'error';
          created_at?: string;
          updated_at?: string;
          launched_at?: string | null;
          created_by?: string;
        };
      };
      leads: {
        Row: {
          id: string;
          campaign_id: string;
          source: 'facebook' | 'google' | 'website' | 'referral';
          name: string;
          phone: string;
          email: string | null;
          address: string | null;
          service_interest: string;
          budget_range: string | null;
          urgency: 'asap' | 'this_week' | 'this_month' | 'next_month' | 'just_browsing';
          property_type: 'residential' | 'commercial';
          score: number;
          quality: 'hot' | 'warm' | 'cold';
          vapi_call_id: string | null;
          call_status: 'pending' | 'calling' | 'completed' | 'failed' | 'no_answer';
          call_attempts: number;
          last_call_at: string | null;
          next_call_at: string | null;
          appointment_scheduled: boolean;
          appointment_date: string | null;
          appointment_notes: string | null;
          status: 'new' | 'contacted' | 'qualified' | 'appointment' | 'converted' | 'lost';
          created_at: string;
          updated_at: string;
          last_contacted_at: string | null;
          notes: string;
          created_by: string;
        };
        Insert: {
          id?: string;
          campaign_id: string;
          source: 'facebook' | 'google' | 'website' | 'referral';
          name: string;
          phone: string;
          email?: string | null;
          address?: string | null;
          service_interest: string;
          budget_range?: string | null;
          urgency: 'asap' | 'this_week' | 'this_month' | 'next_month' | 'just_browsing';
          property_type: 'residential' | 'commercial';
          score?: number;
          quality?: 'hot' | 'warm' | 'cold';
          vapi_call_id?: string | null;
          call_status?: 'pending' | 'calling' | 'completed' | 'failed' | 'no_answer';
          call_attempts?: number;
          last_call_at?: string | null;
          next_call_at?: string | null;
          appointment_scheduled?: boolean;
          appointment_date?: string | null;
          appointment_notes?: string | null;
          status?: 'new' | 'contacted' | 'qualified' | 'appointment' | 'converted' | 'lost';
          created_at?: string;
          updated_at?: string;
          last_contacted_at?: string | null;
          notes?: string;
          created_by: string;
        };
        Update: {
          id?: string;
          campaign_id?: string;
          source?: 'facebook' | 'google' | 'website' | 'referral';
          name?: string;
          phone?: string;
          email?: string | null;
          address?: string | null;
          service_interest?: string;
          budget_range?: string | null;
          urgency?: 'asap' | 'this_week' | 'this_month' | 'next_month' | 'just_browsing';
          property_type?: 'residential' | 'commercial';
          score?: number;
          quality?: 'hot' | 'warm' | 'cold';
          vapi_call_id?: string | null;
          call_status?: 'pending' | 'calling' | 'completed' | 'failed' | 'no_answer';
          call_attempts?: number;
          last_call_at?: string | null;
          next_call_at?: string | null;
          appointment_scheduled?: boolean;
          appointment_date?: string | null;
          appointment_notes?: string | null;
          status?: 'new' | 'contacted' | 'qualified' | 'appointment' | 'converted' | 'lost';
          created_at?: string;
          updated_at?: string;
          last_contacted_at?: string | null;
          notes?: string;
          created_by?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
