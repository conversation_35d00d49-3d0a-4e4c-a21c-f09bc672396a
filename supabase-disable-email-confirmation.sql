-- Disable email confirmation for development
-- This allows users to sign up and login immediately without confirming their email

-- Update auth configuration to disable email confirmation
-- Note: This should be done through the Supabase dashboard, but here's the SQL equivalent

-- For existing users who might be stuck in unconfirmed state, confirm them
UPDATE auth.users 
SET email_confirmed_at = NOW(), 
    confirmed_at = NOW()
WHERE email_confirmed_at IS NULL;

-- Create a function to auto-confirm new users (alternative approach)
CREATE OR REPLACE FUNCTION public.auto_confirm_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Auto-confirm the user
    NEW.email_confirmed_at = NOW();
    NEW.confirmed_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-confirm users on signup (if needed)
-- Note: This is a backup approach - prefer disabling email confirmation in dashboard
DROP TRIGGER IF EXISTS auto_confirm_user_trigger ON auth.users;
CREATE TRIGGER auto_confirm_user_trigger
    BEFORE INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.auto_confirm_user();
