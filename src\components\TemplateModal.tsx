import React, { useState, useEffect } from 'react';
import { X, Save, Eye } from 'lucide-react';
import { AdTemplate, ServiceType, PressureService } from '../types/database';
import { db } from '../services/database';

interface TemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  template?: AdTemplate | null;
  onSave: (template: AdTemplate) => void;
}

export const TemplateModal: React.FC<TemplateModalProps> = ({
  isOpen,
  onClose,
  template,
  onSave
}) => {
  const [serviceTypes, setServiceTypes] = useState<ServiceType[]>([]);
  const [pressureServices, setPressureServices] = useState<PressureService[]>([]);
  const [filteredServices, setFilteredServices] = useState<PressureService[]>([]);
  
  const [formData, setFormData] = useState({
    name: '',
    service_type_id: '',
    pressure_service_id: '',
    category: '',
    service: '',
    creative: {
      primary_text: '',
      headline: '',
      description: '',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      }
    },
    targeting: {
      location_radius: '15_miles',
      age_range: { min: 35, max: 65 },
      home_value_range: '$200k_plus',
      interests: ['home_improvement'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 50, max: 150, suggested: 75 },
    seasonal_timing: ['spring'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    status: 'draft' as const
  });

  useEffect(() => {
    const loadData = async () => {
      const [types, services] = await Promise.all([
        db.getServiceTypes(),
        db.getPressureServices()
      ]);
      setServiceTypes(types);
      setPressureServices(services);
    };
    loadData();
  }, []);

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        service_type_id: template.service_type_id,
        pressure_service_id: template.pressure_service_id,
        category: template.category,
        service: template.service,
        creative: template.creative,
        targeting: template.targeting,
        budget_range: template.budget_range,
        seasonal_timing: template.seasonal_timing,
        target_customer: template.target_customer,
        pricing_strategy: template.pricing_strategy,
        status: template.status
      });
    }
  }, [template]);

  useEffect(() => {
    if (formData.service_type_id) {
      const filtered = pressureServices.filter(
        service => service.service_type_id === formData.service_type_id
      );
      setFilteredServices(filtered);
      
      // Update category based on service type
      const serviceType = serviceTypes.find(st => st.id === formData.service_type_id);
      if (serviceType) {
        setFormData(prev => ({ ...prev, category: serviceType.name }));
      }
    }
  }, [formData.service_type_id, pressureServices, serviceTypes]);

  useEffect(() => {
    if (formData.pressure_service_id) {
      const service = pressureServices.find(ps => ps.id === formData.pressure_service_id);
      if (service) {
        setFormData(prev => ({ ...prev, service: service.name }));
      }
    }
  }, [formData.pressure_service_id, pressureServices]);

  const handleSave = async () => {
    try {
      if (template) {
        // Update existing template
        const updated = await db.updateAdTemplate(template.id, {
          ...formData,
          updated_at: new Date()
        });
        if (updated) onSave(updated);
      } else {
        // Create new template
        const newTemplate = await db.createAdTemplate({
          ...formData,
          performance: {
            ctr: '0%',
            cpl: '$0',
            conversions: 0,
            total_spend: 0,
            total_leads: 0,
            last_updated: new Date()
          },
          is_featured: false,
          created_by: 'user-1'
        });
        onSave(newTemplate);
      }
      onClose();
    } catch (error) {
      console.error('Error saving template:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-black border border-cyan-500/30 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">
              {template ? 'Edit Template' : 'Create New Template'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <X size={24} />
            </button>
          </div>

          {/* Form */}
          <div className="space-y-6">
            {/* Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Template Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="e.g., House Washing Spring Special"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Service Type
                </label>
                <select
                  value={formData.service_type_id}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    service_type_id: e.target.value,
                    pressure_service_id: '' // Reset service when type changes
                  }))}
                  className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                >
                  <option value="">Select Service Type</option>
                  {serviceTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
              </div>
            </div>

            {formData.service_type_id && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Specific Service
                </label>
                <select
                  value={formData.pressure_service_id}
                  onChange={(e) => setFormData(prev => ({ ...prev, pressure_service_id: e.target.value }))}
                  className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                >
                  <option value="">Select Service</option>
                  {filteredServices.map(service => (
                    <option key={service.id} value={service.id}>{service.name}</option>
                  ))}
                </select>
              </div>
            )}

            {/* Ad Creative */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-semibold text-white mb-4">Ad Creative</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Headline
                  </label>
                  <input
                    type="text"
                    value={formData.creative.headline}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      creative: { ...prev.creative, headline: e.target.value }
                    }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="e.g., Transform Your Home's Curb Appeal"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Primary Text
                  </label>
                  <textarea
                    value={formData.creative.primary_text}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      creative: { ...prev.creative, primary_text: e.target.value }
                    }))}
                    rows={3}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="Main ad copy that will appear in the Facebook ad..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Description
                  </label>
                  <input
                    type="text"
                    value={formData.creative.description}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      creative: { ...prev.creative, description: e.target.value }
                    }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="Short description for the ad"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Call to Action
                  </label>
                  <select
                    value={formData.creative.call_to_action}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      creative: { ...prev.creative, call_to_action: e.target.value }
                    }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="Get Free Estimate">Get Free Estimate</option>
                    <option value="Book Now">Book Now</option>
                    <option value="Request Quote">Request Quote</option>
                    <option value="Learn More">Learn More</option>
                    <option value="Contact Us">Contact Us</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Budget */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-semibold text-white mb-4">Budget Range</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Min Daily Budget
                  </label>
                  <input
                    type="number"
                    value={formData.budget_range.min}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      budget_range: { ...prev.budget_range, min: parseInt(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Max Daily Budget
                  </label>
                  <input
                    type="number"
                    value={formData.budget_range.max}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      budget_range: { ...prev.budget_range, max: parseInt(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Suggested Budget
                  </label>
                  <input
                    type="number"
                    value={formData.budget_range.suggested}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      budget_range: { ...prev.budget_range, suggested: parseInt(e.target.value) || 0 }
                    }))}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-700">
              <button
                onClick={onClose}
                className="px-6 py-2 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
              >
                <Save size={16} />
                <span>{template ? 'Update' : 'Create'} Template</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
