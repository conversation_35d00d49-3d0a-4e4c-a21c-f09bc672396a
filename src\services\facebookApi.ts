/**
 * Facebook Marketing API Service
 * Handles communication with the Python backend for Facebook API operations
 */

// Configuration
const CONFIG = {
  API_BASE_URL: 'http://localhost:5000',
  FACEBOOK_APP_ID: '***************',
  DEFAULT_DAILY_BUDGET: 50,
  DEFAULT_RADIUS_MILES: 25
};

const API_BASE_URL = CONFIG.API_BASE_URL;

export interface FacebookAccount {
  account_id: string;
  name: string;
  account_status: number;
  currency: string;
  timezone_name: string;
  amount_spent: string;
  balance: string;
}

export interface CampaignData {
  account_id: string;
  name: string;
  objective?: string;
  status?: string;
  daily_budget?: number;
  special_ad_categories?: string[];
}

export interface AdSetData {
  campaign_id: string;
  name: string;
  daily_budget: number;
  targeting: any;
  optimization_goal?: string;
  billing_event?: string;
  status?: string;
  start_time?: string;
  end_time?: string;
}

export interface CreativeData {
  account_id: string;
  name: string;
  page_id: string;
  message: string;
  headline?: string;
  description?: string;
  link: string;
  call_to_action?: string;
  image_hash?: string;
}

export interface AdData {
  adset_id: string;
  creative_id: string;
  name: string;
  status?: string;
}

export interface LaunchCampaignData {
  account_id: string;
  template: any;
  targeting: any;
  budget: {
    daily_budget: number;
  };
  page_id?: string;
  landing_page_url?: string;
}

export interface CampaignInsights {
  campaign_name: string;
  impressions: string;
  clicks: string;
  spend: string;
  cpm: string;
  cpc: string;
  ctr: string;
  actions?: any[];
  cost_per_action_type?: any[];
}

class FacebookApiService {
  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${API_BASE_URL}${endpoint}`;

    // Get auth token from localStorage
    const token = localStorage.getItem('pressuremax_token');

    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
      },
    };

    const response = await fetch(url, { ...defaultOptions, ...options });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * Get all Facebook ad accounts for the authenticated user
   */
  async getAdAccounts(): Promise<FacebookAccount[]> {
    const response = await this.makeRequest('/api/facebook/accounts');
    return response.accounts;
  }

  /**
   * Create a new Facebook campaign
   */
  async createCampaign(campaignData: CampaignData): Promise<any> {
    const response = await this.makeRequest('/api/facebook/campaign', {
      method: 'POST',
      body: JSON.stringify(campaignData),
    });
    return response.campaign;
  }

  /**
   * Create a new ad set
   */
  async createAdSet(adSetData: AdSetData): Promise<any> {
    const response = await this.makeRequest('/api/facebook/adset', {
      method: 'POST',
      body: JSON.stringify(adSetData),
    });
    return response.adset;
  }

  /**
   * Upload an image for use in ads
   */
  async uploadImage(accountId: string, imagePath: string): Promise<any> {
    const response = await this.makeRequest('/api/facebook/upload-image', {
      method: 'POST',
      body: JSON.stringify({
        account_id: accountId,
        image_path: imagePath,
      }),
    });
    return response.image;
  }

  /**
   * Create an ad creative
   */
  async createAdCreative(creativeData: CreativeData): Promise<any> {
    const response = await this.makeRequest('/api/facebook/creative', {
      method: 'POST',
      body: JSON.stringify(creativeData),
    });
    return response.creative;
  }

  /**
   * Create an ad
   */
  async createAd(adData: AdData): Promise<any> {
    const response = await this.makeRequest('/api/facebook/ad', {
      method: 'POST',
      body: JSON.stringify(adData),
    });
    return response.ad;
  }

  /**
   * Get campaign performance insights
   */
  async getCampaignInsights(
    campaignId: string,
    startDate: string,
    endDate: string
  ): Promise<CampaignInsights[]> {
    const response = await this.makeRequest(
      `/api/facebook/campaign/${campaignId}/insights?start_date=${startDate}&end_date=${endDate}`
    );
    return response.insights;
  }

  /**
   * Pause a campaign
   */
  async pauseCampaign(campaignId: string): Promise<any> {
    const response = await this.makeRequest(`/api/facebook/campaign/${campaignId}/pause`, {
      method: 'POST',
    });
    return response;
  }

  /**
   * Resume a campaign
   */
  async resumeCampaign(campaignId: string): Promise<any> {
    const response = await this.makeRequest(`/api/facebook/campaign/${campaignId}/resume`, {
      method: 'POST',
    });
    return response;
  }

  /**
   * Launch a complete campaign from a PressureMax template
   * This is the main function that creates campaign, ad set, creative, and ad in one go
   */
  async launchCampaignFromTemplate(data: LaunchCampaignData): Promise<any> {
    const response = await this.makeRequest('/api/facebook/launch-campaign', {
      method: 'POST',
      body: JSON.stringify(data),
    });
    return response;
  }

  /**
   * Check if the Facebook API backend is healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.makeRequest('/health');
      return response.status === 'healthy';
    } catch (error) {
      console.error('Facebook API health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const facebookApi = new FacebookApiService();

// Export targeting helpers
export const FacebookTargeting = {
  /**
   * Create basic location targeting for pressure washing businesses
   */
  createLocationTargeting(latitude: number, longitude: number, radiusMiles: number = 25) {
    return {
      geo_locations: {
        custom_locations: [
          {
            latitude,
            longitude,
            radius: radiusMiles,
            distance_unit: 'mile',
          },
        ],
      },
      age_min: 25,
      age_max: 65,
      genders: [1, 2], // All genders
    };
  },

  /**
   * Create homeowner targeting for residential services
   */
  createHomeownerTargeting(location: any) {
    return {
      ...location,
      interests: [
        { id: '6003107902433', name: 'Home improvement' },
        { id: '6003056986033', name: 'Home and garden' },
        { id: '6003348617755', name: 'Real estate' },
      ],
      behaviors: [
        { id: '6002714895372', name: 'Homeowners' },
      ],
    };
  },

  /**
   * Create business owner targeting for commercial services
   */
  createBusinessTargeting(location: any) {
    return {
      ...location,
      interests: [
        { id: '6003195797498', name: 'Business' },
        { id: '6003020834693', name: 'Small business' },
        { id: '6003348617755', name: 'Real estate' },
      ],
      behaviors: [
        { id: '6002714898572', name: 'Business decision makers' },
      ],
    };
  },
};
