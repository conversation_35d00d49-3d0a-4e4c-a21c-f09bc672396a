// Quick test to verify templates are loading
import { db } from './src/services/database.js';

async function testTemplates() {
  try {
    console.log('Testing template loading...');
    const templates = await db.getAdTemplates();
    console.log(`✅ Successfully loaded ${templates.length} templates`);
    
    // Show first few templates
    templates.slice(0, 3).forEach((template, index) => {
      console.log(`${index + 1}. ${template.name} (${template.category})`);
    });
    
    return templates;
  } catch (error) {
    console.error('❌ Error loading templates:', error);
    return [];
  }
}

testTemplates();
