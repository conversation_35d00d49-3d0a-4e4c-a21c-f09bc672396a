import React, { useState } from 'react';
import { Phone, Mail, MapPin, CheckCircle, AlertCircle, Shield } from 'lucide-react';
import { db } from '../services/database';

interface LeadCaptureFormProps {
  campaignId?: string;
  source?: 'facebook' | 'google' | 'website' | 'referral';
  onSuccess?: (leadId: string) => void;
  className?: string;
}

export const LeadCaptureForm: React.FC<LeadCaptureFormProps> = ({
  campaignId = 'campaign-default',
  source = 'website',
  onSuccess,
  className = ''
}) => {
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    serviceInterest: '',
    budgetRange: '',
    urgency: 'this_month' as const,
    propertyType: 'residential' as const,
    tcpaConsent: false,
    marketingConsent: false,
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  const serviceOptions = [
    'House Washing',
    'Driveway Cleaning',
    'Deck Restoration',
    'Roof Cleaning',
    'Commercial Building Washing',
    'Parking Lot Cleaning',
    'Other'
  ];

  const budgetOptions = [
    'Under $200',
    '$200 - $500',
    '$500 - $1,000',
    '$1,000 - $2,000',
    'Over $2,000',
    'Not sure'
  ];

  const urgencyOptions = [
    { value: 'asap', label: 'ASAP' },
    { value: 'this_week', label: 'This Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'next_month', label: 'Next Month' },
    { value: 'just_browsing', label: 'Just Browsing' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.tcpaConsent) {
      setErrorMessage('You must consent to receive calls and texts to proceed.');
      return;
    }

    if (!formData.name || !formData.phone) {
      setErrorMessage('Name and phone number are required.');
      return;
    }

    setIsSubmitting(true);
    setErrorMessage('');

    try {
      // Create lead in database
      const lead = await db.createLead({
        campaign_id: campaignId,
        source,
        name: formData.name,
        phone: formData.phone,
        email: formData.email || undefined,
        address: formData.address || undefined,
        service_interest: formData.serviceInterest,
        budget_range: formData.budgetRange || undefined,
        urgency: formData.urgency,
        property_type: formData.propertyType,
        score: calculateLeadScore(formData),
        quality: determineLeadQuality(formData),
        call_status: 'pending',
        call_attempts: 0,
        appointment_scheduled: false,
        status: 'new',
        notes: formData.notes
      });

      setSubmitStatus('success');
      
      // Trigger VAPI call (would be implemented in VAPI integration)
      console.log('Lead created, triggering VAPI call:', lead.id);
      
      if (onSuccess) {
        onSuccess(lead.id);
      }

      // Reset form after success
      setTimeout(() => {
        setFormData({
          name: '',
          phone: '',
          email: '',
          address: '',
          serviceInterest: '',
          budgetRange: '',
          urgency: 'this_month',
          propertyType: 'residential',
          tcpaConsent: false,
          marketingConsent: false,
          notes: ''
        });
        setSubmitStatus('idle');
      }, 3000);

    } catch (error) {
      console.error('Error submitting lead:', error);
      setSubmitStatus('error');
      setErrorMessage('Failed to submit your information. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateLeadScore = (data: typeof formData): number => {
    let score = 50; // Base score
    
    // Urgency scoring
    if (data.urgency === 'asap') score += 30;
    else if (data.urgency === 'this_week') score += 20;
    else if (data.urgency === 'this_month') score += 10;
    else if (data.urgency === 'just_browsing') score -= 20;
    
    // Budget scoring
    if (data.budgetRange === 'Over $2,000') score += 25;
    else if (data.budgetRange === '$1,000 - $2,000') score += 15;
    else if (data.budgetRange === '$500 - $1,000') score += 10;
    else if (data.budgetRange === 'Under $200') score -= 10;
    
    // Contact info completeness
    if (data.email) score += 10;
    if (data.address) score += 10;
    
    // Property type
    if (data.propertyType === 'commercial') score += 15;
    
    return Math.max(0, Math.min(100, score));
  };

  const determineLeadQuality = (data: typeof formData): 'hot' | 'warm' | 'cold' => {
    const score = calculateLeadScore(data);
    if (score >= 80) return 'hot';
    if (score >= 60) return 'warm';
    return 'cold';
  };

  if (submitStatus === 'success') {
    return (
      <div className={`bg-green-500/10 border border-green-500/30 p-8 text-center ${className}`}>
        <CheckCircle className="text-green-400 mx-auto mb-4" size={48} />
        <h3 className="text-xl font-bold text-white mb-2">Thank You!</h3>
        <p className="text-gray-300 mb-4">
          We've received your information and will call you within 5 minutes to discuss your project.
        </p>
        <div className="text-sm text-gray-400">
          <Shield className="inline mr-2" size={16} />
          Your information is secure and will only be used to contact you about your pressure washing needs.
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className={`space-y-6 ${className}`}>
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-white mb-2">Get Your Free Estimate</h3>
        <p className="text-gray-300">Fill out the form below and we'll call you within 5 minutes!</p>
      </div>

      {/* Contact Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Full Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
            placeholder="Your full name"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Phone Number *
          </label>
          <div className="relative">
            <Phone className="absolute left-3 top-2.5 text-gray-400" size={16} />
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              className="w-full bg-gray-900 border border-gray-700 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
              placeholder="(*************"
              required
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Email Address
          </label>
          <div className="relative">
            <Mail className="absolute left-3 top-2.5 text-gray-400" size={16} />
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              className="w-full bg-gray-900 border border-gray-700 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
              placeholder="<EMAIL>"
            />
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Property Address
          </label>
          <div className="relative">
            <MapPin className="absolute left-3 top-2.5 text-gray-400" size={16} />
            <input
              type="text"
              value={formData.address}
              onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
              className="w-full bg-gray-900 border border-gray-700 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
              placeholder="123 Main St, City, State"
            />
          </div>
        </div>
      </div>

      {/* Service Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Service Needed
          </label>
          <select
            value={formData.serviceInterest}
            onChange={(e) => setFormData(prev => ({ ...prev, serviceInterest: e.target.value }))}
            className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
          >
            <option value="">Select a service</option>
            {serviceOptions.map(service => (
              <option key={service} value={service}>{service}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Budget Range
          </label>
          <select
            value={formData.budgetRange}
            onChange={(e) => setFormData(prev => ({ ...prev, budgetRange: e.target.value }))}
            className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
          >
            <option value="">Select budget range</option>
            {budgetOptions.map(budget => (
              <option key={budget} value={budget}>{budget}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            When do you need this done?
          </label>
          <select
            value={formData.urgency}
            onChange={(e) => setFormData(prev => ({ ...prev, urgency: e.target.value as any }))}
            className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
          >
            {urgencyOptions.map(option => (
              <option key={option.value} value={option.value}>{option.label}</option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Property Type
          </label>
          <select
            value={formData.propertyType}
            onChange={(e) => setFormData(prev => ({ ...prev, propertyType: e.target.value as any }))}
            className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
          >
            <option value="residential">Residential</option>
            <option value="commercial">Commercial</option>
          </select>
        </div>
      </div>

      {/* TCPA Compliance */}
      <div className="space-y-4 bg-gray-900 border border-gray-700 p-4">
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="tcpaConsent"
            checked={formData.tcpaConsent}
            onChange={(e) => setFormData(prev => ({ ...prev, tcpaConsent: e.target.checked }))}
            className="mt-1 text-cyan-500 focus:ring-cyan-500"
            required
          />
          <label htmlFor="tcpaConsent" className="text-sm text-gray-300">
            <span className="text-red-400">*</span> I consent to receive calls and text messages from this business at the phone number provided above. 
            I understand that these communications may be made using an automatic telephone dialing system and may include marketing messages. 
            Message and data rates may apply. I can opt out at any time by replying STOP.
          </label>
        </div>
        
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="marketingConsent"
            checked={formData.marketingConsent}
            onChange={(e) => setFormData(prev => ({ ...prev, marketingConsent: e.target.checked }))}
            className="mt-1 text-cyan-500 focus:ring-cyan-500"
          />
          <label htmlFor="marketingConsent" className="text-sm text-gray-300">
            I would like to receive marketing emails and promotional offers (optional).
          </label>
        </div>
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 border border-red-500/30 p-3">
          <AlertCircle size={16} />
          <span className="text-sm">{errorMessage}</span>
        </div>
      )}

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting || !formData.tcpaConsent}
        className="w-full bg-gradient-to-r from-cyan-500 to-cyan-600 text-black py-3 px-6 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting ? 'Submitting...' : 'Get My Free Estimate - Call Me Now!'}
      </button>

      <div className="text-center text-xs text-gray-500">
        <Shield className="inline mr-1" size={12} />
        Your information is secure and protected. We respect your privacy.
      </div>
    </form>
  );
};
