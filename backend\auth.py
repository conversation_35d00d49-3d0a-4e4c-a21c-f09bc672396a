"""
Authentication module for PressureMax
Handles user authentication, JWT tokens, and password management
"""

import jwt
import bcrypt
import secrets
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON>ipart
from flask import current_app
import os
from typing import Dict, Optional, Tuple

# Mock user database (replace with real database in production)
users_db = {}
password_reset_tokens = {}

class AuthService:
    def __init__(self):
        self.secret_key = os.getenv('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
        self.algorithm = 'HS256'
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 30
        
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def create_access_token(self, user_id: str) -> str:
        """Create a JWT access token"""
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes),
            'iat': datetime.utcnow(),
            'type': 'access'
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create a JWT refresh token"""
        payload = {
            'user_id': user_id,
            'exp': datetime.utcnow() + timedelta(days=self.refresh_token_expire_days),
            'iat': datetime.utcnow(),
            'type': 'refresh'
        }
        return jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Optional[Dict]:
        """Verify and decode a JWT token"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def create_user(self, user_data: Dict) -> Dict:
        """Create a new user"""
        user_id = f"user_{len(users_db) + 1}"
        
        # Hash the password
        hashed_password = self.hash_password(user_data['password'])
        
        # Create user record
        user = {
            'id': user_id,
            'email': user_data['email'],
            'name': user_data['name'],
            'company_name': user_data['company_name'],
            'phone': user_data.get('phone', ''),
            'plan': 'starter',  # Default plan
            'subscription_status': 'active',
            'password_hash': hashed_password,
            'timezone': user_data.get('timezone', 'UTC'),
            'business_hours': user_data.get('business_hours', {
                'start': '09:00',
                'end': '17:00',
                'days': ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
            }),
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat(),
            'last_login_at': None,
            'email_verified': False,
            'facebook_access_token': None,
            'facebook_ad_account_id': None,
            'facebook_page_id': None,
            'vapi_api_key': None,
            'vapi_assistant_id': None
        }
        
        # Store user (in production, save to database)
        users_db[user_id] = user
        
        # Return user without password hash
        return self.get_user_safe(user)
    
    def authenticate_user(self, email: str, password: str) -> Optional[Dict]:
        """Authenticate a user with email and password"""
        print(f"Auth: Looking for user with email: {email}")
        print(f"Auth: Current users in database: {list(users_db.keys())}")
        print(f"Auth: User emails in database: {[user['email'] for user in users_db.values()]}")

        # Find user by email
        user = None
        for user_data in users_db.values():
            if user_data['email'].lower() == email.lower():
                user = user_data
                break

        if not user:
            print(f"Auth: No user found with email: {email}")
            return None

        print(f"Auth: Found user: {user['email']}")

        # Verify password
        if not self.verify_password(password, user['password_hash']):
            print(f"Auth: Password verification failed for user: {email}")
            return None

        print(f"Auth: Password verification successful for user: {email}")

        # Update last login
        user['last_login_at'] = datetime.utcnow().isoformat()

        return self.get_user_safe(user)
    
    def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """Get user by ID"""
        user = users_db.get(user_id)
        return self.get_user_safe(user) if user else None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user by email"""
        for user_data in users_db.values():
            if user_data['email'].lower() == email.lower():
                return self.get_user_safe(user_data)
        return None
    
    def update_user(self, user_id: str, updates: Dict) -> Optional[Dict]:
        """Update user profile"""
        user = users_db.get(user_id)
        if not user:
            return None
        
        # Update allowed fields
        allowed_fields = [
            'name', 'company_name', 'phone', 'timezone', 'business_hours',
            'facebook_access_token', 'facebook_ad_account_id', 'facebook_page_id',
            'vapi_api_key', 'vapi_assistant_id'
        ]
        
        for field in allowed_fields:
            if field in updates:
                user[field] = updates[field]
        
        user['updated_at'] = datetime.utcnow().isoformat()
        
        return self.get_user_safe(user)
    
    def get_user_safe(self, user: Dict) -> Dict:
        """Return user data without sensitive information"""
        if not user:
            return {}
        
        safe_user = user.copy()
        safe_user.pop('password_hash', None)
        return safe_user
    
    def email_exists(self, email: str) -> bool:
        """Check if email already exists"""
        return any(user['email'].lower() == email.lower() for user in users_db.values())
    
    def create_password_reset_token(self, email: str) -> Optional[str]:
        """Create a password reset token"""
        user = self.get_user_by_email(email)
        if not user:
            return None
        
        # Generate secure token
        token = secrets.token_urlsafe(32)
        
        # Store token with expiration (1 hour)
        password_reset_tokens[token] = {
            'user_id': user['id'],
            'email': email,
            'expires_at': datetime.utcnow() + timedelta(hours=1)
        }
        
        return token
    
    def verify_password_reset_token(self, token: str) -> Optional[Dict]:
        """Verify a password reset token"""
        token_data = password_reset_tokens.get(token)
        if not token_data:
            return None
        
        # Check if token is expired
        if datetime.utcnow() > token_data['expires_at']:
            del password_reset_tokens[token]
            return None
        
        return token_data
    
    def reset_password(self, token: str, new_password: str) -> bool:
        """Reset password using token"""
        token_data = self.verify_password_reset_token(token)
        if not token_data:
            return False
        
        # Update password
        user = users_db.get(token_data['user_id'])
        if not user:
            return False
        
        user['password_hash'] = self.hash_password(new_password)
        user['updated_at'] = datetime.utcnow().isoformat()
        
        # Remove used token
        del password_reset_tokens[token]
        
        return True
    
    def send_password_reset_email(self, email: str, token: str) -> bool:
        """Send password reset email (mock implementation)"""
        # In production, implement actual email sending
        reset_url = f"http://localhost:3000/reset-password?token={token}"
        
        print(f"Password reset email would be sent to {email}")
        print(f"Reset URL: {reset_url}")
        
        # For now, just return True (email "sent")
        return True

# Global auth service instance
auth_service = AuthService()

def get_auth_service():
    """Get the auth service instance"""
    return auth_service
