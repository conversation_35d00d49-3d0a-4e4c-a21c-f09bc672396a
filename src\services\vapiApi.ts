// VAPI API endpoints for appointment booking integration
import { Lead } from '../types/database';
import { calendarService, TimeSlot } from './calendar';
import { db } from './database';

export interface VAPIAvailabilityRequest {
  preferredDate?: string;
  serviceType: string;
  urgency?: 'asap' | 'this_week' | 'this_month' | 'flexible';
}

export interface VAPIAvailabilityResponse {
  availableSlots: Array<{
    date: string;
    time: string;
    duration: number;
    displayText: string;
  }>;
  message: string;
}

export interface VAPIBookingRequest {
  customerName: string;
  customerPhone: string;
  appointmentDate: string;
  appointmentTime: string;
  serviceType: string;
  leadId?: string;
}

export interface VAPIBookingResponse {
  success: boolean;
  appointmentId?: string;
  confirmationNumber?: string;
  message: string;
  appointmentDetails?: {
    date: string;
    time: string;
    service: string;
    duration: number;
    location: string;
  };
}

export class VAPIApiService {
  // Check availability for appointments
  async checkAvailability(request: VAPIAvailabilityRequest): Promise<VAPIAvailabilityResponse> {
    try {
      const { preferredDate, serviceType, urgency } = request;
      
      // Determine date range based on urgency
      let startDate = new Date();
      let endDate = new Date();
      
      switch (urgency) {
        case 'asap':
          endDate.setDate(startDate.getDate() + 3); // Next 3 days
          break;
        case 'this_week':
          endDate.setDate(startDate.getDate() + 7); // Next week
          break;
        case 'this_month':
          endDate.setDate(startDate.getDate() + 30); // Next month
          break;
        default:
          endDate.setDate(startDate.getDate() + 14); // Default 2 weeks
      }

      // If preferred date is provided, focus around that date
      if (preferredDate) {
        const preferred = new Date(preferredDate);
        if (preferred > startDate) {
          startDate = preferred;
          endDate = new Date(preferred);
          endDate.setDate(preferred.getDate() + 7); // Week around preferred date
        }
      }

      // Get available slots from calendar service
      const slots = await calendarService.getAvailableSlots(startDate, endDate);
      
      // Format slots for VAPI response
      const availableSlots = slots.slice(0, 6).map(slot => ({
        date: slot.start.toISOString().split('T')[0],
        time: slot.start.toLocaleTimeString('en-US', { 
          hour: 'numeric', 
          minute: '2-digit',
          hour12: true 
        }),
        duration: 60, // Standard estimate duration
        displayText: `${slot.start.toLocaleDateString('en-US', { 
          weekday: 'long',
          month: 'short',
          day: 'numeric'
        })} at ${slot.start.toLocaleTimeString('en-US', { 
          hour: 'numeric', 
          minute: '2-digit',
          hour12: true 
        })}`
      }));

      let message = '';
      if (availableSlots.length === 0) {
        message = 'No available slots found for the requested timeframe. Let me check alternative dates.';
      } else if (urgency === 'asap') {
        message = `I found ${availableSlots.length} slots available in the next few days.`;
      } else {
        message = `I have ${availableSlots.length} appointment slots available.`;
      }

      return {
        availableSlots,
        message
      };

    } catch (error) {
      console.error('Error checking availability:', error);
      return {
        availableSlots: [],
        message: 'Sorry, I\'m having trouble checking availability right now. Let me transfer you to our scheduling team.'
      };
    }
  }

  // Book an appointment
  async bookAppointment(request: VAPIBookingRequest): Promise<VAPIBookingResponse> {
    try {
      const { customerName, customerPhone, appointmentDate, appointmentTime, serviceType, leadId } = request;

      // Parse appointment date and time
      const appointmentDateTime = new Date(`${appointmentDate}T${appointmentTime}`);
      
      // Find or create lead
      let lead: Lead | null = null;
      
      if (leadId) {
        lead = await db.getLeadById(leadId);
      }
      
      if (!lead) {
        // Search for existing lead by phone
        const leads = await db.getLeads();
        lead = leads.find(l => l.phone === customerPhone) || null;
      }

      if (!lead) {
        // Create new lead
        lead = await db.createLead({
          campaign_id: 'vapi-call-campaign',
          source: 'phone',
          name: customerName,
          phone: customerPhone,
          service_interest: serviceType,
          urgency: 'this_week',
          property_type: 'residential',
          score: 75,
          quality: 'warm',
          call_status: 'completed',
          call_attempts: 1,
          appointment_scheduled: false,
          status: 'contacted',
          notes: `Lead created via VAPI call. Interested in ${serviceType}.`
        });
      }

      // Book the appointment using calendar service
      const appointment = await calendarService.bookAppointment(
        lead,
        appointmentDateTime,
        serviceType,
        60 // 1 hour duration
      );

      // Update lead with appointment info
      await db.updateLead(lead.id, {
        appointment_scheduled: true,
        appointment_date: appointmentDateTime,
        appointment_notes: `${serviceType} - Booked via VAPI`,
        status: 'appointment',
        quality: 'hot' // Scheduled appointments are hot leads
      });

      // Generate confirmation number
      const confirmationNumber = `PM${Date.now().toString().slice(-6)}`;

      return {
        success: true,
        appointmentId: appointment.id,
        confirmationNumber,
        message: `Perfect! Your appointment is confirmed for ${appointmentDateTime.toLocaleDateString()} at ${appointmentDateTime.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}.`,
        appointmentDetails: {
          date: appointmentDateTime.toLocaleDateString('en-US', { 
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
          time: appointmentDateTime.toLocaleTimeString('en-US', { 
            hour: 'numeric', 
            minute: '2-digit',
            hour12: true 
          }),
          service: serviceType,
          duration: 60,
          location: lead.address || 'Customer Location'
        }
      };

    } catch (error) {
      console.error('Error booking appointment:', error);
      return {
        success: false,
        message: 'I\'m sorry, there was an issue booking your appointment. Let me transfer you to our scheduling team to complete this manually.'
      };
    }
  }

  // Get lead information for VAPI calls
  async getLeadInfo(phoneNumber: string): Promise<Lead | null> {
    try {
      const leads = await db.getLeads();
      return leads.find(lead => lead.phone === phoneNumber) || null;
    } catch (error) {
      console.error('Error getting lead info:', error);
      return null;
    }
  }

  // Update lead after call completion
  async updateLeadAfterCall(leadId: string, callData: {
    callId: string;
    duration?: number;
    transcript?: string;
    appointmentBooked?: boolean;
    sentiment?: 'positive' | 'neutral' | 'negative';
  }): Promise<void> {
    try {
      const updates: Partial<Lead> = {
        call_status: 'completed',
        last_call_at: new Date(),
        vapi_call_id: callData.callId
      };

      if (callData.transcript) {
        updates.notes = `${updates.notes || ''}\n\nCall transcript: ${callData.transcript}`;
      }

      if (callData.appointmentBooked) {
        updates.status = 'appointment';
        updates.quality = 'hot';
      } else if (callData.sentiment === 'positive') {
        updates.status = 'contacted';
        updates.quality = 'warm';
      } else if (callData.sentiment === 'negative') {
        updates.status = 'qualified';
        updates.quality = 'cold';
      }

      await db.updateLead(leadId, updates);
    } catch (error) {
      console.error('Error updating lead after call:', error);
    }
  }
}

// Export singleton instance
export const vapiApiService = new VAPIApiService();
