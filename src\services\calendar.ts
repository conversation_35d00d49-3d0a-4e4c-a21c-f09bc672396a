// Calendar Integration Service for Appointment Booking
import { Lead } from '../types/database';

export interface CalendarEvent {
  id: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  attendees: {
    email: string;
    name: string;
    status: 'pending' | 'accepted' | 'declined';
  }[];
  leadId?: string;
  serviceType?: string;
  estimatedDuration: number; // minutes
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no_show';
}

export interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
  reason?: string; // If not available
}

export interface CalendarProvider {
  id: string;
  name: string;
  type: 'google' | 'outlook' | 'calendly' | 'acuity';
  isConnected: boolean;
  settings: {
    calendarId?: string;
    timeZone: string;
    workingHours: {
      start: string; // "09:00"
      end: string; // "17:00"
      days: number[]; // [1,2,3,4,5] for Mon-Fri
    };
    bufferTime: number; // minutes between appointments
    maxAdvanceBooking: number; // days
  };
}

export class CalendarService {
  private providers: CalendarProvider[] = [];
  private events: CalendarEvent[] = [];

  constructor() {
    this.initializeDefaultProvider();
  }

  // Initialize default calendar provider
  private initializeDefaultProvider() {
    this.providers.push({
      id: 'default-calendar',
      name: 'Business Calendar',
      type: 'google',
      isConnected: true,
      settings: {
        timeZone: 'America/New_York',
        workingHours: {
          start: '08:00',
          end: '18:00',
          days: [1, 2, 3, 4, 5, 6] // Mon-Sat
        },
        bufferTime: 30,
        maxAdvanceBooking: 30
      }
    });
  }

  // Get available time slots for a given date range
  async getAvailableSlots(
    startDate: Date, 
    endDate: Date, 
    duration: number = 60,
    providerId: string = 'default-calendar'
  ): Promise<TimeSlot[]> {
    const provider = this.providers.find(p => p.id === providerId);
    if (!provider) {
      throw new Error('Calendar provider not found');
    }

    const slots: TimeSlot[] = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      const dayOfWeek = current.getDay() === 0 ? 7 : current.getDay(); // Convert Sunday from 0 to 7
      
      // Check if this day is a working day
      if (provider.settings.workingHours.days.includes(dayOfWeek)) {
        const daySlots = this.generateDaySlotsForDate(current, duration, provider);
        slots.push(...daySlots);
      }
      
      current.setDate(current.getDate() + 1);
    }

    return slots;
  }

  // Generate time slots for a specific date
  private generateDaySlotsForDate(
    date: Date, 
    duration: number, 
    provider: CalendarProvider
  ): TimeSlot[] {
    const slots: TimeSlot[] = [];
    const workingHours = provider.settings.workingHours;
    
    // Parse working hours
    const [startHour, startMinute] = workingHours.start.split(':').map(Number);
    const [endHour, endMinute] = workingHours.end.split(':').map(Number);
    
    const startTime = new Date(date);
    startTime.setHours(startHour, startMinute, 0, 0);
    
    const endTime = new Date(date);
    endTime.setHours(endHour, endMinute, 0, 0);
    
    const current = new Date(startTime);
    
    while (current < endTime) {
      const slotEnd = new Date(current.getTime() + duration * 60000);
      
      if (slotEnd <= endTime) {
        const isAvailable = this.isSlotAvailable(current, slotEnd, provider);
        
        slots.push({
          start: new Date(current),
          end: new Date(slotEnd),
          available: isAvailable,
          reason: isAvailable ? undefined : 'Booked'
        });
      }
      
      // Move to next slot (duration + buffer time)
      current.setTime(current.getTime() + (duration + provider.settings.bufferTime) * 60000);
    }
    
    return slots;
  }

  // Check if a time slot is available
  private isSlotAvailable(start: Date, end: Date, provider: CalendarProvider): boolean {
    // Check against existing events
    const conflictingEvents = this.events.filter(event => {
      return event.status !== 'cancelled' && 
             ((start >= event.startTime && start < event.endTime) ||
              (end > event.startTime && end <= event.endTime) ||
              (start <= event.startTime && end >= event.endTime));
    });

    return conflictingEvents.length === 0;
  }

  // Book an appointment
  async bookAppointment(
    lead: Lead,
    startTime: Date,
    serviceType: string,
    duration: number = 60,
    providerId: string = 'default-calendar'
  ): Promise<CalendarEvent> {
    const provider = this.providers.find(p => p.id === providerId);
    if (!provider) {
      throw new Error('Calendar provider not found');
    }

    const endTime = new Date(startTime.getTime() + duration * 60000);
    
    // Check availability
    if (!this.isSlotAvailable(startTime, endTime, provider)) {
      throw new Error('Time slot is not available');
    }

    // Create calendar event
    const event: CalendarEvent = {
      id: `event-${Date.now()}`,
      title: `Pressure Washing Estimate - ${lead.name}`,
      description: this.generateAppointmentDescription(lead, serviceType),
      startTime,
      endTime,
      location: lead.address || 'Customer Location',
      attendees: [
        {
          email: lead.email || '',
          name: lead.name,
          status: 'pending'
        }
      ],
      leadId: lead.id,
      serviceType,
      estimatedDuration: duration,
      status: 'scheduled'
    };

    this.events.push(event);

    // In a real implementation, this would sync with the actual calendar provider
    await this.syncWithProvider(event, provider);

    return event;
  }

  // Generate appointment description
  private generateAppointmentDescription(lead: Lead, serviceType: string): string {
    return `Free Pressure Washing Estimate

Customer: ${lead.name}
Phone: ${lead.phone}
Email: ${lead.email || 'Not provided'}
Service: ${serviceType}
Property Type: ${lead.property_type}
Urgency: ${lead.urgency.replace('_', ' ')}
Budget Range: ${lead.budget_range || 'Not specified'}

Notes: ${lead.notes || 'No additional notes'}

Please arrive 5 minutes early and bring:
- Measuring tools
- Estimate forms
- Business cards
- Before photos capability`;
  }

  // Sync with calendar provider (mock implementation)
  private async syncWithProvider(event: CalendarEvent, provider: CalendarProvider): Promise<void> {
    console.log(`Syncing event ${event.id} with ${provider.name}`);
    
    // Mock API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real implementation, this would:
    // - Create event in Google Calendar, Outlook, etc.
    // - Send calendar invites
    // - Set up reminders
    // - Handle timezone conversions
    
    console.log(`Event synced successfully with ${provider.type}`);
  }

  // Cancel an appointment
  async cancelAppointment(eventId: string, reason?: string): Promise<void> {
    const event = this.events.find(e => e.id === eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    event.status = 'cancelled';
    event.description += `\n\nCancelled: ${reason || 'No reason provided'}`;

    // Sync cancellation with provider
    const provider = this.providers[0]; // Use default provider
    await this.syncWithProvider(event, provider);

    console.log(`Appointment ${eventId} cancelled`);
  }

  // Reschedule an appointment
  async rescheduleAppointment(
    eventId: string, 
    newStartTime: Date, 
    duration?: number
  ): Promise<CalendarEvent> {
    const event = this.events.find(e => e.id === eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    const newDuration = duration || event.estimatedDuration;
    const newEndTime = new Date(newStartTime.getTime() + newDuration * 60000);

    // Check availability for new time
    const provider = this.providers[0];
    if (!this.isSlotAvailable(newStartTime, newEndTime, provider)) {
      throw new Error('New time slot is not available');
    }

    // Update event
    event.startTime = newStartTime;
    event.endTime = newEndTime;
    event.estimatedDuration = newDuration;
    event.description += `\n\nRescheduled from original time`;

    // Sync with provider
    await this.syncWithProvider(event, provider);

    return event;
  }

  // Get appointments for a date range
  getAppointments(startDate: Date, endDate: Date): CalendarEvent[] {
    return this.events.filter(event => 
      event.startTime >= startDate && 
      event.startTime <= endDate &&
      event.status !== 'cancelled'
    );
  }

  // Get appointments for a specific lead
  getLeadAppointments(leadId: string): CalendarEvent[] {
    return this.events.filter(event => event.leadId === leadId);
  }

  // Mark appointment as completed
  async completeAppointment(eventId: string, notes?: string): Promise<void> {
    const event = this.events.find(e => e.id === eventId);
    if (!event) {
      throw new Error('Event not found');
    }

    event.status = 'completed';
    if (notes) {
      event.description += `\n\nCompletion Notes: ${notes}`;
    }

    console.log(`Appointment ${eventId} marked as completed`);
  }

  // Get calendar statistics
  getCalendarStats(): {
    totalAppointments: number;
    scheduledAppointments: number;
    completedAppointments: number;
    cancelledAppointments: number;
    noShowAppointments: number;
    upcomingAppointments: number;
  } {
    const now = new Date();
    
    return {
      totalAppointments: this.events.length,
      scheduledAppointments: this.events.filter(e => e.status === 'scheduled').length,
      completedAppointments: this.events.filter(e => e.status === 'completed').length,
      cancelledAppointments: this.events.filter(e => e.status === 'cancelled').length,
      noShowAppointments: this.events.filter(e => e.status === 'no_show').length,
      upcomingAppointments: this.events.filter(e => 
        e.status === 'scheduled' && e.startTime > now
      ).length
    };
  }

  // Get next available appointment slot
  async getNextAvailableSlot(
    duration: number = 60,
    providerId: string = 'default-calendar'
  ): Promise<TimeSlot | null> {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const twoWeeksFromNow = new Date();
    twoWeeksFromNow.setDate(twoWeeksFromNow.getDate() + 14);

    const availableSlots = await this.getAvailableSlots(
      tomorrow, 
      twoWeeksFromNow, 
      duration, 
      providerId
    );

    const nextSlot = availableSlots.find(slot => slot.available);
    return nextSlot || null;
  }
}

// Export singleton instance
export const calendarService = new CalendarService();
