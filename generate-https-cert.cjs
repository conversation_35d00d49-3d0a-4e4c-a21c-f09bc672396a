/**
 * Generate HTTPS certificates for local development
 * This script creates self-signed certificates for localhost
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔐 Generating HTTPS certificates for local development...');

try {
  // Check if OpenSSL is available
  try {
    execSync('openssl version', { stdio: 'ignore' });
  } catch (error) {
    console.log('❌ OpenSSL not found. Please install OpenSSL to generate certificates.');
    console.log('💡 Alternative: Use mkcert for easier certificate generation:');
    console.log('   npm install -g mkcert');
    console.log('   mkcert -install');
    console.log('   mkcert localhost 127.0.0.1 ::1');
    process.exit(1);
  }

  // Generate private key
  console.log('📝 Generating private key...');
  execSync('openssl genrsa -out localhost-key.pem 2048', { stdio: 'inherit' });

  // Generate certificate
  console.log('📜 Generating certificate...');
  execSync(`openssl req -new -x509 -key localhost-key.pem -out localhost.pem -days 365 -subj "/C=US/ST=CA/L=San Francisco/O=PressureMax/OU=Development/CN=localhost"`, { stdio: 'inherit' });

  // Verify files were created
  if (fs.existsSync('localhost-key.pem') && fs.existsSync('localhost.pem')) {
    console.log('✅ HTTPS certificates generated successfully!');
    console.log('📁 Files created:');
    console.log('   - localhost-key.pem (private key)');
    console.log('   - localhost.pem (certificate)');
    console.log('');
    console.log('🚀 You can now run the development server with HTTPS:');
    console.log('   npm run dev');
    console.log('');
    console.log('🌐 Access your app at: https://localhost:5174');
    console.log('');
    console.log('⚠️  Note: You may see a security warning in your browser.');
    console.log('   Click "Advanced" → "Proceed to localhost (unsafe)" to continue.');
  } else {
    console.log('❌ Failed to generate certificates.');
  }

} catch (error) {
  console.error('❌ Error generating certificates:', error.message);
  console.log('');
  console.log('💡 Alternative solution using mkcert:');
  console.log('   1. Install mkcert: npm install -g mkcert');
  console.log('   2. Install CA: mkcert -install');
  console.log('   3. Generate certs: mkcert localhost 127.0.0.1 ::1');
  console.log('   4. Rename files:');
  console.log('      mv localhost+2.pem localhost.pem');
  console.log('      mv localhost+2-key.pem localhost-key.pem');
}
