# PressureMax Facebook Marketing API Integration

This backend service integrates Facebook Marketing API with PressureMax, allowing users to launch ad campaigns directly from the dashboard.

## 🚀 Quick Start

### 1. Setup Environment

```bash
cd backend
python setup.py
```

### 2. Configure Facebook App

Update the `.env` file with your Facebook app secret:

```env
FACEBOOK_APP_SECRET=your_actual_app_secret_here
```

### 3. Run the Server

```bash
python app.py
```

The server will start on `http://localhost:5000`

## 📋 Facebook App Configuration

### App Details
- **App ID**: `***************`
- **App Name**: PressureMax Marketing
- **Access Token**: Already configured (expires periodically)

### Required Permissions
- `ads_management` - Create and manage ad campaigns
- `ads_read` - Read campaign data and insights
- `read_insights` - Access performance metrics

### API Endpoints

#### Health Check
```
GET /health
```

#### Get Ad Accounts
```
GET /api/facebook/accounts
```

#### Launch Campaign from Template
```
POST /api/facebook/launch-campaign
{
  "account_id": "*********",
  "template": { ... },
  "targeting": { ... },
  "budget": { "daily_budget": 50 }
}
```

#### Campaign Management
```
POST /api/facebook/campaign/{id}/pause
POST /api/facebook/campaign/{id}/resume
GET /api/facebook/campaign/{id}/insights?start_date=2024-01-01&end_date=2024-01-31
```

## 🎯 How It Works

### 1. Template Selection
Users select a campaign template from the PressureMax dashboard.

### 2. Campaign Creation
The system automatically creates:
- **Campaign** with lead generation objective
- **Ad Set** with targeting and budget
- **Ad Creative** using template content
- **Ad** linking everything together

### 3. Targeting Options
- **Homeowners**: For residential services
- **Business Owners**: For commercial services
- **Location-based**: Radius targeting around service area

### 4. Budget Management
- Default: $50/day per campaign
- Campaigns start paused for review
- Users can adjust budgets in Facebook Ads Manager

## 🔧 Development

### Project Structure
```
backend/
├── app.py              # Flask API server
├── facebook_api.py     # Facebook SDK wrapper
├── requirements.txt    # Python dependencies
├── setup.py           # Setup script
└── .env               # Environment variables
```

### Key Dependencies
- `facebook-business` - Official Facebook Business SDK
- `Flask` - Web framework
- `Flask-CORS` - Cross-origin requests
- `python-dotenv` - Environment variables

### Testing

```bash
# Test Facebook API connection
curl http://localhost:5000/health

# Test ad accounts endpoint
curl http://localhost:5000/api/facebook/accounts
```

## 🛡️ Security

### Access Token Management
- Tokens are stored securely in environment variables
- Tokens expire and need periodic renewal
- Use Facebook's token debugger to check status

### API Rate Limits
- Facebook enforces rate limits on API calls
- The SDK handles basic rate limiting
- Monitor usage in Facebook Developer Console

### Error Handling
- All API calls include comprehensive error handling
- Errors are logged and returned to frontend
- Failed campaigns can be retried

## 📊 Campaign Performance

### Metrics Tracked
- **Impressions**: How many times ads were shown
- **Clicks**: Number of ad clicks
- **Spend**: Total amount spent
- **CPM**: Cost per 1,000 impressions
- **CPC**: Cost per click
- **CTR**: Click-through rate
- **Leads**: Lead generation actions
- **Cost per Lead**: Average cost to acquire a lead

### Insights API
Real-time performance data is available through the insights endpoints.

## 🔄 Workflow Integration

### From PressureMax Dashboard
1. User selects campaign template
2. Clicks "Launch on Facebook"
3. System creates complete campaign
4. Campaign starts paused for review
5. User can activate in Facebook Ads Manager

### Campaign Lifecycle
1. **Created** - Campaign is built but paused
2. **Review** - User reviews in Facebook Ads Manager
3. **Active** - Campaign is running and spending budget
4. **Optimized** - Facebook optimizes for best performance
5. **Completed** - Campaign reaches end date or budget

## 🚨 Troubleshooting

### Common Issues

#### "Facebook API not initialized"
- Check that FACEBOOK_APP_SECRET is set in .env
- Verify access token is valid
- Ensure all required permissions are granted

#### "No ad accounts found"
- User needs access to a Facebook ad account
- Check Business Manager permissions
- Verify account is active and not restricted

#### "Failed to create campaign"
- Check campaign name doesn't already exist
- Verify targeting parameters are valid
- Ensure account has sufficient permissions

### Debug Mode
Enable debug mode to see raw API calls:

```python
FacebookAdsApi.init(app_id, app_secret, access_token, debug=True)
```

## 📞 Support

### Facebook Resources
- [Marketing API Documentation](https://developers.facebook.com/docs/marketing-api)
- [Business SDK GitHub](https://github.com/facebook/facebook-python-business-sdk)
- [Facebook Developer Support](https://developers.facebook.com/support)

### PressureMax Support
- Check the main README for general support
- Report issues in the project repository
- Contact the development team for API-specific issues

## 🔮 Future Enhancements

### Planned Features
- **Campaign Templates**: More specialized templates
- **Advanced Targeting**: Custom audience integration
- **Budget Optimization**: Automatic budget allocation
- **A/B Testing**: Built-in creative testing
- **Reporting Dashboard**: Enhanced analytics
- **Multi-Account**: Support for multiple ad accounts

### Integration Opportunities
- **Google Ads**: Parallel campaign creation
- **CRM Integration**: Lead data synchronization
- **Analytics**: Enhanced tracking and attribution
- **Automation**: Smart campaign optimization
