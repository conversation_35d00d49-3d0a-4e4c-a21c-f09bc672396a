#!/bin/bash

echo ""
echo "========================================"
echo " PressureMax Facebook Integration Setup"
echo "========================================"
echo ""

echo "🔧 Setting up Facebook Marketing API integration..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

echo "✅ Python is installed"

# Navigate to backend directory
if [ ! -d "backend" ]; then
    echo "❌ Backend directory not found"
    echo "Please run this script from the project root directory"
    exit 1
fi

cd backend

# Install Python dependencies
echo ""
echo "📦 Installing Python dependencies..."
if ! pip3 install -r requirements.txt; then
    echo "❌ Failed to install dependencies"
    echo "Try: python3 -m pip install -r requirements.txt"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo ""
    echo "📝 Creating .env configuration file..."
    cp .env.example .env
    echo "⚠️  Please update FACEBOOK_APP_SECRET in backend/.env file"
fi

echo ""
echo "🚀 Starting Facebook API server..."
echo "Server will start on http://localhost:5000"
echo ""
echo "📋 Next steps:"
echo "1. Keep this terminal open (Flask server running)"
echo "2. Open a new terminal and run: npm start"
echo "3. Test Facebook integration from the dashboard"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the Flask server
python3 app.py
