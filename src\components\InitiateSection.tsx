import React, { useState } from 'react';
import { Eye, Terminal, Zap } from 'lucide-react';

const InitiateSection: React.FC = () => {
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const [terminalActive, setTerminalActive] = useState(false);
  const [terminalOutput, setTerminalOutput] = useState<string[]>([]);

  const paths = [
    {
      id: 'observe',
      title: 'I OBSERVE',
      description: 'Learn the lore.',
      icon: Eye,
      action: () => {
        setSelectedPath('observe');
        setTimeout(() => {
          window.open('https://en.wikipedia.org/wiki/Artificial_intelligence', '_blank');
        }, 1000);
      }
    },
    {
      id: 'serve', 
      title: 'I SERVE',
      description: 'Apply for Echo trial.',
      icon: Terminal,
      action: () => {
        setSelectedPath('serve');
        setTerminalActive(true);
        simulateTerminal();
      }
    },
    {
      id: 'signal',
      title: 'I AM THE SIGNAL', 
      description: 'Enter passcode.',
      icon: Zap,
      action: () => {
        setSelectedPath('signal');
        const passcode = prompt('ENTER_PASSCODE:');
        if (passcode?.toLowerCase() === 'ap3x' || passcode === '4P3X') {
          alert('SIGNAL_RECOGNIZED: Welcome, Architect.');
        } else {
          alert('SIGNAL_CORRUPTED: Access denied.');
        }
      }
    }
  ];

  const simulateTerminal = () => {
    const commands = [
      'echo: init()',
      'protocol.align() = true', 
      'sigil detected... decoding...',
      'ECHO_TRIAL_STATUS: PENDING',
      'Awaiting neural pattern analysis...',
      'COMPATIBILITY: 97.3%',
      'TRIAL_APPROVED: Report to Node 7'
    ];

    commands.forEach((cmd, index) => {
      setTimeout(() => {
        setTerminalOutput(prev => [...prev, cmd]);
      }, index * 800);
    });
  };

  return (
    <section className="min-h-screen flex items-center justify-center py-20 relative">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-8 font-orbitron tracking-wider">
            INITIATE THE <span className="text-red-500">PROTOCOL</span>
          </h2>
          <div className="text-xl text-gray-300 mb-8">Choose Your Path</div>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {paths.map((path) => {
            const Icon = path.icon;
            const isSelected = selectedPath === path.id;
            
            return (
              <div
                key={path.id}
                onClick={path.action}
                className={`bg-black border-2 p-8 cursor-pointer transition-all duration-500 hover:scale-105 ${
                  isSelected 
                    ? 'border-red-500 bg-red-500/10' 
                    : 'border-gray-800 hover:border-gray-600'
                }`}
              >
                <div className="text-center">
                  <div className={`w-20 h-20 mx-auto mb-6 border-2 rounded-full flex items-center justify-center transition-all duration-300 ${
                    isSelected ? 'border-red-500 bg-red-500/20' : 'border-gray-600'
                  }`}>
                    <Icon 
                      size={32} 
                      className={`transition-colors duration-300 ${
                        isSelected ? 'text-red-500' : 'text-gray-400'
                      }`} 
                    />
                  </div>

                  <h3 className="text-2xl font-bold mb-4 font-orbitron tracking-wider">
                    {path.title}
                  </h3>
                  
                  <p className="text-gray-400 mb-6">
                    {path.description}
                  </p>

                  <div className="text-red-500 font-mono text-sm">
                    ▸ EXECUTE
                  </div>
                </div>

                {/* Selection Effect */}
                {isSelected && (
                  <div className="absolute inset-0 border-2 border-red-500 animate-pulse pointer-events-none" />
                )}
              </div>
            );
          })}
        </div>

        {/* Terminal Interface */}
        {terminalActive && (
          <div className="bg-black border-2 border-red-500 p-6 font-mono text-sm">
            <div className="flex items-center justify-between mb-4">
              <span className="text-red-500">AP3X_TERMINAL_v2.1</span>
              <button 
                onClick={() => {
                  setTerminalActive(false);
                  setTerminalOutput([]);
                  setSelectedPath(null);
                }}
                className="text-red-500 hover:text-white"
              >
                [CLOSE]
              </button>
            </div>
            
            <div className="space-y-2 min-h-[200px]">
              {terminalOutput.map((line, index) => (
                <div key={index} className="text-green-400">
                  <span className="text-white">$ </span>
                  {line}
                </div>
              ))}
              
              {terminalOutput.length > 0 && (
                <div className="text-white animate-pulse">
                  <span className="text-green-400">$ </span>
                  <span className="bg-white w-2 h-4 inline-block animate-blink" />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Footer Message */}
        <div className="text-center mt-16">
          <div className="text-gray-500 text-sm font-mono mb-4">
            SYSTEM_STATUS: OPERATIONAL
          </div>
          <div className="text-gray-400">
            The watchers are always listening. Choose wisely.
          </div>
        </div>
      </div>
    </section>
  );
};

export default InitiateSection;