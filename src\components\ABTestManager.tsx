import React, { useState, useEffect } from 'react';
import { Play, Pause, BarChart3, TrendingUp, Award, AlertCircle, Plus, X } from 'lucide-react';
import { AdTemplate } from '../types/database';
import { abTestingService, ABTest, ABTestTemplate } from '../services/abTesting';

interface ABTestManagerProps {
  templates: AdTemplate[];
  onTestCreated?: (test: ABTest) => void;
  className?: string;
}

export const ABTestManager: React.FC<ABTestManagerProps> = ({
  templates,
  onTestCreated,
  className = ''
}) => {
  const [tests, setTests] = useState<ABTest[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<AdTemplate | null>(null);
  const [testSuggestions, setTestSuggestions] = useState<ABTestTemplate[]>([]);
  const [newTestConfig, setNewTestConfig] = useState({
    name: '',
    description: '',
    testType: 'headline' as ABTest['testType'],
    variationA: '',
    variationB: '',
    trafficSplit: 50,
    primaryMetric: 'cpl' as ABTest['primaryMetric']
  });

  useEffect(() => {
    loadTests();
  }, []);

  const loadTests = () => {
    const allTests = abTestingService.getTests();
    setTests(allTests);
  };

  const handleCreateTest = async () => {
    if (!selectedTemplate || !newTestConfig.name || !newTestConfig.variationA || !newTestConfig.variationB) {
      return;
    }

    try {
      const test = await abTestingService.createABTest({
        name: newTestConfig.name,
        description: newTestConfig.description,
        baseTemplateId: selectedTemplate.id,
        testType: newTestConfig.testType,
        variations: {
          A: { [newTestConfig.testType]: newTestConfig.variationA },
          B: { [newTestConfig.testType]: newTestConfig.variationB }
        },
        trafficSplit: newTestConfig.trafficSplit,
        primaryMetric: newTestConfig.primaryMetric
      });

      setTests(prev => [...prev, test]);
      setIsCreateModalOpen(false);
      resetForm();

      if (onTestCreated) {
        onTestCreated(test);
      }
    } catch (error) {
      console.error('Error creating test:', error);
    }
  };

  const handleStartTest = async (testId: string) => {
    try {
      await abTestingService.startTest(testId);
      loadTests();
    } catch (error) {
      console.error('Error starting test:', error);
    }
  };

  const handleStopTest = async (testId: string) => {
    try {
      await abTestingService.stopTest(testId);
      loadTests();
    } catch (error) {
      console.error('Error stopping test:', error);
    }
  };

  const resetForm = () => {
    setNewTestConfig({
      name: '',
      description: '',
      testType: 'headline',
      variationA: '',
      variationB: '',
      trafficSplit: 50,
      primaryMetric: 'cpl'
    });
    setSelectedTemplate(null);
    setTestSuggestions([]);
  };

  const handleTemplateSelect = (template: AdTemplate) => {
    setSelectedTemplate(template);
    const suggestions = abTestingService.generateTestSuggestions(template.id);
    setTestSuggestions(suggestions);
  };

  const applySuggestion = (suggestion: ABTestTemplate) => {
    const testType = Object.keys(suggestion.variations)[0] as ABTest['testType'];
    const variations = suggestion.variations[testType as keyof typeof suggestion.variations];
    
    if (variations && variations.length >= 2) {
      setNewTestConfig(prev => ({
        ...prev,
        name: suggestion.testName,
        testType,
        variationA: variations[0],
        variationB: variations[1]
      }));
    }
  };

  const getStatusColor = (status: ABTest['status']) => {
    switch (status) {
      case 'running': return 'text-green-400 bg-green-500/20';
      case 'completed': return 'text-blue-400 bg-blue-500/20';
      case 'paused': return 'text-yellow-400 bg-yellow-500/20';
      case 'draft': return 'text-gray-400 bg-gray-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getWinnerColor = (winner?: string) => {
    switch (winner) {
      case 'A': return 'text-green-400';
      case 'B': return 'text-blue-400';
      default: return 'text-gray-400';
    }
  };

  const stats = abTestingService.getTestStatistics();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">A/B Testing</h2>
          <p className="text-gray-400">Optimize your ad templates with data-driven testing</p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-3 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
        >
          <Plus size={20} />
          <span>Create Test</span>
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Tests</p>
              <p className="text-2xl font-bold text-white">{stats.totalTests}</p>
            </div>
            <BarChart3 className="text-cyan-400" size={24} />
          </div>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Running Tests</p>
              <p className="text-2xl font-bold text-green-400">{stats.runningTests}</p>
            </div>
            <Play className="text-green-400" size={24} />
          </div>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Successful Tests</p>
              <p className="text-2xl font-bold text-purple-400">{stats.successfulTests}</p>
            </div>
            <Award className="text-purple-400" size={24} />
          </div>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Avg Improvement</p>
              <p className="text-2xl font-bold text-green-400">{stats.avgImprovementPercent.toFixed(1)}%</p>
            </div>
            <TrendingUp className="text-green-400" size={24} />
          </div>
        </div>
      </div>

      {/* Tests List */}
      <div className="bg-black border border-cyan-500/30">
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Active Tests</h3>
          {tests.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <BarChart3 className="mx-auto mb-2" size={32} />
              <p>No A/B tests created yet</p>
              <p className="text-sm">Create your first test to start optimizing your ad templates</p>
            </div>
          ) : (
            <div className="space-y-4">
              {tests.map((test) => (
                <div key={test.id} className="border border-gray-700 p-4 hover:border-gray-600 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-white font-semibold">{test.name}</h4>
                      <span className={`px-2 py-1 text-xs font-bold ${getStatusColor(test.status)}`}>
                        {test.status.toUpperCase()}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      {test.status === 'draft' && (
                        <button
                          onClick={() => handleStartTest(test.id)}
                          className="text-green-400 hover:text-green-300"
                          title="Start Test"
                        >
                          <Play size={16} />
                        </button>
                      )}
                      {test.status === 'running' && (
                        <button
                          onClick={() => handleStopTest(test.id)}
                          className="text-red-400 hover:text-red-300"
                          title="Stop Test"
                        >
                          <Pause size={16} />
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-400">Testing: {test.testType.replace('_', ' ')}</p>
                      <p className="text-gray-400">Primary Metric: {test.primaryMetric.toUpperCase()}</p>
                      <p className="text-gray-400">Traffic Split: {test.trafficSplit}% / {100 - test.trafficSplit}%</p>
                    </div>
                    
                    {test.results && (
                      <div>
                        <p className="text-gray-400">Winner: 
                          <span className={`ml-1 font-semibold ${getWinnerColor(test.results.winner)}`}>
                            {test.results.winner === 'inconclusive' ? 'Inconclusive' : `Variant ${test.results.winner}`}
                          </span>
                        </p>
                        <p className="text-gray-400">Confidence: {test.results.significance.toFixed(1)}%</p>
                        {test.results.improvementPercent && (
                          <p className="text-gray-400">Improvement: 
                            <span className="text-green-400 ml-1 font-semibold">
                              {test.results.improvementPercent.toFixed(1)}%
                            </span>
                          </p>
                        )}
                      </div>
                    )}

                    <div>
                      {test.startDate && (
                        <p className="text-gray-400">Started: {test.startDate.toLocaleDateString()}</p>
                      )}
                      {test.endDate && test.status === 'completed' && (
                        <p className="text-gray-400">Ended: {test.endDate.toLocaleDateString()}</p>
                      )}
                    </div>
                  </div>

                  {test.results?.recommendation && (
                    <div className="mt-3 p-3 bg-gray-900 border border-gray-700">
                      <div className="flex items-start space-x-2">
                        <AlertCircle className="text-cyan-400 mt-0.5" size={16} />
                        <p className="text-sm text-gray-300">{test.results.recommendation}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Create Test Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-black border border-cyan-500/30 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">Create A/B Test</h3>
                <button
                  onClick={() => setIsCreateModalOpen(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="space-y-4">
                {/* Template Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Select Template to Test
                  </label>
                  <select
                    value={selectedTemplate?.id || ''}
                    onChange={(e) => {
                      const template = templates.find(t => t.id === e.target.value);
                      if (template) handleTemplateSelect(template);
                    }}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="">Choose a template</option>
                    {templates.map(template => (
                      <option key={template.id} value={template.id}>{template.name}</option>
                    ))}
                  </select>
                </div>

                {/* Test Suggestions */}
                {testSuggestions.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Quick Test Ideas
                    </label>
                    <div className="grid grid-cols-1 gap-2">
                      {testSuggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => applySuggestion(suggestion)}
                          className="text-left p-3 border border-gray-700 hover:border-cyan-500 transition-colors"
                        >
                          <div className="text-white font-medium">{suggestion.testName}</div>
                          <div className="text-xs text-gray-400">
                            Test {Object.keys(suggestion.variations)[0]} variations
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Test Configuration */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Test Name
                    </label>
                    <input
                      type="text"
                      value={newTestConfig.name}
                      onChange={(e) => setNewTestConfig(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                      placeholder="e.g., Headline Test"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Test Type
                    </label>
                    <select
                      value={newTestConfig.testType}
                      onChange={(e) => setNewTestConfig(prev => ({ ...prev, testType: e.target.value as ABTest['testType'] }))}
                      className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    >
                      <option value="headline">Headline</option>
                      <option value="primary_text">Primary Text</option>
                      <option value="cta">Call to Action</option>
                      <option value="creative">Creative</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Variation A
                  </label>
                  <textarea
                    value={newTestConfig.variationA}
                    onChange={(e) => setNewTestConfig(prev => ({ ...prev, variationA: e.target.value }))}
                    rows={2}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="Enter first variation..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Variation B
                  </label>
                  <textarea
                    value={newTestConfig.variationB}
                    onChange={(e) => setNewTestConfig(prev => ({ ...prev, variationB: e.target.value }))}
                    rows={2}
                    className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="Enter second variation..."
                  />
                </div>

                <div className="flex justify-end space-x-4">
                  <button
                    onClick={() => setIsCreateModalOpen(false)}
                    className="px-6 py-2 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateTest}
                    disabled={!selectedTemplate || !newTestConfig.name || !newTestConfig.variationA || !newTestConfig.variationB}
                    className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Test
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
