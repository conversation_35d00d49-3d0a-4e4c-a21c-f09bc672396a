// Automated Follow-up Service for Multi-touch Lead Nurturing
import { Lead } from '../types/database';
import { vapiService } from './vapi';
import { db } from './database';

export interface FollowUpSequence {
  id: string;
  name: string;
  description: string;
  triggers: {
    leadStatus: string[];
    leadQuality: string[];
    daysSinceLastContact: number;
    noAppointmentScheduled: boolean;
  };
  steps: FollowUpStep[];
  isActive: boolean;
}

export interface FollowUpStep {
  id: string;
  sequenceId: string;
  stepNumber: number;
  type: 'email' | 'sms' | 'voice_call' | 'wait';
  delayHours: number;
  content: {
    subject?: string; // For email
    message: string;
    template?: string;
  };
  conditions?: {
    leadQuality?: string[];
    urgency?: string[];
    propertyType?: string[];
  };
}

export interface FollowUpExecution {
  id: string;
  leadId: string;
  sequenceId: string;
  stepId: string;
  scheduledAt: Date;
  executedAt?: Date;
  status: 'scheduled' | 'executing' | 'completed' | 'failed' | 'skipped';
  result?: {
    success: boolean;
    message: string;
    responseReceived?: boolean;
  };
}

export class FollowUpService {
  private sequences: FollowUpSequence[] = [];
  private executions: FollowUpExecution[] = [];

  constructor() {
    this.initializeDefaultSequences();
  }

  // Initialize default follow-up sequences
  private initializeDefaultSequences() {
    this.sequences = [
      {
        id: 'seq-new-lead',
        name: 'New Lead Welcome Sequence',
        description: 'Immediate follow-up for new leads who haven\'t been contacted',
        triggers: {
          leadStatus: ['new'],
          leadQuality: ['hot', 'warm', 'cold'],
          daysSinceLastContact: 0,
          noAppointmentScheduled: true
        },
        steps: [
          {
            id: 'step-1',
            sequenceId: 'seq-new-lead',
            stepNumber: 1,
            type: 'voice_call',
            delayHours: 0, // Immediate
            content: {
              message: 'Initial qualification call to new lead'
            }
          },
          {
            id: 'step-2',
            sequenceId: 'seq-new-lead',
            stepNumber: 2,
            type: 'sms',
            delayHours: 2, // 2 hours after call
            content: {
              message: 'Hi {name}! Thanks for your interest in pressure washing. We tried calling but missed you. When\'s a good time to discuss your project? Reply STOP to opt out.'
            }
          },
          {
            id: 'step-3',
            sequenceId: 'seq-new-lead',
            stepNumber: 3,
            type: 'email',
            delayHours: 24, // 1 day after initial contact
            content: {
              subject: 'Your Pressure Washing Estimate - {name}',
              message: 'Hi {name},\n\nWe noticed you\'re interested in pressure washing services. We\'d love to provide you with a free, no-obligation estimate.\n\nOur services include:\n• House washing\n• Driveway cleaning\n• Deck restoration\n• Roof cleaning\n\nWhen would be a good time to schedule your free estimate?\n\nBest regards,\nThe PressureMax Team'
            }
          }
        ],
        isActive: true
      },
      {
        id: 'seq-no-answer',
        name: 'No Answer Follow-up',
        description: 'Follow-up sequence for leads who didn\'t answer initial calls',
        triggers: {
          leadStatus: ['contacted'],
          leadQuality: ['hot', 'warm'],
          daysSinceLastContact: 1,
          noAppointmentScheduled: true
        },
        steps: [
          {
            id: 'step-1',
            sequenceId: 'seq-no-answer',
            stepNumber: 1,
            type: 'voice_call',
            delayHours: 0,
            content: {
              message: 'Second attempt call with different time of day'
            }
          },
          {
            id: 'step-2',
            sequenceId: 'seq-no-answer',
            stepNumber: 2,
            type: 'sms',
            delayHours: 4,
            content: {
              message: 'Hi {name}, we tried calling about your pressure washing project. Would you prefer we text you instead? What\'s the best way to reach you?'
            }
          },
          {
            id: 'step-3',
            sequenceId: 'seq-no-answer',
            stepNumber: 3,
            type: 'wait',
            delayHours: 48,
            content: {
              message: 'Wait period before next contact attempt'
            }
          },
          {
            id: 'step-4',
            sequenceId: 'seq-no-answer',
            stepNumber: 4,
            type: 'voice_call',
            delayHours: 0,
            content: {
              message: 'Final call attempt with special offer'
            }
          }
        ],
        isActive: true
      },
      {
        id: 'seq-reactivation',
        name: 'Lead Reactivation Campaign',
        description: 'Reactivate cold leads after extended period',
        triggers: {
          leadStatus: ['contacted', 'qualified'],
          leadQuality: ['warm', 'cold'],
          daysSinceLastContact: 30,
          noAppointmentScheduled: true
        },
        steps: [
          {
            id: 'step-1',
            sequenceId: 'seq-reactivation',
            stepNumber: 1,
            type: 'email',
            delayHours: 0,
            content: {
              subject: 'Special Offer - {name}, Ready for That Pressure Washing?',
              message: 'Hi {name},\n\nWe spoke about pressure washing your property a while back. We\'re running a special promotion this month and wanted to reach out.\n\n🎯 15% OFF all pressure washing services\n🎯 Free deck sealing with house wash\n🎯 Same-day estimates available\n\nIs now a good time to schedule your free estimate?\n\nThis offer expires soon, so let us know if you\'re interested!\n\nBest regards,\nThe PressureMax Team'
            }
          },
          {
            id: 'step-2',
            sequenceId: 'seq-reactivation',
            stepNumber: 2,
            type: 'sms',
            delayHours: 72,
            content: {
              message: 'Hi {name}! Quick reminder about our 15% off pressure washing special. Expires this week. Interested in scheduling? Reply YES for details.'
            }
          }
        ],
        isActive: true
      }
    ];
  }

  // Check if a lead qualifies for any follow-up sequences
  async checkLeadForSequences(lead: Lead): Promise<FollowUpSequence[]> {
    const qualifyingSequences: FollowUpSequence[] = [];
    const daysSinceLastContact = lead.last_contacted_at 
      ? Math.floor((Date.now() - new Date(lead.last_contacted_at).getTime()) / (1000 * 60 * 60 * 24))
      : 999;

    for (const sequence of this.sequences) {
      if (!sequence.isActive) continue;

      const triggers = sequence.triggers;
      
      // Check all trigger conditions
      const statusMatch = triggers.leadStatus.includes(lead.status);
      const qualityMatch = triggers.leadQuality.includes(lead.quality);
      const daysSinceMatch = daysSinceLastContact >= triggers.daysSinceLastContact;
      const appointmentMatch = triggers.noAppointmentScheduled ? !lead.appointment_scheduled : true;

      if (statusMatch && qualityMatch && daysSinceMatch && appointmentMatch) {
        // Check if this sequence is already running for this lead
        const existingExecution = this.executions.find(exec => 
          exec.leadId === lead.id && 
          exec.sequenceId === sequence.id && 
          exec.status === 'scheduled'
        );

        if (!existingExecution) {
          qualifyingSequences.push(sequence);
        }
      }
    }

    return qualifyingSequences;
  }

  // Start a follow-up sequence for a lead
  async startSequence(leadId: string, sequenceId: string): Promise<void> {
    const sequence = this.sequences.find(s => s.id === sequenceId);
    if (!sequence) {
      throw new Error(`Sequence ${sequenceId} not found`);
    }

    const lead = await db.getLeadById(leadId);
    if (!lead) {
      throw new Error(`Lead ${leadId} not found`);
    }

    // Schedule all steps in the sequence
    for (const step of sequence.steps) {
      const scheduledAt = new Date(Date.now() + (step.delayHours * 60 * 60 * 1000));
      
      const execution: FollowUpExecution = {
        id: `exec-${Date.now()}-${step.id}`,
        leadId,
        sequenceId,
        stepId: step.id,
        scheduledAt,
        status: 'scheduled'
      };

      this.executions.push(execution);
    }

    console.log(`Started sequence ${sequence.name} for lead ${lead.name}`);
  }

  // Execute a follow-up step
  async executeStep(executionId: string): Promise<void> {
    const execution = this.executions.find(e => e.id === executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const lead = await db.getLeadById(execution.leadId);
    if (!lead) {
      execution.status = 'failed';
      execution.result = { success: false, message: 'Lead not found' };
      return;
    }

    const sequence = this.sequences.find(s => s.id === execution.sequenceId);
    const step = sequence?.steps.find(s => s.id === execution.stepId);
    
    if (!step) {
      execution.status = 'failed';
      execution.result = { success: false, message: 'Step not found' };
      return;
    }

    execution.status = 'executing';
    execution.executedAt = new Date();

    try {
      switch (step.type) {
        case 'voice_call':
          await this.executeVoiceCall(lead, step);
          break;
        case 'sms':
          await this.executeSMS(lead, step);
          break;
        case 'email':
          await this.executeEmail(lead, step);
          break;
        case 'wait':
          // Wait steps are just delays, mark as completed
          break;
      }

      execution.status = 'completed';
      execution.result = { success: true, message: `${step.type} executed successfully` };

      // Update lead's last contacted time
      await db.updateLead(lead.id, {
        last_contacted_at: new Date()
      });

    } catch (error) {
      execution.status = 'failed';
      execution.result = { 
        success: false, 
        message: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Execute voice call step
  private async executeVoiceCall(lead: Lead, step: FollowUpStep): Promise<void> {
    // Get or create assistant
    const assistant = await vapiService.createPressureWashingAssistant();
    
    // Initiate call
    const call = await vapiService.callLead(lead, assistant.id);
    
    // Update lead with call information
    await db.updateLead(lead.id, {
      call_status: 'calling',
      call_attempts: lead.call_attempts + 1,
      vapi_call_id: call.id
    });

    console.log(`Voice call initiated for ${lead.name}: ${call.id}`);
  }

  // Execute SMS step
  private async executeSMS(lead: Lead, step: FollowUpStep): Promise<void> {
    const message = this.personalizeMessage(step.content.message, lead);
    
    // In a real implementation, integrate with SMS service (Twilio, etc.)
    console.log(`SMS sent to ${lead.phone}: ${message}`);
    
    // Mock SMS sending
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Execute email step
  private async executeEmail(lead: Lead, step: FollowUpStep): Promise<void> {
    if (!lead.email) {
      throw new Error('Lead has no email address');
    }

    const subject = this.personalizeMessage(step.content.subject || 'Follow-up', lead);
    const message = this.personalizeMessage(step.content.message, lead);
    
    // In a real implementation, integrate with email service (SendGrid, etc.)
    console.log(`Email sent to ${lead.email}:`);
    console.log(`Subject: ${subject}`);
    console.log(`Message: ${message}`);
    
    // Mock email sending
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Personalize message with lead data
  private personalizeMessage(template: string, lead: Lead): string {
    return template
      .replace(/{name}/g, lead.name)
      .replace(/{phone}/g, lead.phone)
      .replace(/{email}/g, lead.email || '')
      .replace(/{service}/g, lead.service_interest || 'pressure washing')
      .replace(/{property_type}/g, lead.property_type);
  }

  // Process scheduled executions (would be called by a cron job)
  async processScheduledExecutions(): Promise<void> {
    const now = new Date();
    const dueExecutions = this.executions.filter(exec => 
      exec.status === 'scheduled' && exec.scheduledAt <= now
    );

    for (const execution of dueExecutions) {
      try {
        await this.executeStep(execution.id);
      } catch (error) {
        console.error(`Error executing step ${execution.id}:`, error);
      }
    }
  }

  // Get follow-up statistics
  getFollowUpStats(): {
    totalSequences: number;
    activeSequences: number;
    scheduledExecutions: number;
    completedExecutions: number;
    failedExecutions: number;
  } {
    return {
      totalSequences: this.sequences.length,
      activeSequences: this.sequences.filter(s => s.isActive).length,
      scheduledExecutions: this.executions.filter(e => e.status === 'scheduled').length,
      completedExecutions: this.executions.filter(e => e.status === 'completed').length,
      failedExecutions: this.executions.filter(e => e.status === 'failed').length
    };
  }

  // Get executions for a specific lead
  getLeadExecutions(leadId: string): FollowUpExecution[] {
    return this.executions.filter(exec => exec.leadId === leadId);
  }
}

// Export singleton instance
export const followUpService = new FollowUpService();
