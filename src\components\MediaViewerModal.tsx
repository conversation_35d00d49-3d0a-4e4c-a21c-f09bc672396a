/**
 * Media Viewer Modal
 * Full-screen modal for viewing images and videos from Facebook ad creatives
 */

import React, { useState, useEffect } from 'react';
import { X, Download, ExternalLink, ZoomIn, ZoomOut, RotateCw } from 'lucide-react';

interface MediaViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  mediaUrl: string;
  mediaType: 'image' | 'video';
  title?: string;
  downloadUrl?: string;
}

export const MediaViewerModal: React.FC<MediaViewerModalProps> = ({
  isOpen,
  onClose,
  mediaUrl,
  mediaType,
  title,
  downloadUrl
}) => {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isOpen) {
      setZoom(1);
      setRotation(0);
      setIsLoading(true);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleDownload = async () => {
    try {
      const response = await fetch(downloadUrl || mediaUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `facebook-creative-${Date.now()}.${mediaType === 'video' ? 'mp4' : 'jpg'}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback: open in new tab
      window.open(downloadUrl || mediaUrl, '_blank');
    }
  };

  const handleZoomIn = () => setZoom(prev => Math.min(prev + 0.25, 3));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 0.25, 0.25));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/90 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative z-10 max-w-7xl max-h-screen w-full h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gray-900/80 backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <h3 className="text-white font-semibold">
              {title || `${mediaType === 'video' ? 'Video' : 'Image'} Preview`}
            </h3>
            {isLoading && (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-cyan-500 border-t-transparent rounded-full animate-spin" />
                <span className="text-gray-400 text-sm">Loading...</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* Image Controls */}
            {mediaType === 'image' && (
              <>
                <button
                  onClick={handleZoomOut}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                  title="Zoom Out"
                >
                  <ZoomOut size={20} />
                </button>
                <span className="text-gray-400 text-sm min-w-[60px] text-center">
                  {Math.round(zoom * 100)}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                  title="Zoom In"
                >
                  <ZoomIn size={20} />
                </button>
                <button
                  onClick={handleRotate}
                  className="p-2 text-gray-400 hover:text-white transition-colors"
                  title="Rotate"
                >
                  <RotateCw size={20} />
                </button>
              </>
            )}

            {/* Download Button */}
            <button
              onClick={handleDownload}
              className="p-2 text-gray-400 hover:text-white transition-colors"
              title="Download"
            >
              <Download size={20} />
            </button>

            {/* Open in New Tab */}
            <button
              onClick={() => window.open(mediaUrl, '_blank')}
              className="p-2 text-gray-400 hover:text-white transition-colors"
              title="Open in New Tab"
            >
              <ExternalLink size={20} />
            </button>

            {/* Close Button */}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
              title="Close"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Media Content */}
        <div className="flex-1 flex items-center justify-center p-4 overflow-hidden">
          {mediaType === 'image' ? (
            <img
              src={mediaUrl}
              alt={title || 'Facebook ad creative'}
              className="max-w-full max-h-full object-contain transition-transform duration-200"
              style={{
                transform: `scale(${zoom}) rotate(${rotation}deg)`,
                cursor: zoom > 1 ? 'grab' : 'default'
              }}
              onLoad={() => setIsLoading(false)}
              onError={() => setIsLoading(false)}
              draggable={false}
            />
          ) : (
            <video
              src={mediaUrl}
              controls
              className="max-w-full max-h-full"
              onLoadedData={() => setIsLoading(false)}
              onError={() => setIsLoading(false)}
              autoPlay
            >
              Your browser does not support the video tag.
            </video>
          )}
        </div>

        {/* Footer with keyboard shortcuts */}
        <div className="p-2 bg-gray-900/80 backdrop-blur-sm">
          <div className="flex items-center justify-center space-x-6 text-xs text-gray-400">
            <span>ESC to close</span>
            {mediaType === 'image' && (
              <>
                <span>+ / - to zoom</span>
                <span>R to rotate</span>
              </>
            )}
            <span>D to download</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Keyboard shortcuts
export const useMediaViewerKeyboard = (
  isOpen: boolean,
  onClose: () => void,
  onZoomIn?: () => void,
  onZoomOut?: () => void,
  onRotate?: () => void,
  onDownload?: () => void
) => {
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          onClose();
          break;
        case '+':
        case '=':
          event.preventDefault();
          onZoomIn?.();
          break;
        case '-':
          event.preventDefault();
          onZoomOut?.();
          break;
        case 'r':
        case 'R':
          event.preventDefault();
          onRotate?.();
          break;
        case 'd':
        case 'D':
          event.preventDefault();
          onDownload?.();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose, onZoomIn, onZoomOut, onRotate, onDownload]);
};

export default MediaViewerModal;
