/**
 * Supabase Database Service for PressureMax
 * Handles all database operations using Supabase
 */

import { supabase, handleSupabaseError } from '../lib/supabase';
import { 
  AdTemplate, 
  Campaign, 
  Lead, 
  ServiceType, 
  PressureService,
  ServiceTypeWithServices,
  AdTemplateWithRelations,
  CampaignWithTemplate,
  LeadWithCampaign
} from '../types/database';

class SupabaseDatabaseService {
  /**
   * Get all ad templates
   */
  async getAdTemplates(): Promise<AdTemplate[]> {
    try {
      const { data, error } = await supabase
        .from('ad_templates')
        .select('*')
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data.map(this.mapSupabaseToAdTemplate);
    } catch (error) {
      console.error('Error fetching ad templates:', error);
      throw error;
    }
  }

  /**
   * Get ad template by ID
   */
  async getAdTemplate(id: string): Promise<AdTemplate | null> {
    try {
      const { data, error } = await supabase
        .from('ad_templates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToAdTemplate(data);
    } catch (error) {
      console.error('Error fetching ad template:', error);
      throw error;
    }
  }

  /**
   * Create a new ad template
   */
  async createAdTemplate(template: Omit<AdTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<AdTemplate> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Not authenticated');
      }

      const templateData = {
        ...template,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        performance: {
          ...template.performance,
          last_updated: new Date().toISOString()
        }
      };

      const { data, error } = await supabase
        .from('ad_templates')
        .insert(templateData)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToAdTemplate(data);
    } catch (error) {
      console.error('Error creating ad template:', error);
      throw error;
    }
  }

  /**
   * Update an ad template
   */
  async updateAdTemplate(id: string, updates: Partial<AdTemplate>): Promise<AdTemplate> {
    try {
      const { data, error } = await supabase
        .from('ad_templates')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToAdTemplate(data);
    } catch (error) {
      console.error('Error updating ad template:', error);
      throw error;
    }
  }

  /**
   * Update an ad template
   */
  async updateAdTemplate(id: string, template: Partial<AdTemplate>): Promise<AdTemplate> {
    try {
      const templateData = {
        ...template,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('ad_templates')
        .update(templateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToAdTemplate(data);
    } catch (error) {
      console.error('Error updating ad template:', error);
      throw error;
    }
  }

  /**
   * Delete an ad template
   */
  async deleteAdTemplate(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('ad_templates')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(handleSupabaseError(error));
      }
    } catch (error) {
      console.error('Error deleting ad template:', error);
      throw error;
    }
  }

  /**
   * Get all campaigns
   */
  async getCampaigns(): Promise<Campaign[]> {
    try {
      const { data, error } = await supabase
        .from('campaigns')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data.map(this.mapSupabaseToCampaign);
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      throw error;
    }
  }

  /**
   * Create a new campaign
   */
  async createCampaign(campaign: Omit<Campaign, 'id' | 'created_at' | 'updated_at'>): Promise<Campaign> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Not authenticated');
      }

      const campaignData = {
        ...campaign,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        metrics: {
          ...campaign.metrics,
          last_sync: new Date().toISOString()
        }
      };

      const { data, error } = await supabase
        .from('campaigns')
        .insert(campaignData)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToCampaign(data);
    } catch (error) {
      console.error('Error creating campaign:', error);
      throw error;
    }
  }

  /**
   * Update an existing campaign
   */
  async updateCampaign(id: string, campaign: Partial<Campaign>): Promise<Campaign> {
    try {
      const campaignData = {
        ...campaign,
        updated_at: new Date().toISOString(),
        metrics: campaign.metrics ? {
          ...campaign.metrics,
          last_sync: new Date().toISOString()
        } : undefined
      };

      const { data, error } = await supabase
        .from('campaigns')
        .update(campaignData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToCampaign(data);
    } catch (error) {
      console.error('Error updating campaign:', error);
      throw error;
    }
  }

  /**
   * Get all leads
   */
  async getLeads(): Promise<Lead[]> {
    try {
      const { data, error } = await supabase
        .from('leads')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data.map(this.mapSupabaseToLead);
    } catch (error) {
      console.error('Error fetching leads:', error);
      throw error;
    }
  }

  /**
   * Create a new lead
   */
  async createLead(lead: Omit<Lead, 'id' | 'created_at' | 'updated_at'>): Promise<Lead> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Not authenticated');
      }

      const leadData = {
        ...lead,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('leads')
        .insert(leadData)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToLead(data);
    } catch (error) {
      console.error('Error creating lead:', error);
      throw error;
    }
  }

  /**
   * Update a lead
   */
  async updateLead(id: string, updates: Partial<Lead>): Promise<Lead> {
    try {
      const { data, error } = await supabase
        .from('leads')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return this.mapSupabaseToLead(data);
    } catch (error) {
      console.error('Error updating lead:', error);
      throw error;
    }
  }

  /**
   * Get service types with services
   */
  async getServiceTypes(): Promise<ServiceTypeWithServices[]> {
    try {
      const { data, error } = await supabase
        .from('service_types')
        .select(`
          *,
          pressure_services (*)
        `)
        .order('sort_order');

      if (error) {
        throw new Error(handleSupabaseError(error));
      }

      return data.map(item => ({
        ...this.mapSupabaseToServiceType(item),
        services: item.pressure_services.map(this.mapSupabaseToPressureService)
      }));
    } catch (error) {
      console.error('Error fetching service types:', error);
      throw error;
    }
  }

  /**
   * Map Supabase data to AdTemplate
   */
  private mapSupabaseToAdTemplate(data: any): AdTemplate {
    return {
      id: data.id,
      name: data.name,
      service_type_id: data.service_type_id,
      pressure_service_id: data.pressure_service_id,
      category: data.category,
      service: data.service,
      creative: data.creative,
      targeting: data.targeting,
      budget_range: data.budget_range,
      seasonal_timing: data.seasonal_timing,
      target_customer: data.target_customer,
      pricing_strategy: data.pricing_strategy,
      performance: {
        ...data.performance,
        last_updated: new Date(data.performance.last_updated)
      },
      template_type: data.template_type,
      is_public: data.is_public,
      parent_template_id: data.parent_template_id,
      status: data.status,
      is_featured: data.is_featured,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      created_by: data.created_by
    };
  }

  /**
   * Map Supabase data to Campaign
   */
  private mapSupabaseToCampaign(data: any): Campaign {
    return {
      id: data.id,
      template_id: data.template_id,
      name: data.name,
      facebook_campaign_id: data.facebook_campaign_id,
      budget: data.budget,
      start_date: new Date(data.start_date),
      end_date: data.end_date ? new Date(data.end_date) : undefined,
      custom_creative: data.custom_creative,
      custom_targeting: data.custom_targeting,
      metrics: {
        ...data.metrics,
        last_sync: new Date(data.metrics.last_sync)
      },
      status: data.status,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      launched_at: data.launched_at ? new Date(data.launched_at) : undefined
    };
  }

  /**
   * Map Supabase data to Lead
   */
  private mapSupabaseToLead(data: any): Lead {
    return {
      id: data.id,
      campaign_id: data.campaign_id,
      source: data.source,
      name: data.name,
      phone: data.phone,
      email: data.email,
      address: data.address,
      service_interest: data.service_interest,
      budget_range: data.budget_range,
      urgency: data.urgency,
      property_type: data.property_type,
      score: data.score,
      quality: data.quality,
      vapi_call_id: data.vapi_call_id,
      call_status: data.call_status,
      call_attempts: data.call_attempts,
      last_call_at: data.last_call_at ? new Date(data.last_call_at) : undefined,
      next_call_at: data.next_call_at ? new Date(data.next_call_at) : undefined,
      appointment_scheduled: data.appointment_scheduled,
      appointment_date: data.appointment_date ? new Date(data.appointment_date) : undefined,
      appointment_notes: data.appointment_notes,
      status: data.status,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at),
      last_contacted_at: data.last_contacted_at ? new Date(data.last_contacted_at) : undefined,
      notes: data.notes
    };
  }

  /**
   * Map Supabase data to ServiceType
   */
  private mapSupabaseToServiceType(data: any): ServiceType {
    return {
      id: data.id,
      name: data.name,
      icon: data.icon,
      color: data.color,
      sort_order: data.sort_order,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at)
    };
  }

  /**
   * Map Supabase data to PressureService
   */
  private mapSupabaseToPressureService(data: any): PressureService {
    return {
      id: data.id,
      service_type_id: data.service_type_id,
      name: data.name,
      description: data.description,
      typical_pricing: data.typical_pricing,
      season_preference: data.season_preference,
      equipment_needed: data.equipment_needed,
      sort_order: data.sort_order,
      created_at: new Date(data.created_at),
      updated_at: new Date(data.updated_at)
    };
  }
}

// Export singleton instance
export const supabaseDb = new SupabaseDatabaseService();
