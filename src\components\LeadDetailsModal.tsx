import React, { useState } from 'react';
import { X, Phone, Mail, MapPin, Calendar, User, Building, DollarSign, Clock, Star } from 'lucide-react';
import { Lead } from '../types/database';
import { VAPICallManager } from './VAPICallManager';
import { db } from '../services/database';
import { followUpService } from '../services/followUp';
import { AppointmentBooking } from './AppointmentBooking';
import { calendarService } from '../services/calendar';

interface LeadDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  lead: Lead | null;
  onLeadUpdate?: (lead: Lead) => void;
}

export const LeadDetailsModal: React.FC<LeadDetailsModalProps> = ({
  isOpen,
  onClose,
  lead,
  onLeadUpdate
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedLead, setEditedLead] = useState<Lead | null>(null);

  React.useEffect(() => {
    if (lead) {
      setEditedLead({ ...lead });
    }
  }, [lead]);

  const handleSave = async () => {
    if (!editedLead) return;

    try {
      const updated = await db.updateLead(editedLead.id, editedLead);
      if (updated && onLeadUpdate) {
        onLeadUpdate(updated);
      }
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating lead:', error);
    }
  };

  const handleCallComplete = async () => {
    // Refresh lead data after call completion
    if (lead) {
      try {
        const updated = await db.getLeadById(lead.id);
        if (updated && onLeadUpdate) {
          onLeadUpdate(updated);
        }
      } catch (error) {
        console.error('Error refreshing lead:', error);
      }
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'hot': return 'text-red-400 bg-red-500/20';
      case 'warm': return 'text-yellow-400 bg-yellow-500/20';
      case 'cold': return 'text-blue-400 bg-blue-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'text-cyan-400 bg-cyan-500/20';
      case 'contacted': return 'text-yellow-400 bg-yellow-500/20';
      case 'qualified': return 'text-green-400 bg-green-500/20';
      case 'appointment': return 'text-purple-400 bg-purple-500/20';
      case 'converted': return 'text-emerald-400 bg-emerald-500/20';
      case 'lost': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  if (!isOpen || !lead || !editedLead) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-black border border-cyan-500/30 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <h2 className="text-2xl font-bold text-white">Lead Details</h2>
              <div className="flex space-x-2">
                <span className={`px-3 py-1 text-xs font-bold ${getQualityColor(lead.quality)}`}>
                  {lead.quality.toUpperCase()}
                </span>
                <span className={`px-3 py-1 text-xs font-bold ${getStatusColor(lead.status)}`}>
                  {lead.status.toUpperCase()}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="px-4 py-2 border border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-black transition-colors"
              >
                {isEditing ? 'Cancel' : 'Edit'}
              </button>
              {isEditing && (
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
                >
                  Save
                </button>
              )}
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <X size={24} />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Lead Information */}
            <div className="space-y-6">
              {/* Contact Info */}
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Contact Information</h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <User className="text-gray-400" size={16} />
                    {isEditing ? (
                      <input
                        type="text"
                        value={editedLead.name}
                        onChange={(e) => setEditedLead(prev => prev ? { ...prev, name: e.target.value } : null)}
                        className="bg-gray-800 border border-gray-600 text-white px-2 py-1 flex-1"
                      />
                    ) : (
                      <span className="text-white">{lead.name}</span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Phone className="text-gray-400" size={16} />
                    {isEditing ? (
                      <input
                        type="tel"
                        value={editedLead.phone}
                        onChange={(e) => setEditedLead(prev => prev ? { ...prev, phone: e.target.value } : null)}
                        className="bg-gray-800 border border-gray-600 text-white px-2 py-1 flex-1"
                      />
                    ) : (
                      <span className="text-white">{lead.phone}</span>
                    )}
                  </div>
                  
                  {lead.email && (
                    <div className="flex items-center space-x-3">
                      <Mail className="text-gray-400" size={16} />
                      {isEditing ? (
                        <input
                          type="email"
                          value={editedLead.email || ''}
                          onChange={(e) => setEditedLead(prev => prev ? { ...prev, email: e.target.value } : null)}
                          className="bg-gray-800 border border-gray-600 text-white px-2 py-1 flex-1"
                        />
                      ) : (
                        <span className="text-white">{lead.email}</span>
                      )}
                    </div>
                  )}
                  
                  {lead.address && (
                    <div className="flex items-center space-x-3">
                      <MapPin className="text-gray-400" size={16} />
                      {isEditing ? (
                        <input
                          type="text"
                          value={editedLead.address || ''}
                          onChange={(e) => setEditedLead(prev => prev ? { ...prev, address: e.target.value } : null)}
                          className="bg-gray-800 border border-gray-600 text-white px-2 py-1 flex-1"
                        />
                      ) : (
                        <span className="text-white">{lead.address}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Service Details */}
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Service Details</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Service Interest:</span>
                    <span className="text-white">{lead.service_interest || 'Not specified'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Budget Range:</span>
                    <span className="text-white">{lead.budget_range || 'Not specified'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Urgency:</span>
                    <span className="text-white capitalize">{lead.urgency.replace('_', ' ')}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Property Type:</span>
                    <span className="text-white capitalize">{lead.property_type}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Lead Score:</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-white">{lead.score}/100</span>
                      <Star className="text-yellow-400" size={16} />
                    </div>
                  </div>
                </div>
              </div>

              {/* Timeline */}
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Timeline</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Created:</span>
                    <span className="text-white">{new Date(lead.created_at).toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-400">Last Updated:</span>
                    <span className="text-white">{new Date(lead.updated_at).toLocaleString()}</span>
                  </div>
                  {lead.last_contacted_at && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Last Contacted:</span>
                      <span className="text-white">{new Date(lead.last_contacted_at).toLocaleString()}</span>
                    </div>
                  )}
                  {lead.appointment_date && (
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Appointment:</span>
                      <span className="text-green-400">{new Date(lead.appointment_date).toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Notes */}
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Notes</h3>
                {isEditing ? (
                  <textarea
                    value={editedLead.notes}
                    onChange={(e) => setEditedLead(prev => prev ? { ...prev, notes: e.target.value } : null)}
                    rows={4}
                    className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2"
                    placeholder="Add notes about this lead..."
                  />
                ) : (
                  <div className="text-gray-300 whitespace-pre-wrap">
                    {lead.notes || 'No notes yet.'}
                  </div>
                )}
              </div>
            </div>

            {/* VAPI Call Management */}
            <div className="space-y-6">
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">AI Call Management</h3>
                <VAPICallManager
                  lead={lead}
                  onCallComplete={handleCallComplete}
                />
              </div>

              {/* Follow-up Automation */}
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Follow-up Automation</h3>
                <div className="space-y-4">
                  <div className="text-sm">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-400">Active Sequences:</span>
                      <span className="text-cyan-400">{followUpService.getLeadExecutions(lead.id).length}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-400">Next Follow-up:</span>
                      <span className="text-white">
                        {lead.next_call_at ? new Date(lead.next_call_at).toLocaleDateString() : 'Not scheduled'}
                      </span>
                    </div>
                  </div>

                  <button
                    onClick={async () => {
                      const sequences = await followUpService.checkLeadForSequences(lead);
                      if (sequences.length > 0) {
                        await followUpService.startSequence(lead.id, sequences[0].id);
                        console.log('Follow-up sequence started');
                      } else {
                        console.log('No qualifying sequences found');
                      }
                    }}
                    className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white py-2 px-4 font-bold hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
                  >
                    Start Follow-up Sequence
                  </button>

                  <div className="text-xs text-gray-500">
                    <p>Automated follow-up includes:</p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Voice calls with AI assistant</li>
                      <li>Personalized SMS messages</li>
                      <li>Email nurture campaigns</li>
                      <li>Reactivation sequences</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Appointment Booking */}
              <div className="bg-gray-900 border border-gray-700 p-4">
                <h3 className="text-lg font-semibold text-white mb-4">Schedule Appointment</h3>
                {lead.appointment_scheduled ? (
                  <div className="space-y-4">
                    <div className="bg-green-500/10 border border-green-500/30 p-4">
                      <div className="flex items-center space-x-2 mb-2">
                        <Calendar className="text-green-400" size={16} />
                        <span className="text-green-400 font-semibold">Appointment Scheduled</span>
                      </div>
                      {lead.appointment_date && (
                        <div className="text-sm text-gray-300">
                          <p>{new Date(lead.appointment_date).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}</p>
                          <p>{new Date(lead.appointment_date).toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                          })}</p>
                        </div>
                      )}
                      {lead.appointment_notes && (
                        <p className="text-xs text-gray-400 mt-2">{lead.appointment_notes}</p>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={async () => {
                          const appointments = calendarService.getLeadAppointments(lead.id);
                          if (appointments.length > 0) {
                            await calendarService.completeAppointment(appointments[0].id, 'Estimate completed');
                            if (onLeadUpdate) {
                              const updated = await db.updateLead(lead.id, { status: 'converted' });
                              if (updated) onLeadUpdate(updated);
                            }
                          }
                        }}
                        className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 text-sm font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300"
                      >
                        Mark Completed
                      </button>
                      <button
                        onClick={async () => {
                          const appointments = calendarService.getLeadAppointments(lead.id);
                          if (appointments.length > 0) {
                            await calendarService.cancelAppointment(appointments[0].id, 'Customer cancelled');
                            if (onLeadUpdate) {
                              const updated = await db.updateLead(lead.id, {
                                appointment_scheduled: false,
                                appointment_date: undefined,
                                status: 'contacted'
                              });
                              if (updated) onLeadUpdate(updated);
                            }
                          }
                        }}
                        className="flex-1 border border-red-500 text-red-400 py-2 px-4 text-sm font-bold hover:bg-red-500 hover:text-white transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <AppointmentBooking
                    lead={lead}
                    onAppointmentBooked={async (appointment) => {
                      console.log('Appointment booked:', appointment);
                      if (onLeadUpdate) {
                        const updated = await db.getLeadById(lead.id);
                        if (updated) onLeadUpdate(updated);
                      }
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
