/**
 * Admin Template Manager for PressureMax
 * Allows admins to create, edit, and manage global ad templates
 */

import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Copy, 
  Star, 
  Globe, 
  Lock, 
  Save, 
  X,
  Upload,
  Image as ImageIcon,
  Target,
  DollarSign,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { AdTemplate, ServiceType, PressureService } from '../types/database';
import { useAuth } from '../contexts/AuthContext';
import { useAdminAuth } from '../services/adminAuth';
import { db } from '../services/database';

interface AdminTemplateManagerProps {
  className?: string;
}

export const AdminTemplateManager: React.FC<AdminTemplateManagerProps> = ({
  className = ''
}) => {
  const { user } = useAuth();
  const { hasPermission, logAction } = useAdminAuth();
  const [templates, setTemplates] = useState<AdTemplate[]>([]);
  const [serviceTypes, setServiceTypes] = useState<ServiceType[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<AdTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [filter, setFilter] = useState({
    status: 'all',
    category: 'all',
    service: 'all'
  });

  // Template form data
  const [templateForm, setTemplateForm] = useState({
    name: '',
    service_type_id: '',
    pressure_service_id: '',
    category: 'Residential',
    service: '',
    creative: {
      headline: '',
      primary_text: '',
      description: '',
      call_to_action: 'LEARN_MORE',
      image_path: ''
    },
    targeting: {
      location_type: 'local',
      location_radius: '25_miles',
      age_range: { min: 30, max: 65 },
      home_value_range: '$200k_plus',
      interests: ['home_improvement', 'property_maintenance'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: {
      min: 20,
      max: 100,
      suggested: 50
    },
    seasonal_timing: ['spring', 'summer'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    status: 'draft' as const,
    is_featured: false,
    template_type: 'global' as const,
    is_public: true
  });

  useEffect(() => {
    loadTemplates();
    loadServiceTypes();
  }, []);

  const loadTemplates = async () => {
    try {
      const allTemplates = await db.getAdTemplates();
      // Filter to show only global templates for admin management
      const globalTemplates = allTemplates.filter(t => t.template_type === 'global');
      setTemplates(globalTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  };

  const loadServiceTypes = async () => {
    try {
      const types = await db.getServiceTypes();
      setServiceTypes(types);
    } catch (error) {
      console.error('Error loading service types:', error);
    }
  };

  const createTemplate = async () => {
    if (!hasPermission(user, 'template_create')) {
      alert('You do not have permission to create templates');
      return;
    }

    setIsLoading(true);
    try {
      const newTemplate: AdTemplate = {
        id: `template_${Date.now()}`,
        ...templateForm,
        performance: {
          ctr: '0%',
          cpl: '$0',
          conversions: 0,
          total_spend: 0,
          total_leads: 0,
          last_updated: new Date()
        },
        created_at: new Date(),
        updated_at: new Date(),
        created_by: user?.id || 'admin'
      };

      // Save template
      await db.createAdTemplate(newTemplate);
      setTemplates(prev => [...prev, newTemplate]);
      
      // Log admin action
      await logAction(user?.id || '', 'template_created', {
        templateId: newTemplate.id,
        templateName: newTemplate.name
      });

      // Reset form and close modal
      resetForm();
      setIsCreateModalOpen(false);
      
      alert('Global template created successfully!');
    } catch (error) {
      console.error('Error creating template:', error);
      alert('Failed to create template');
    } finally {
      setIsLoading(false);
    }
  };

  const updateTemplate = async () => {
    if (!hasPermission(user, 'template_edit') || !editingTemplate) {
      alert('You do not have permission to edit templates');
      return;
    }

    setIsLoading(true);
    try {
      const updatedTemplate: AdTemplate = {
        ...editingTemplate,
        ...templateForm,
        updated_at: new Date()
      };

      // Update template
      await db.updateAdTemplate(updatedTemplate.id, updatedTemplate);
      setTemplates(prev => prev.map(t => t.id === updatedTemplate.id ? updatedTemplate : t));
      
      // Log admin action
      await logAction(user?.id || '', 'template_updated', {
        templateId: updatedTemplate.id,
        templateName: updatedTemplate.name
      });

      // Reset form and close modal
      resetForm();
      setEditingTemplate(null);
      
      alert('Template updated successfully!');
    } catch (error) {
      console.error('Error updating template:', error);
      alert('Failed to update template');
    } finally {
      setIsLoading(false);
    }
  };

  const deleteTemplate = async (templateId: string) => {
    if (!hasPermission(user, 'template_delete')) {
      alert('You do not have permission to delete templates');
      return;
    }

    if (!confirm('Are you sure you want to delete this global template? This action cannot be undone.')) {
      return;
    }

    try {
      await db.deleteAdTemplate(templateId);
      setTemplates(prev => prev.filter(t => t.id !== templateId));
      
      // Log admin action
      await logAction(user?.id || '', 'template_deleted', {
        templateId
      });
      
      alert('Template deleted successfully!');
    } catch (error) {
      console.error('Error deleting template:', error);
      alert('Failed to delete template');
    }
  };

  const toggleFeatured = async (templateId: string) => {
    if (!hasPermission(user, 'template_edit')) return;

    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) return;

      const updatedTemplate = { ...template, is_featured: !template.is_featured };
      await db.updateAdTemplate(templateId, updatedTemplate);
      setTemplates(prev => prev.map(t => t.id === templateId ? updatedTemplate : t));
      
      await logAction(user?.id || '', 'template_featured_toggled', {
        templateId,
        featured: updatedTemplate.is_featured
      });
    } catch (error) {
      console.error('Error toggling featured status:', error);
    }
  };

  const publishTemplate = async (templateId: string) => {
    if (!hasPermission(user, 'template_publish')) {
      alert('You do not have permission to publish templates');
      return;
    }

    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) return;

      const updatedTemplate = { ...template, status: 'active' as const };
      await db.updateAdTemplate(templateId, updatedTemplate);
      setTemplates(prev => prev.map(t => t.id === templateId ? updatedTemplate : t));
      
      await logAction(user?.id || '', 'template_published', {
        templateId,
        templateName: template.name
      });
      
      alert('Template published successfully!');
    } catch (error) {
      console.error('Error publishing template:', error);
      alert('Failed to publish template');
    }
  };

  const resetForm = () => {
    setTemplateForm({
      name: '',
      service_type_id: '',
      pressure_service_id: '',
      category: 'Residential',
      service: '',
      creative: {
        headline: '',
        primary_text: '',
        description: '',
        call_to_action: 'LEARN_MORE',
        image_path: ''
      },
      targeting: {
        location_type: 'local',
        location_radius: '25_miles',
        age_range: { min: 30, max: 65 },
        home_value_range: '$200k_plus',
        interests: ['home_improvement', 'property_maintenance'],
        exclude_competitors: true,
        custom_audiences: [],
        lookalike_audiences: []
      },
      budget_range: {
        min: 20,
        max: 100,
        suggested: 50
      },
      seasonal_timing: ['spring', 'summer'],
      target_customer: 'homeowner',
      pricing_strategy: 'competitive',
      status: 'draft',
      is_featured: false,
      template_type: 'global',
      is_public: true
    });
  };

  const startEdit = (template: AdTemplate) => {
    setEditingTemplate(template);
    setTemplateForm({
      name: template.name,
      service_type_id: template.service_type_id,
      pressure_service_id: template.pressure_service_id,
      category: template.category,
      service: template.service,
      creative: template.creative,
      targeting: template.targeting,
      budget_range: template.budget_range,
      seasonal_timing: template.seasonal_timing,
      target_customer: template.target_customer,
      pricing_strategy: template.pricing_strategy,
      status: template.status,
      is_featured: template.is_featured,
      template_type: template.template_type,
      is_public: template.is_public
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'draft': return 'text-yellow-400';
      case 'paused': return 'text-orange-400';
      case 'archived': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const filteredTemplates = templates.filter(template => {
    if (filter.status !== 'all' && template.status !== filter.status) return false;
    if (filter.category !== 'all' && template.category !== filter.category) return false;
    if (filter.service !== 'all' && template.service !== filter.service) return false;
    return true;
  });

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Global Template Library</h2>
          <p className="text-gray-400">Create and manage ad templates for all users</p>
        </div>
        
        {hasPermission(user, 'template_create') && (
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-4 py-2 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>Create Global Template</span>
          </button>
        )}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Total Templates</h3>
          <p className="text-2xl font-bold text-white">{templates.length}</p>
        </div>
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Active</h3>
          <p className="text-2xl font-bold text-green-400">
            {templates.filter(t => t.status === 'active').length}
          </p>
        </div>
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Featured</h3>
          <p className="text-2xl font-bold text-yellow-400">
            {templates.filter(t => t.is_featured).length}
          </p>
        </div>
        <div className="bg-gray-800 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Draft</h3>
          <p className="text-2xl font-bold text-orange-400">
            {templates.filter(t => t.status === 'draft').length}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-800 border border-gray-700 p-4">
        <div className="flex items-center space-x-4">
          <select
            value={filter.status}
            onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value }))}
            className="bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="draft">Draft</option>
            <option value="paused">Paused</option>
            <option value="archived">Archived</option>
          </select>
          
          <select
            value={filter.category}
            onChange={(e) => setFilter(prev => ({ ...prev, category: e.target.value }))}
            className="bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
          >
            <option value="all">All Categories</option>
            <option value="Residential">Residential</option>
            <option value="Commercial">Commercial</option>
            <option value="Specialty">Specialty</option>
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <div key={template.id} className="bg-gray-800 border border-gray-700 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className="text-lg font-semibold text-white">{template.name}</h3>
                  {template.is_featured && <Star className="text-yellow-400" size={16} />}
                  <Globe className="text-cyan-400" size={16} title="Global Template" />
                </div>
                <p className="text-sm text-gray-400 capitalize">{template.category} • {template.service}</p>
              </div>
              <span className={`text-sm font-medium capitalize ${getStatusColor(template.status)}`}>
                {template.status}
              </span>
            </div>

            <div className="space-y-2 mb-4">
              <p className="text-sm text-gray-300">{template.creative.headline}</p>
              <p className="text-xs text-gray-500">{template.creative.primary_text.substring(0, 100)}...</p>
            </div>

            <div className="flex items-center justify-between text-xs text-gray-400 mb-4">
              <span>Budget: ${template.budget_range.min}-${template.budget_range.max}</span>
              <span>CTR: {template.performance.ctr}</span>
            </div>

            <div className="flex items-center space-x-2">
              {hasPermission(user, 'template_edit') && (
                <button
                  onClick={() => startEdit(template)}
                  className="p-2 bg-blue-600 hover:bg-blue-700 text-white transition-colors"
                  title="Edit Template"
                >
                  <Edit size={14} />
                </button>
              )}
              
              <button
                onClick={() => toggleFeatured(template.id)}
                className={`p-2 transition-colors ${
                  template.is_featured 
                    ? 'bg-yellow-600 hover:bg-yellow-700' 
                    : 'bg-gray-600 hover:bg-gray-700'
                } text-white`}
                title="Toggle Featured"
              >
                <Star size={14} />
              </button>
              
              {template.status === 'draft' && hasPermission(user, 'template_publish') && (
                <button
                  onClick={() => publishTemplate(template.id)}
                  className="p-2 bg-green-600 hover:bg-green-700 text-white transition-colors"
                  title="Publish Template"
                >
                  <Globe size={14} />
                </button>
              )}
              
              {hasPermission(user, 'template_delete') && (
                <button
                  onClick={() => deleteTemplate(template.id)}
                  className="p-2 bg-red-600 hover:bg-red-700 text-white transition-colors"
                  title="Delete Template"
                >
                  <Trash2 size={14} />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Globe className="mx-auto text-gray-500 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No global templates found</h3>
          <p className="text-gray-500">Create your first global template to get started</p>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(isCreateModalOpen || editingTemplate) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold text-white">
                {editingTemplate ? 'Edit Global Template' : 'Create Global Template'}
              </h2>
              <button
                onClick={() => {
                  setIsCreateModalOpen(false);
                  setEditingTemplate(null);
                  resetForm();
                }}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X size={24} />
              </button>
            </div>

            <div className="p-6">
              <div className="text-center py-12">
                <Plus className="mx-auto text-gray-500 mb-4" size={48} />
                <h3 className="text-lg font-medium text-gray-400 mb-2">Template creation form</h3>
                <p className="text-gray-500">Full template creation/editing form will be implemented here</p>
                
                <div className="flex items-center justify-center space-x-4 mt-6">
                  <button
                    onClick={() => {
                      setIsCreateModalOpen(false);
                      setEditingTemplate(null);
                      resetForm();
                    }}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={editingTemplate ? updateTemplate : createTemplate}
                    disabled={isLoading}
                    className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-4 py-2 font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 disabled:opacity-50"
                  >
                    {isLoading ? 'Saving...' : editingTemplate ? 'Update Template' : 'Create Template'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
