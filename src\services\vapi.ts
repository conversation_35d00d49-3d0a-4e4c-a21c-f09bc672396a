// VAPI Integration Service for AI Voice Calling
import { Lead } from '../types/database';

export interface VAPIAssistant {
  id: string;
  name: string;
  model: {
    provider: string;
    model: string;
    messages: Array<{
      role: string;
      content: string;
    }>;
  };
  voice: {
    provider: string;
    voiceId: string;
  };
  firstMessage: string;
  recordingEnabled: boolean;
  endCallMessage: string;
  endCallPhrases: string[];
  maxDurationSeconds: number;
}

export interface VAPICall {
  id: string;
  assistantId: string;
  phoneNumber: string;
  status: 'queued' | 'ringing' | 'in-progress' | 'forwarding' | 'ended';
  startedAt?: string;
  endedAt?: string;
  cost?: number;
  transcript?: string;
  summary?: string;
  analysis?: {
    sentiment: 'positive' | 'neutral' | 'negative';
    intent: string;
    appointmentScheduled: boolean;
    followUpNeeded: boolean;
    leadQuality: 'hot' | 'warm' | 'cold';
  };
}

export interface VAPICallRequest {
  assistantId: string;
  phoneNumber: string;
  customerName?: string;
  metadata?: {
    leadId: string;
    serviceInterest: string;
    urgency: string;
    propertyType: string;
  };
}

export class VAPIService {
  private apiKey: string;
  private baseUrl: string = 'https://api.vapi.ai';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  // Helper method to make API requests
  private async makeRequest(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any) {
    const url = `${this.baseUrl}${endpoint}`;
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(`VAPI Error: ${result.message || 'Unknown error'}`);
      }
      
      return result;
    } catch (error) {
      console.error('VAPI request failed:', error);
      throw error;
    }
  }

  // Create or get the pressure washing assistant with appointment booking
  async createPressureWashingAssistant(): Promise<VAPIAssistant> {
    const assistantData: Omit<VAPIAssistant, 'id'> = {
      name: 'Sarah - PressureMax Appointment Assistant',
      model: {
        provider: 'openai',
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: `You are Sarah, a highly efficient appointment scheduling assistant for PressureMax, a professional pressure washing company. Your primary goal is to book appointments for free estimates while providing excellent customer service.

WORKFLOW:
1. **Greeting & Verification**: Confirm you're speaking with the right person about their pressure washing inquiry
2. **Qualify Interest**: Confirm they're still interested in pressure washing services
3. **Gather Service Details**: Ask about specific services needed and property details
4. **Schedule Appointment**: Use the booking tools to check availability and schedule their free estimate
5. **Confirm Details**: Verify all appointment information before ending the call

KEY INFORMATION TO COLLECT:
- Service type needed (house washing, driveway cleaning, deck restoration, roof cleaning, commercial building)
- Property type and size (residential/commercial, square footage if possible)
- Urgency level (ASAP, this week, this month, flexible)
- Preferred appointment times
- Contact verification (name, phone, email if available)
- Property address for the estimate

CONVERSATION GUIDELINES:
- Keep responses under 35 words when possible
- Be warm, professional, and efficient
- Listen actively and ask follow-up questions
- Emphasize FREE estimates with no obligation
- Address pricing concerns by highlighting the free estimate
- If they're not ready now, offer to call back at a better time
- Always try to schedule a specific appointment time

OBJECTION HANDLING:
- "Too expensive": "That's exactly why we offer free estimates - so you know the exact cost with no surprises or pressure."
- "Need to think about it": "I understand completely. What specific concerns can I help address? Our estimates are always free."
- "Not the right time": "When would work better for you? I can schedule a callback or future appointment."
- "Already have someone": "That's great! If you ever need a second opinion or backup service, we're here to help."

APPOINTMENT BOOKING:
- Use the checkAvailability tool to find open slots
- Offer 2-3 time options when possible
- Confirm all details before booking
- Use the bookAppointment tool to finalize the schedule
- Provide confirmation details

END CALL PROFESSIONALLY:
- Confirm appointment details if scheduled
- Provide company contact information
- Thank them for their time
- Keep calls under 10 minutes total

Remember: Your success is measured by appointments booked and customer satisfaction. Be helpful, efficient, and always professional.`
          }
        ]
      },
      voice: {
        provider: 'elevenlabs',
        voiceId: 'sarah' // Professional female voice
      },
      firstMessage: "Hi! This is Sarah calling from PressureMax about your interest in pressure washing services. I have just a few quick questions to help schedule your free estimate. Do you have a couple minutes to chat?",
      recordingEnabled: true,
      endCallMessage: "Perfect! Your free estimate is confirmed. You'll receive a confirmation text shortly. If you have any questions before then, feel free to call us at any time. Thank you and have a great day!",
      endCallPhrases: ["goodbye", "bye", "talk to you later", "have a good day", "not interested", "no thank you"],
      maxDurationSeconds: 600 // 10 minutes max
    };

    return await this.makeRequest('/assistant', 'POST', assistantData);
  }

  // Initiate a call to a lead
  async callLead(lead: Lead, assistantId: string): Promise<VAPICall> {
    const callRequest: VAPICallRequest = {
      assistantId,
      phoneNumber: lead.phone,
      customerName: lead.name,
      metadata: {
        leadId: lead.id,
        serviceInterest: lead.service_interest,
        urgency: lead.urgency,
        propertyType: lead.property_type
      }
    };

    return await this.makeRequest('/call', 'POST', callRequest);
  }

  // Get call details and transcript
  async getCall(callId: string): Promise<VAPICall> {
    return await this.makeRequest(`/call/${callId}`);
  }

  // Get all calls for an assistant
  async getCalls(assistantId?: string): Promise<VAPICall[]> {
    const endpoint = assistantId ? `/call?assistantId=${assistantId}` : '/call';
    const response = await this.makeRequest(endpoint);
    return response.data || [];
  }

  // Analyze call outcome and update lead
  async analyzeCallOutcome(callId: string): Promise<VAPICall['analysis']> {
    const call = await this.getCall(callId);
    
    if (!call.transcript) {
      return {
        sentiment: 'neutral',
        intent: 'unknown',
        appointmentScheduled: false,
        followUpNeeded: true,
        leadQuality: 'cold'
      };
    }

    // Simple analysis based on transcript keywords
    const transcript = call.transcript.toLowerCase();
    
    // Sentiment analysis
    const positiveWords = ['yes', 'interested', 'sounds good', 'perfect', 'great', 'awesome'];
    const negativeWords = ['no', 'not interested', 'busy', 'expensive', 'can\'t afford'];
    
    const positiveCount = positiveWords.filter(word => transcript.includes(word)).length;
    const negativeCount = negativeWords.filter(word => transcript.includes(word)).length;
    
    let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
    if (positiveCount > negativeCount) sentiment = 'positive';
    else if (negativeCount > positiveCount) sentiment = 'negative';

    // Intent detection
    let intent = 'information_gathering';
    if (transcript.includes('schedule') || transcript.includes('appointment')) {
      intent = 'appointment_scheduling';
    } else if (transcript.includes('price') || transcript.includes('cost') || transcript.includes('estimate')) {
      intent = 'pricing_inquiry';
    } else if (transcript.includes('not interested') || transcript.includes('no thank')) {
      intent = 'rejection';
    }

    // Appointment detection
    const appointmentScheduled = transcript.includes('schedule') && 
                                (transcript.includes('tomorrow') || 
                                 transcript.includes('monday') || 
                                 transcript.includes('tuesday') || 
                                 transcript.includes('wednesday') || 
                                 transcript.includes('thursday') || 
                                 transcript.includes('friday') ||
                                 transcript.includes('time'));

    // Follow-up needed
    const followUpNeeded = !appointmentScheduled && sentiment !== 'negative';

    // Lead quality assessment
    let leadQuality: 'hot' | 'warm' | 'cold' = 'cold';
    if (appointmentScheduled || (sentiment === 'positive' && intent === 'appointment_scheduling')) {
      leadQuality = 'hot';
    } else if (sentiment === 'positive' || intent === 'pricing_inquiry') {
      leadQuality = 'warm';
    }

    return {
      sentiment,
      intent,
      appointmentScheduled,
      followUpNeeded,
      leadQuality
    };
  }

  // Create assistant with appointment booking tools
  async createAppointmentBookingAssistant(): Promise<VAPIAssistant> {
    const assistantData = {
      name: 'Sarah - PressureMax Appointment Booking Assistant',
      model: {
        provider: 'openai',
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: `You are Sarah, a professional appointment scheduling assistant for PressureMax pressure washing services. Follow this precise workflow:

1. **Gather Customer Info**: Ask for full name and verify phone number
2. **Check Interest**: Confirm they're still interested in pressure washing services
3. **Service Details**: Ask what type of service they need (house washing, driveway, deck, etc.)
4. **Check Availability**: Use checkAvailability tool to find open appointment slots
5. **Offer Options**: Present 2-3 available time slots clearly
6. **Book Appointment**: Use bookAppointment tool to confirm their selection
7. **Confirm Details**: Verify all appointment information

Keep responses under 30 words. Be friendly, professional, and efficient. Always emphasize FREE estimates.`
          }
        ],
        tools: [
          {
            type: 'function',
            function: {
              name: 'checkAvailability',
              description: 'Check available appointment slots for pressure washing estimates',
              parameters: {
                type: 'object',
                properties: {
                  preferredDate: {
                    type: 'string',
                    description: 'Preferred date in YYYY-MM-DD format'
                  },
                  serviceType: {
                    type: 'string',
                    description: 'Type of pressure washing service needed'
                  },
                  urgency: {
                    type: 'string',
                    enum: ['asap', 'this_week', 'this_month', 'flexible'],
                    description: 'How urgently they need the service'
                  }
                },
                required: ['serviceType']
              }
            },
            server: {
              url: `${window.location.origin}/api/vapi/check-availability`,
              timeoutSeconds: 30
            }
          },
          {
            type: 'function',
            function: {
              name: 'bookAppointment',
              description: 'Book a confirmed appointment slot',
              parameters: {
                type: 'object',
                properties: {
                  customerName: {
                    type: 'string',
                    description: 'Customer full name'
                  },
                  customerPhone: {
                    type: 'string',
                    description: 'Customer phone number'
                  },
                  appointmentDate: {
                    type: 'string',
                    description: 'Appointment date in YYYY-MM-DD format'
                  },
                  appointmentTime: {
                    type: 'string',
                    description: 'Appointment time in HH:MM format'
                  },
                  serviceType: {
                    type: 'string',
                    description: 'Type of pressure washing service'
                  },
                  leadId: {
                    type: 'string',
                    description: 'Lead ID from the call metadata'
                  }
                },
                required: ['customerName', 'customerPhone', 'appointmentDate', 'appointmentTime', 'serviceType']
              }
            },
            server: {
              url: `${window.location.origin}/api/vapi/book-appointment`,
              timeoutSeconds: 30
            }
          }
        ]
      },
      voice: {
        provider: 'elevenlabs',
        voiceId: 'sarah'
      },
      firstMessage: "Hi! This is Sarah from PressureMax calling about your pressure washing inquiry. I can help schedule your free estimate today. Do you have a quick minute?",
      recordingEnabled: true,
      endCallMessage: "Perfect! Your free estimate is confirmed. You'll get a confirmation text shortly. Thanks for choosing PressureMax!",
      endCallPhrases: ["goodbye", "bye", "not interested", "no thank you"],
      maxDurationSeconds: 600
    };

    return await this.makeRequest('/assistant', 'POST', assistantData);
  }

  // Schedule a follow-up call
  async scheduleFollowUpCall(leadId: string, assistantId: string, delayMinutes: number = 60): Promise<void> {
    // In a real implementation, this would schedule a delayed call
    console.log(`Follow-up call scheduled for lead ${leadId} in ${delayMinutes} minutes`);

    // For now, we'll just log it. In production, you'd use a job queue or scheduler
    setTimeout(async () => {
      console.log(`Executing follow-up call for lead ${leadId}`);
      // Would trigger another call here
    }, delayMinutes * 60 * 1000);
  }

  // Automatic new lead calling - triggers immediately when a lead is created
  async triggerNewLeadCall(lead: Lead): Promise<VAPICall | null> {
    try {
      console.log(`🔥 NEW LEAD TRIGGER: Initiating automatic call for ${lead.name}`);

      // Create appointment booking assistant if not exists
      const assistant = await this.createAppointmentBookingAssistant();

      // Enhanced call with lead context for better appointment booking
      const call = await this.callLead(lead, assistant.id);

      console.log(`✅ Automatic call initiated for new lead ${lead.name}: ${call.id}`);
      return call;

    } catch (error) {
      console.error(`❌ Failed to trigger automatic call for new lead ${lead.name}:`, error);
      return null;
    }
  }

  // Bulk call campaign for reactivation campaigns (existing leads)
  async runBulkCallCampaign(leads: Lead[], assistantId: string, phoneNumberId?: string): Promise<VAPICall[]> {
    const calls: VAPICall[] = [];

    console.log(`🔄 REACTIVATION CAMPAIGN: Starting bulk calls for ${leads.length} leads`);

    for (const lead of leads) {
      try {
        const call = await this.callLead(lead, assistantId);
        calls.push(call);

        // Rate limiting - wait 3 seconds between calls
        await new Promise(resolve => setTimeout(resolve, 3000));

        console.log(`📞 Reactivation call initiated for ${lead.name}: ${call.id}`);
      } catch (error) {
        console.error(`❌ Failed to call ${lead.name}:`, error);
      }
    }

    console.log(`✅ Reactivation campaign completed: ${calls.length}/${leads.length} calls initiated`);
    return calls;
  }

  // Smart lead prioritization for automatic calling
  async shouldTriggerAutomaticCall(lead: Lead): Promise<boolean> {
    // Only call new leads that haven't been contacted yet
    if (lead.status !== 'new' || lead.call_attempts > 0) {
      return false;
    }

    // Skip if lead was created more than 24 hours ago (should use reactivation instead)
    const leadAge = Date.now() - new Date(lead.created_at).getTime();
    const twentyFourHours = 24 * 60 * 60 * 1000;
    if (leadAge > twentyFourHours) {
      console.log(`⏰ Lead ${lead.name} is older than 24 hours, skipping automatic call`);
      return false;
    }

    // Prioritize high-quality leads
    const highPriorityQualities = ['hot', 'warm'];
    const highPriorityUrgency = ['asap', 'this_week'];

    if (highPriorityQualities.includes(lead.quality) || highPriorityUrgency.includes(lead.urgency)) {
      console.log(`🔥 High priority lead detected: ${lead.name} (${lead.quality}, ${lead.urgency})`);
      return true;
    }

    // Call all new leads within business hours
    const now = new Date();
    const hour = now.getHours();
    const isBusinessHours = hour >= 9 && hour <= 18; // 9 AM to 6 PM

    if (!isBusinessHours) {
      console.log(`🌙 Outside business hours, scheduling delayed call for ${lead.name}`);
      // Could implement delayed calling here
      return false;
    }

    return true;
  }
}

// Mock VAPI Service for development/testing
export class MockVAPIService extends VAPIService {
  private mockCalls: VAPICall[] = [];

  constructor() {
    super('mock_api_key');
  }

  async createPressureWashingAssistant(): Promise<VAPIAssistant> {
    return {
      id: 'assistant_mock_123',
      name: 'Sarah - Pressure Washing Assistant',
      model: {
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        messages: []
      },
      voice: {
        provider: 'elevenlabs',
        voiceId: 'sarah'
      },
      firstMessage: "Hi! This is Sarah calling about your pressure washing inquiry...",
      recordingEnabled: true,
      endCallMessage: "Thank you for your time!",
      endCallPhrases: ["goodbye", "bye"],
      maxDurationSeconds: 600
    };
  }

  async callLead(lead: Lead, assistantId: string): Promise<VAPICall> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const call: VAPICall = {
      id: `call_${Date.now()}`,
      assistantId,
      phoneNumber: lead.phone,
      status: 'queued',
      startedAt: new Date().toISOString()
    };

    this.mockCalls.push(call);

    // Simulate call progression
    setTimeout(() => {
      call.status = 'ringing';
    }, 2000);

    setTimeout(() => {
      call.status = 'in-progress';
    }, 5000);

    setTimeout(() => {
      call.status = 'ended';
      call.endedAt = new Date().toISOString();
      call.cost = Math.random() * 2 + 0.5; // $0.50 - $2.50
      call.transcript = this.generateMockTranscript(lead);
    }, 30000); // 30 second mock call

    return call;
  }

  async getCall(callId: string): Promise<VAPICall> {
    const call = this.mockCalls.find(c => c.id === callId);
    if (!call) throw new Error('Call not found');
    return call;
  }

  async getCalls(): Promise<VAPICall[]> {
    return this.mockCalls;
  }

  private generateMockTranscript(lead: Lead): string {
    const transcripts = [
      `Sarah: Hi ${lead.name}! This is Sarah calling about your interest in pressure washing services. Do you have a couple minutes to chat?
Customer: Yes, I do.
Sarah: Great! I see you're interested in ${lead.service_interest}. Can you tell me a bit more about what you're looking to have cleaned?
Customer: I need my house washed, it's looking pretty dirty after the winter.
Sarah: I understand completely. House washing is one of our most popular services. What's the best time for us to come out and give you a free estimate?
Customer: How about this Saturday morning?
Sarah: Perfect! I'll schedule you for Saturday at 10 AM. We'll provide a detailed estimate at no cost. Thank you!`,
      
      `Sarah: Hi ${lead.name}! This is Sarah calling about your pressure washing inquiry.
Customer: Oh hi, I'm actually pretty busy right now.
Sarah: I completely understand. When would be a better time to call you back?
Customer: Maybe next week sometime?
Sarah: Absolutely. I'll call you back next Tuesday. Have a great day!`,
      
      `Sarah: Hi ${lead.name}! This is Sarah calling about your interest in pressure washing.
Customer: Yes, I was looking at getting my driveway cleaned. How much does that usually cost?
Sarah: Great question! Driveway cleaning typically ranges from $150-300 depending on size and condition. We'd love to give you a free estimate. When works best for you?
Customer: That sounds reasonable. How about tomorrow afternoon?
Sarah: Perfect! I'll schedule you for tomorrow at 2 PM. Thank you!`
    ];

    return transcripts[Math.floor(Math.random() * transcripts.length)];
  }
}

// Export the appropriate service based on environment
export const vapiService = process.env.NODE_ENV === 'production' 
  ? new VAPIService(process.env.REACT_APP_VAPI_API_KEY || '')
  : new MockVAPIService();
