/**
 * Session Validator for PressureMax
 * Validates session persistence and authentication stability
 */

import { supabase } from '../lib/supabase';
import { facebookIntegration } from '../services/facebookIntegration';
import { sessionManager } from '../services/sessionManager';

export interface SessionValidationResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export interface SessionValidationSummary {
  totalTests: number;
  passed: number;
  failed: number;
  warnings: number;
  results: SessionValidationResult[];
  overallStatus: 'pass' | 'fail' | 'warning';
  recommendations: string[];
}

class SessionValidator {
  private results: SessionValidationResult[] = [];

  /**
   * Run comprehensive session validation
   */
  async validateSessions(): Promise<SessionValidationSummary> {
    this.results = [];
    
    console.log('🔍 Starting Session Validation...');

    // Test Supabase session persistence
    await this.testSupabaseSession();
    
    // Test Facebook session persistence
    await this.testFacebookSession();
    
    // Test session manager
    await this.testSessionManager();
    
    // Test authentication stability
    await this.testAuthStability();

    return this.generateSummary();
  }

  private async testSupabaseSession(): Promise<void> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        this.addResult('Supabase Session', 'fail', 
          `Session retrieval error: ${error.message}`, { error });
        return;
      }

      if (session) {
        this.addResult('Supabase Session', 'pass', 
          'Active Supabase session found', {
            userId: session.user.id,
            email: session.user.email,
            expiresAt: new Date(session.expires_at! * 1000).toISOString()
          });

        // Check token expiry
        const expiresAt = session.expires_at! * 1000;
        const timeUntilExpiry = expiresAt - Date.now();
        const hoursUntilExpiry = timeUntilExpiry / (1000 * 60 * 60);

        if (hoursUntilExpiry < 1) {
          this.addResult('Token Expiry', 'warning', 
            `Supabase token expires in ${Math.round(hoursUntilExpiry * 60)} minutes`);
        } else {
          this.addResult('Token Expiry', 'pass', 
            `Supabase token valid for ${Math.round(hoursUntilExpiry)} hours`);
        }
      } else {
        this.addResult('Supabase Session', 'warning', 
          'No active Supabase session (user not logged in)');
      }
    } catch (error) {
      this.addResult('Supabase Session', 'fail', 
        `Session test failed: ${error.message}`, { error });
    }
  }

  private async testFacebookSession(): Promise<void> {
    try {
      const status = facebookIntegration.getIntegrationStatus();
      
      if (status.isConnected) {
        this.addResult('Facebook Session', 'pass', 
          'Facebook integration is connected', {
            pages: status.pages?.length || 0,
            adAccounts: status.adAccounts?.length || 0,
            permissions: status.permissions?.length || 0
          });

        // Check token expiry
        if (status.expiresAt) {
          const timeUntilExpiry = status.expiresAt.getTime() - Date.now();
          const hoursUntilExpiry = timeUntilExpiry / (1000 * 60 * 60);

          if (hoursUntilExpiry < 1) {
            this.addResult('Facebook Token Expiry', 'warning', 
              `Facebook token expires in ${Math.round(hoursUntilExpiry * 60)} minutes`);
          } else {
            this.addResult('Facebook Token Expiry', 'pass', 
              `Facebook token valid for ${Math.round(hoursUntilExpiry)} hours`);
          }
        } else {
          this.addResult('Facebook Token Expiry', 'warning', 
            'Facebook token expiry not available');
        }

        // Test API connectivity
        try {
          await facebookIntegration.testConnection();
          this.addResult('Facebook API Test', 'pass', 
            'Facebook API connection successful');
        } catch (error) {
          this.addResult('Facebook API Test', 'fail', 
            `Facebook API test failed: ${error.message}`);
        }
      } else {
        this.addResult('Facebook Session', 'warning', 
          'Facebook integration not connected');
      }
    } catch (error) {
      this.addResult('Facebook Session', 'fail', 
        `Facebook session test failed: ${error.message}`, { error });
    }
  }

  private async testSessionManager(): Promise<void> {
    try {
      const sessionInfo = sessionManager.getSessionInfo();
      
      this.addResult('Session Manager', 'pass', 
        'Session manager is active', {
          sessionId: sessionInfo.sessionId,
          duration: sessionManager.getSessionDuration(),
          isValid: sessionManager.isSessionValid()
        });

      // Test session persistence
      const isValid = sessionManager.isSessionValid();
      if (isValid) {
        this.addResult('Session Validity', 'pass', 
          'Session is valid and persistent');
      } else {
        this.addResult('Session Validity', 'warning', 
          'Session may have expired or is invalid');
      }
    } catch (error) {
      this.addResult('Session Manager', 'fail', 
        `Session manager test failed: ${error.message}`, { error });
    }
  }

  private async testAuthStability(): Promise<void> {
    try {
      // Test multiple rapid auth checks to simulate real usage
      const authChecks = [];
      for (let i = 0; i < 5; i++) {
        authChecks.push(supabase.auth.getSession());
      }

      const results = await Promise.allSettled(authChecks);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      if (failed === 0) {
        this.addResult('Auth Stability', 'pass', 
          `All ${successful} concurrent auth checks succeeded`);
      } else {
        this.addResult('Auth Stability', 'warning', 
          `${failed}/${results.length} auth checks failed under load`);
      }
    } catch (error) {
      this.addResult('Auth Stability', 'fail', 
        `Auth stability test failed: ${error.message}`, { error });
    }
  }

  private addResult(test: string, status: 'pass' | 'fail' | 'warning', message: string, details?: any): void {
    this.results.push({ test, status, message, details });
    
    const emoji = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    console.log(`${emoji} [Session] ${test}: ${message}`);
  }

  private generateSummary(): SessionValidationSummary {
    const passed = this.results.filter(r => r.status === 'pass').length;
    const failed = this.results.filter(r => r.status === 'fail').length;
    const warnings = this.results.filter(r => r.status === 'warning').length;

    let overallStatus: 'pass' | 'fail' | 'warning' = 'pass';
    if (failed > 0) {
      overallStatus = 'fail';
    } else if (warnings > 0) {
      overallStatus = 'warning';
    }

    const recommendations = this.generateRecommendations();

    const summary = {
      totalTests: this.results.length,
      passed,
      failed,
      warnings,
      results: this.results,
      overallStatus,
      recommendations
    };

    console.log('\n📊 Session Validation Summary:');
    console.log(`Total Tests: ${summary.totalTests}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⚠️ Warnings: ${warnings}`);
    console.log(`Overall Status: ${overallStatus.toUpperCase()}`);

    if (recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      recommendations.forEach(rec => console.log(`  • ${rec}`));
    }

    return summary;
  }

  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    // Check for token expiry warnings
    const tokenWarnings = this.results.filter(r => 
      r.test.includes('Token Expiry') && r.status === 'warning'
    );
    if (tokenWarnings.length > 0) {
      recommendations.push('Consider refreshing authentication tokens before they expire');
    }

    // Check for failed API tests
    const apiFailures = this.results.filter(r => 
      r.test.includes('API Test') && r.status === 'fail'
    );
    if (apiFailures.length > 0) {
      recommendations.push('Check network connectivity and API permissions');
    }

    // Check for auth stability issues
    const stabilityIssues = this.results.filter(r => 
      r.test.includes('Stability') && r.status !== 'pass'
    );
    if (stabilityIssues.length > 0) {
      recommendations.push('Consider implementing request debouncing to improve auth stability');
    }

    // Check for session issues
    const sessionIssues = this.results.filter(r => 
      r.test.includes('Session') && r.status === 'fail'
    );
    if (sessionIssues.length > 0) {
      recommendations.push('Review session management configuration and storage');
    }

    return recommendations;
  }
}

export const sessionValidator = new SessionValidator();
