/**
 * Campaign Creation Wizard Page for PressureMax
 * Full-page multi-step wizard for streamlined campaign creation
 */

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Play, Eye, Target, DollarSign, Image, CheckCircle, AlertCircle, Loader, ArrowLeft } from 'lucide-react';
import { AdTemplate } from '../types/database';
import { facebookAdsService, FacebookAdAccount } from '../services/facebookAds';
import { facebookIntegration } from '../services/facebookIntegration';
import { facebookCampaignService } from '../services/facebookCampaignService';
import { db } from '../services/database';
import { useAuth } from '../contexts/AuthContext';
import { CampaignPreview } from './CampaignPreview';

interface CampaignWizardPageProps {
  template: AdTemplate | null;
  onSuccess: (campaignId: string) => void;
  onBack: () => void;
}

interface WizardStep {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
}

const wizardSteps: WizardStep[] = [
  {
    id: 'template',
    title: 'Template Selection',
    icon: Target,
    description: 'Choose and customize your ad template'
  },
  {
    id: 'targeting',
    title: 'Audience Targeting',
    icon: Target,
    description: 'Define your target audience'
  },
  {
    id: 'budget',
    title: 'Budget & Schedule',
    icon: DollarSign,
    description: 'Set your budget and campaign duration'
  },
  {
    id: 'creative',
    title: 'Creative Assets',
    icon: Image,
    description: 'Upload images and customize ad copy'
  },
  {
    id: 'preview',
    title: 'Preview & Launch',
    icon: Eye,
    description: 'Review your campaign before going live'
  }
];

// Helper function to get user's location for smart defaults
const getUserLocation = () => {
  // In a real implementation, this would use geolocation API or user's saved location
  return {
    lat: 40.7128,
    lng: -74.0060,
    city: 'New York, NY'
  };
};

export const CampaignWizardPage: React.FC<CampaignWizardPageProps> = ({
  template,
  onSuccess,
  onBack
}) => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [adAccounts, setAdAccounts] = useState<FacebookAdAccount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isQuickLaunch, setIsQuickLaunch] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    // Template data
    selectedTemplate: template,
    customHeadline: '',
    customDescription: '',
    customCTA: '',

    // Targeting data
    selectedAccount: '',
    pageId: '',
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      radius: 25,
      city: 'New York, NY'
    },
    locationType: 'home',
    ageRange: { min: 30, max: 65 },
    gender: 'all',
    relationshipStatus: 'all',
    interests: ['homeowner', 'property maintenance'],
    incomeLevel: 'top_25',
    propertyTypes: ['Single family home'],

    // Budget data
    dailyBudget: 50,
    campaignDuration: 30,
    bidStrategy: 'LOWEST_COST_WITH_BID_CAP',

    // Creative data
    websiteUrl: '',
    uploadedImages: [] as File[],

    // Preview data
    campaignName: ''
  });

  useEffect(() => {
    if (template) {
      // Smart defaults for faster workflow
      const userLocation = getUserLocation();
      const smartDefaults = {
        selectedTemplate: template,
        customHeadline: template.creative.headline,
        customDescription: template.creative.description,
        customCTA: template.creative.call_to_action,
        dailyBudget: template.budget_range.suggested,
        campaignName: `${template.name} - ${new Date().toLocaleDateString()}`,
        // Auto-populate user's business info
        websiteUrl: user?.website_url || '',
        pageId: user?.facebook_page_id || '',
        // Smart location defaults
        location: {
          latitude: userLocation.lat,
          longitude: userLocation.lng,
          radius: 25,
          city: userLocation.city
        },
        // Optimal targeting for pressure washing
        ageRange: { min: 30, max: 65 }, // Homeowner demographic
        interests: ['homeowner', 'property maintenance', 'home improvement'],
        // Optimal bid strategy
        bidStrategy: 'LOWEST_COST_WITH_BID_CAP'
      };

      setFormData(prev => ({ ...prev, ...smartDefaults }));
      loadAdAccounts();
    }
  }, [template, user]);

  // Keyboard shortcuts for faster navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (isLoading) return;

      if (e.key === 'Enter' && e.ctrlKey) {
        // Ctrl+Enter to go to next step or launch
        if (currentStep < wizardSteps.length - 1) {
          handleNext();
        } else {
          handleLaunch();
        }
      } else if (e.key === 'Escape') {
        // Escape to go back
        onBack();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isLoading, currentStep]);

  const loadAdAccounts = async () => {
    try {
      setIsLoading(true);

      // Check if Facebook is connected
      const facebookStatus = facebookIntegration.getIntegrationStatus();
      if (!facebookStatus.isConnected) {
        setError('Facebook Business account not connected. Please connect your account in the Integrations page.');
        return;
      }

      // Use ad accounts from the integration
      const accounts = facebookStatus.adAccounts.map(account => ({
        id: account.account_id,
        account_id: account.account_id,
        name: account.name,
        account_status: account.account_status,
        currency: account.currency,
        timezone_name: account.timezone_name
      }));

      setAdAccounts(accounts);
      if (accounts.length > 0) {
        setFormData(prev => ({ ...prev, selectedAccount: accounts[0].id }));
      }

      // Auto-select default page if available
      const defaultPage = facebookIntegration.getDefaultPage();
      if (defaultPage) {
        setFormData(prev => ({ ...prev, pageId: defaultPage.id }));
      }
    } catch (error) {
      console.error('Error loading ad accounts:', error);
      setError('Failed to load Facebook ad accounts');
    } finally {
      setIsLoading(false);
    }
  };

  const validateCurrentStep = (): boolean => {
    const step = wizardSteps[currentStep];

    switch (step.id) {
      case 'template':
        return !!(formData.customHeadline && formData.customDescription && formData.customCTA);
      case 'targeting':
        return !!(formData.selectedAccount && formData.pageId && formData.location.city);
      case 'budget':
        return formData.dailyBudget >= (template?.budget_range.min || 20);
      case 'creative':
        return !!(formData.websiteUrl && formData.campaignName);
      case 'preview':
        return true;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (!validateCurrentStep()) {
      setError('Please complete all required fields before proceeding');
      return;
    }

    setError(''); // Clear any previous errors

    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    setError(''); // Clear errors when going back
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleQuickLaunch = async () => {
    // Validate minimum required fields
    if (!formData.selectedAccount || !formData.pageId || !formData.websiteUrl) {
      setError('Please complete your Facebook setup in settings first');
      return;
    }

    setIsQuickLaunch(true);
    await handleLaunch();
  };

  const handleLaunch = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Create campaign on Facebook using direct Graph API
      const result = await facebookCampaignService.createCampaignFromTemplate(
        formData.selectedTemplate!,
        formData.selectedAccount,
        formData.pageId,
        {
          budget: formData.dailyBudget,
          websiteUrl: formData.websiteUrl,
          customHeadline: formData.customHeadline,
          customDescription: formData.customDescription,
          customCTA: formData.customCTA,
          targeting: {
            geo_locations: {
              custom_locations: [{
                latitude: formData.location.latitude,
                longitude: formData.location.longitude,
                radius: formData.location.radius,
                distance_unit: 'mile'
              }]
            },
            age_min: formData.ageRange.min,
            age_max: formData.ageRange.max
            // Removed interests - not needed for pressure washing lead generation
          }
        }
      );

      if (result.success && result.campaign_id) {
        // Save to database
        const campaign = await db.createCampaign({
          template_id: formData.selectedTemplate!.id,
          name: formData.campaignName,
          facebook_campaign_id: result.campaign_id,
          budget: formData.dailyBudget,
          start_date: new Date(),
          custom_creative: {
            primary_text: formData.customDescription,
            headline: formData.customHeadline,
            description: formData.customDescription,
            call_to_action: formData.customCTA,
            media_requirements: formData.selectedTemplate!.creative.media_requirements
          },
          metrics: {
            impressions: 0,
            clicks: 0,
            ctr: 0,
            cpc: 0,
            cpl: 0,
            leads_generated: 0,
            spend: 0,
            last_sync: new Date()
          },
          status: 'active'
        });

        alert(`🎉 Campaign "${campaign.name}" created successfully in Facebook Ads Manager!\n\nCampaign ID: ${result.campaign_id}\n\nThe campaign is created in PAUSED status. You can review and activate it in Facebook Ads Manager.`);
        onSuccess(campaign.id);
        onBack(); // Go back to dashboard after success
      } else {
        throw new Error(result.error || 'Failed to create campaign');
      }
    } catch (error) {
      console.error('Error launching campaign:', error);
      setError(error instanceof Error ? error.message : 'Failed to launch campaign');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    const step = wizardSteps[currentStep];

    switch (step.id) {
      case 'template':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-white">Template Customization</h3>

            {template && (
              <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
                <h4 className="font-semibold text-white mb-2 text-lg">{template.name}</h4>
                <p className="text-gray-400 text-sm mb-6">{template.category} • {template.service}</p>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Headline</label>
                    <input
                      type="text"
                      value={formData.customHeadline}
                      onChange={(e) => setFormData(prev => ({ ...prev, customHeadline: e.target.value }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                      maxLength={40}
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.customHeadline.length}/40 characters</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                    <textarea
                      value={formData.customDescription}
                      onChange={(e) => setFormData(prev => ({ ...prev, customDescription: e.target.value }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                      rows={4}
                      maxLength={125}
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.customDescription.length}/125 characters</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Call to Action</label>
                    <select
                      value={formData.customCTA}
                      onChange={(e) => setFormData(prev => ({ ...prev, customCTA: e.target.value }))}
                      className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                    >
                      <option value="LEARN_MORE">Learn More</option>
                      <option value="CONTACT_US">Contact Us</option>
                      <option value="GET_QUOTE">Get Quote</option>
                      <option value="CALL_NOW">Call Now</option>
                      <option value="BOOK_TRAVEL">Book Now</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'targeting':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-white">Advanced Audience Targeting</h3>

            {/* Account Setup */}
            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
              <h4 className="font-semibold text-white mb-4 text-lg">Facebook Account Setup</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Facebook Ad Account *</label>
                  <select
                    value={formData.selectedAccount}
                    onChange={(e) => setFormData(prev => ({ ...prev, selectedAccount: e.target.value }))}
                    className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                  >
                    <option value="">Select Account</option>
                    {adAccounts.map(account => (
                      <option key={account.id} value={account.id}>{account.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Facebook Page *</label>
                  <select
                    value={formData.pageId}
                    onChange={(e) => setFormData(prev => ({ ...prev, pageId: e.target.value }))}
                    className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                  >
                    <option value="">Select Facebook Page</option>
                    {/* Show advertising-capable pages first */}
                    {facebookIntegration.getAdvertisingPages().map(page => (
                      <option key={page.id} value={page.id}>
                        {page.name} ({page.category}) ✓ Ads Enabled
                      </option>
                    ))}
                    {/* Show other pages */}
                    {facebookIntegration.getIntegrationStatus().pages
                      .filter(page => !page.tasks || !page.tasks.includes('ADVERTISE'))
                      .map(page => (
                        <option key={page.id} value={page.id}>
                          {page.name} ({page.category})
                        </option>
                      ))}
                  </select>
                  {facebookIntegration.getIntegrationStatus().pages.length === 0 && (
                    <p className="text-xs text-yellow-400 mt-1">
                      No Facebook pages found. Please ensure your Facebook Business account has pages set up.
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'budget':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-white">Budget & Schedule</h3>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Daily Budget</label>
                  <input
                    type="number"
                    value={formData.dailyBudget}
                    onChange={(e) => setFormData(prev => ({ ...prev, dailyBudget: parseInt(e.target.value) || 50 }))}
                    className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                    min={template?.budget_range.min || 20}
                    max={template?.budget_range.max || 500}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Campaign Duration (days)</label>
                  <input
                    type="number"
                    value={formData.campaignDuration}
                    onChange={(e) => setFormData(prev => ({ ...prev, campaignDuration: parseInt(e.target.value) || 30 }))}
                    className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                    min={1}
                    max={365}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'creative':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-white">Creative Assets</h3>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Website URL *</label>
                  <input
                    type="url"
                    value={formData.websiteUrl}
                    onChange={(e) => setFormData(prev => ({ ...prev, websiteUrl: e.target.value }))}
                    className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                    placeholder="https://your-website.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Campaign Name *</label>
                  <input
                    type="text"
                    value={formData.campaignName}
                    onChange={(e) => setFormData(prev => ({ ...prev, campaignName: e.target.value }))}
                    className="w-full bg-gray-700/50 border border-gray-600/50 text-white px-4 py-3 rounded-lg focus:border-cyan-500 focus:outline-none transition-colors"
                    placeholder="Enter campaign name"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'preview':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-semibold text-white">Preview & Launch</h3>

            <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
              <h4 className="text-lg font-semibold text-white mb-4">Campaign Summary</h4>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <span className="text-gray-400">Campaign Name:</span>
                    <p className="text-white font-medium">{formData.campaignName}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Daily Budget:</span>
                    <p className="text-white font-medium">${formData.dailyBudget}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Location:</span>
                    <p className="text-white font-medium">{formData.location.city}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Duration:</span>
                    <p className="text-white font-medium">{formData.campaignDuration} days</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Step content for {step.id}</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 text-white">
      {/* Header */}
      <header className="bg-gray-900/50 backdrop-blur-xl border-b border-gray-700/50 px-8 py-6 sticky top-0 z-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors"
            >
              <ArrowLeft size={20} />
              <span>Back to Dashboard</span>
            </button>
            <div className="w-px h-6 bg-gray-600"></div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
              Campaign Wizard
            </h1>
          </div>

          <div className="flex items-center space-x-4">
            <span className="text-gray-400">
              Step {currentStep + 1} of {wizardSteps.length}
            </span>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar - Step Navigation */}
        <nav className="w-80 bg-gray-900/30 backdrop-blur-xl border-r border-gray-700/50 min-h-screen p-8">
          <div className="space-y-4">
            {wizardSteps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = index === currentStep;
              const isCompleted = index < currentStep;
              const isAccessible = index <= currentStep;

              return (
                <div
                  key={step.id}
                  className={`flex items-start space-x-4 p-4 rounded-xl transition-all duration-200 ${
                    isActive
                      ? 'bg-gradient-to-r from-cyan-500/20 to-blue-500/20 border border-cyan-500/50'
                      : isCompleted
                      ? 'bg-gray-800/50 border border-gray-600/50'
                      : 'bg-gray-800/30 border border-gray-700/30'
                  }`}
                >
                  <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                    isActive
                      ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white'
                      : isCompleted
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-700 text-gray-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle size={20} />
                    ) : (
                      <StepIcon size={20} />
                    )}
                  </div>

                  <div className="flex-1">
                    <h3 className={`font-semibold ${
                      isActive ? 'text-white' : isCompleted ? 'text-gray-200' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </h3>
                    <p className={`text-sm ${
                      isActive ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      {step.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-8">
          <div className="max-w-4xl mx-auto">
            {/* Error Display */}
            {error && (
              <div className="mb-6 bg-red-500/20 border border-red-500/50 rounded-xl p-4 flex items-center space-x-3">
                <AlertCircle className="text-red-400" size={20} />
                <span className="text-red-200">{error}</span>
              </div>
            )}

            {/* Step Content */}
            <div className="mb-8">
              {renderStepContent()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex items-center justify-between">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 0 || isLoading}
                className={`flex items-center space-x-2 px-6 py-3 rounded-xl transition-all duration-200 ${
                  currentStep === 0 || isLoading
                    ? 'bg-gray-700/50 text-gray-500 cursor-not-allowed'
                    : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white border border-gray-600/50'
                }`}
              >
                <ChevronLeft size={20} />
                <span>Previous</span>
              </button>

              <div className="flex space-x-4">
                {currentStep < wizardSteps.length - 1 ? (
                  <button
                    onClick={handleNext}
                    disabled={isLoading || !validateCurrentStep()}
                    className={`flex items-center space-x-2 px-6 py-3 rounded-xl transition-all duration-200 ${
                      isLoading || !validateCurrentStep()
                        ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white hover:from-cyan-400 hover:to-blue-400 shadow-lg shadow-cyan-500/25'
                    }`}
                  >
                    <span>Next</span>
                    <ChevronRight size={20} />
                  </button>
                ) : (
                  <button
                    onClick={handleLaunch}
                    disabled={isLoading || !validateCurrentStep()}
                    className={`flex items-center space-x-2 px-8 py-3 rounded-xl transition-all duration-200 ${
                      isLoading || !validateCurrentStep()
                        ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                        : 'bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-400 hover:to-emerald-400 shadow-lg shadow-green-500/25'
                    }`}
                  >
                    {isLoading ? (
                      <>
                        <Loader className="animate-spin" size={20} />
                        <span>Launching...</span>
                      </>
                    ) : (
                      <>
                        <Play size={20} />
                        <span>Launch Campaign</span>
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
