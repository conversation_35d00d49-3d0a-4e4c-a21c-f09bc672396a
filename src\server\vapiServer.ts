// Simple Express server for VAPI webhook endpoints
import express from 'express';
import cors from 'cors';
import { vapiApiService } from '../services/vapiApi';

const app = express();
const PORT = process.env.VAPI_SERVER_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', service: 'VAPI Integration Server' });
});

// Check availability endpoint for VAPI
app.post('/api/vapi/check-availability', async (req, res) => {
  try {
    console.log('VAPI Availability Check:', req.body);
    
    const { preferredDate, serviceType, urgency } = req.body;
    
    const response = await vapiApiService.checkAvailability({
      preferredDate,
      serviceType,
      urgency
    });
    
    res.json(response);
  } catch (error) {
    console.error('Error in availability check:', error);
    res.status(500).json({
      availableSlots: [],
      message: 'Sorry, I\'m having trouble checking availability. Let me transfer you to our team.'
    });
  }
});

// Book appointment endpoint for VAPI
app.post('/api/vapi/book-appointment', async (req, res) => {
  try {
    console.log('VAPI Appointment Booking:', req.body);
    
    const { customerName, customerPhone, appointmentDate, appointmentTime, serviceType, leadId } = req.body;
    
    const response = await vapiApiService.bookAppointment({
      customerName,
      customerPhone,
      appointmentDate,
      appointmentTime,
      serviceType,
      leadId
    });
    
    res.json(response);
  } catch (error) {
    console.error('Error in appointment booking:', error);
    res.status(500).json({
      success: false,
      message: 'Sorry, there was an issue booking your appointment. Let me transfer you to our team.'
    });
  }
});

// VAPI webhook for call events
app.post('/api/vapi/webhook', async (req, res) => {
  try {
    console.log('VAPI Webhook Event:', req.body);
    
    const { type, call, message } = req.body;
    
    switch (type) {
      case 'call-start':
        console.log(`Call started: ${call?.id}`);
        break;
        
      case 'call-end':
        console.log(`Call ended: ${call?.id}`);
        
        // Update lead after call completion
        if (call?.metadata?.leadId) {
          await vapiApiService.updateLeadAfterCall(call.metadata.leadId, {
            callId: call.id,
            duration: call.duration,
            transcript: call.transcript,
            appointmentBooked: call.metadata?.appointmentBooked === 'true'
          });
        }
        break;
        
      case 'function-call':
        console.log(`Function called: ${message?.function?.name}`);
        break;
        
      case 'transcript':
        console.log(`Transcript: ${message?.transcript}`);
        break;
        
      default:
        console.log(`Unknown webhook event: ${type}`);
    }
    
    res.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Get lead info for VAPI assistant
app.get('/api/vapi/lead/:phoneNumber', async (req, res) => {
  try {
    const { phoneNumber } = req.params;
    const lead = await vapiApiService.getLeadInfo(phoneNumber);
    
    if (lead) {
      res.json({
        leadId: lead.id,
        name: lead.name,
        serviceInterest: lead.service_interest,
        urgency: lead.urgency,
        propertyType: lead.property_type,
        address: lead.address,
        notes: lead.notes
      });
    } else {
      res.status(404).json({ error: 'Lead not found' });
    }
  } catch (error) {
    console.error('Error getting lead info:', error);
    res.status(500).json({ error: 'Failed to get lead info' });
  }
});

// Start server
if (require.main === module) {
  app.listen(PORT, () => {
    console.log(`VAPI Integration Server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/health`);
    console.log(`Webhook endpoint: http://localhost:${PORT}/api/vapi/webhook`);
  });
}

export default app;
