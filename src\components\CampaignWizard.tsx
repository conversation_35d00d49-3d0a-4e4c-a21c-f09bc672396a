/**
 * Enhanced Campaign Creation Wizard for PressureMax
 * Multi-step wizard for streamlined campaign creation
 */

import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Play, Eye, Target, DollarSign, Image, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { AdTemplate } from '../types/database';
import { facebookAdsService, FacebookAdAccount } from '../services/facebookAds';
import { facebookIntegration } from '../services/facebookIntegration';
import { facebookCampaignService } from '../services/facebookCampaignService';
import { db } from '../services/database';
import { useAuth } from '../contexts/AuthContext';
import { CampaignPreview } from './CampaignPreview';

interface CampaignWizardProps {
  isOpen: boolean;
  onClose: () => void;
  template: AdTemplate | null;
  onSuccess: (campaignId: string) => void;
}

interface WizardStep {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
}

const wizardSteps: WizardStep[] = [
  {
    id: 'template',
    title: 'Template Selection',
    icon: Target,
    description: 'Choose and customize your ad template'
  },
  {
    id: 'targeting',
    title: 'Audience Targeting',
    icon: Target,
    description: 'Define your target audience'
  },
  {
    id: 'budget',
    title: 'Budget & Schedule',
    icon: DollarSign,
    description: 'Set your budget and campaign duration'
  },
  {
    id: 'creative',
    title: 'Creative Assets',
    icon: Image,
    description: 'Upload images and customize ad copy'
  },
  {
    id: 'preview',
    title: 'Preview & Launch',
    icon: Eye,
    description: 'Review your campaign before going live'
  }
];

// Helper function to get user's location for smart defaults
const getUserLocation = () => {
  // In a real implementation, this would use geolocation API or user's saved location
  return {
    lat: 40.7128,
    lng: -74.0060,
    city: 'New York, NY'
  };
};

export const CampaignWizard: React.FC<CampaignWizardProps> = ({
  isOpen,
  onClose,
  template,
  onSuccess
}) => {
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [adAccounts, setAdAccounts] = useState<FacebookAdAccount[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isQuickLaunch, setIsQuickLaunch] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    // Template data
    selectedTemplate: template,
    customHeadline: '',
    customDescription: '',
    customCTA: '',

    // Targeting data
    selectedAccount: '',
    pageId: '',
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      radius: 25,
      city: 'New York, NY'
    },
    locationType: 'home',
    ageRange: { min: 30, max: 65 },
    gender: 'all',
    relationshipStatus: 'all',
    interests: ['homeowner', 'property maintenance'],
    incomeLevel: 'top_25',
    propertyTypes: ['Single family home'],

    // Budget data
    dailyBudget: 50,
    campaignDuration: 30,
    bidStrategy: 'LOWEST_COST_WITH_BID_CAP',

    // Creative data
    websiteUrl: '',
    uploadedImages: [] as File[],

    // Preview data
    campaignName: ''
  });

  useEffect(() => {
    if (isOpen && template) {
      // Smart defaults for faster workflow
      const userLocation = getUserLocation();
      const smartDefaults = {
        selectedTemplate: template,
        customHeadline: template.creative.headline,
        customDescription: template.creative.description,
        customCTA: template.creative.call_to_action,
        dailyBudget: template.budget_range.suggested,
        campaignName: `${template.name} - ${new Date().toLocaleDateString()}`,
        // Auto-populate user's business info
        websiteUrl: user?.website_url || '',
        pageId: user?.facebook_page_id || '',
        // Smart location defaults
        location: {
          latitude: userLocation.lat,
          longitude: userLocation.lng,
          radius: 25,
          city: userLocation.city
        },
        // Optimal targeting for pressure washing
        ageRange: { min: 30, max: 65 }, // Homeowner demographic
        interests: ['homeowner', 'property maintenance', 'home improvement'],
        // Optimal bid strategy
        bidStrategy: 'LOWEST_COST_WITH_BID_CAP'
      };

      setFormData(prev => ({ ...prev, ...smartDefaults }));
      loadAdAccounts();
    }
  }, [isOpen, template, user]);

  // Keyboard shortcuts for faster navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isOpen || isLoading) return;

      if (e.key === 'Enter' && e.ctrlKey) {
        // Ctrl+Enter to go to next step or launch
        if (currentStep < wizardSteps.length - 1) {
          handleNext();
        } else {
          handleLaunch();
        }
      } else if (e.key === 'Escape') {
        // Escape to close
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [isOpen, isLoading, currentStep]);

  const loadAdAccounts = async () => {
    try {
      setIsLoading(true);

      // Check if Facebook is connected
      const facebookStatus = facebookIntegration.getIntegrationStatus();
      if (!facebookStatus.isConnected) {
        setError('Facebook Business account not connected. Please connect your account in the Integrations page.');
        return;
      }

      // Use ad accounts from the integration
      const accounts = facebookStatus.adAccounts.map(account => ({
        id: account.account_id,
        account_id: account.account_id,
        name: account.name,
        account_status: account.account_status,
        currency: account.currency,
        timezone_name: account.timezone_name
      }));

      setAdAccounts(accounts);
      if (accounts.length > 0) {
        setFormData(prev => ({ ...prev, selectedAccount: accounts[0].id }));
      }

      // Auto-select first page if available
      if (facebookStatus.pages.length > 0) {
        setFormData(prev => ({ ...prev, pageId: facebookStatus.pages[0].id }));
      }
    } catch (error) {
      console.error('Error loading ad accounts:', error);
      setError('Failed to load Facebook ad accounts');
    } finally {
      setIsLoading(false);
    }
  };

  const validateCurrentStep = (): boolean => {
    const step = wizardSteps[currentStep];

    switch (step.id) {
      case 'template':
        return !!(formData.customHeadline && formData.customDescription && formData.customCTA);
      case 'targeting':
        return !!(formData.selectedAccount && formData.pageId && formData.location.city);
      case 'budget':
        return formData.dailyBudget >= (template?.budget_range.min || 20);
      case 'creative':
        return !!(formData.websiteUrl && formData.campaignName);
      case 'preview':
        return true;
      default:
        return true;
    }
  };

  const handleNext = () => {
    if (!validateCurrentStep()) {
      setError('Please complete all required fields before proceeding');
      return;
    }

    setError(''); // Clear any previous errors

    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    setError(''); // Clear errors when going back
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleQuickLaunch = async () => {
    // Validate minimum required fields
    if (!formData.selectedAccount || !formData.pageId || !formData.websiteUrl) {
      setError('Please complete your Facebook setup in settings first');
      return;
    }

    setIsQuickLaunch(true);
    await handleLaunch();
  };

  const handleLaunch = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Create campaign on Facebook using direct Graph API
      const result = await facebookCampaignService.createCampaignFromTemplate(
        formData.selectedTemplate!,
        formData.selectedAccount,
        formData.pageId,
        {
          budget: formData.dailyBudget,
          websiteUrl: formData.websiteUrl,
          customHeadline: formData.customHeadline,
          customDescription: formData.customDescription,
          customCTA: formData.customCTA,
          targeting: {
            geo_locations: {
              custom_locations: [{
                latitude: formData.location.latitude,
                longitude: formData.location.longitude,
                radius: formData.location.radius,
                distance_unit: 'mile'
              }]
            },
            age_min: formData.ageRange.min,
            age_max: formData.ageRange.max
            // Removed interests - not needed for pressure washing lead generation
          }
        }
      );

      // Save to database
      const campaign = await db.createCampaign({
        template_id: formData.selectedTemplate!.id,
        name: formData.campaignName,
        facebook_campaign_id: result.campaignId,
        budget: formData.dailyBudget,
        start_date: new Date(),
        custom_creative: {
          primary_text: formData.customDescription,
          headline: formData.customHeadline,
          description: formData.customDescription,
          call_to_action: formData.customCTA,
          media_requirements: formData.selectedTemplate!.creative.media_requirements
        },
        metrics: {
          impressions: 0,
          clicks: 0,
          ctr: 0,
          cpc: 0,
          cpl: 0,
          leads_generated: 0,
          spend: 0,
          last_sync: new Date()
        },
        status: 'active'
      });

      onSuccess(campaign.id);
      onClose();
    } catch (error) {
      console.error('Error launching campaign:', error);
      setError(error instanceof Error ? error.message : 'Failed to launch campaign');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    const step = wizardSteps[currentStep];

    switch (step.id) {
      case 'template':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Template Customization</h3>
            
            {template && (
              <div className="bg-gray-800 border border-gray-600 p-4">
                <h4 className="font-semibold text-white mb-2">{template.name}</h4>
                <p className="text-gray-400 text-sm mb-4">{template.category} • {template.service}</p>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Headline</label>
                    <input
                      type="text"
                      value={formData.customHeadline}
                      onChange={(e) => setFormData(prev => ({ ...prev, customHeadline: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                      maxLength={40}
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.customHeadline.length}/40 characters</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Description</label>
                    <textarea
                      value={formData.customDescription}
                      onChange={(e) => setFormData(prev => ({ ...prev, customDescription: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                      rows={3}
                      maxLength={125}
                    />
                    <p className="text-xs text-gray-500 mt-1">{formData.customDescription.length}/125 characters</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">Call to Action</label>
                    <select
                      value={formData.customCTA}
                      onChange={(e) => setFormData(prev => ({ ...prev, customCTA: e.target.value }))}
                      className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    >
                      <option value="LEARN_MORE">Learn More</option>
                      <option value="CONTACT_US">Contact Us</option>
                      <option value="GET_QUOTE">Get Quote</option>
                      <option value="CALL_NOW">Call Now</option>
                      <option value="BOOK_TRAVEL">Book Now</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'targeting':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-white">Advanced Audience Targeting</h3>

            {/* Account Setup */}
            <div className="bg-gray-800 border border-gray-600 p-4">
              <h4 className="font-semibold text-white mb-3">Facebook Account Setup</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Facebook Ad Account *</label>
                  <select
                    value={formData.selectedAccount}
                    onChange={(e) => setFormData(prev => ({ ...prev, selectedAccount: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="">Select Account</option>
                    {adAccounts.map(account => (
                      <option key={account.id} value={account.id}>{account.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Facebook Page *</label>
                  <select
                    value={formData.pageId}
                    onChange={(e) => setFormData(prev => ({ ...prev, pageId: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="">Select Facebook Page</option>
                    {facebookIntegration.getIntegrationStatus().pages.map(page => (
                      <option key={page.id} value={page.id}>
                        {page.name} ({page.category})
                      </option>
                    ))}
                  </select>
                  {facebookIntegration.getIntegrationStatus().pages.length === 0 && (
                    <p className="text-xs text-yellow-400 mt-1">
                      No Facebook pages found. Please ensure your Facebook Business account has pages set up.
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Geographic Targeting */}
            <div className="bg-gray-800 border border-gray-600 p-4">
              <h4 className="font-semibold text-white mb-3">Geographic Targeting</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Primary Location</label>
                  <input
                    type="text"
                    value={formData.location.city}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      location: { ...prev.location, city: e.target.value }
                    }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    placeholder="City, State"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Service Radius (miles)</label>
                  <select
                    value={formData.location.radius}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      location: { ...prev.location, radius: parseInt(e.target.value) || 25 }
                    }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value={10}>10 miles (Urban)</option>
                    <option value={25}>25 miles (Suburban)</option>
                    <option value={50}>50 miles (Rural)</option>
                    <option value={75}>75 miles (Extended)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Location Type</label>
                  <select
                    value={formData.locationType || 'home'}
                    onChange={(e) => setFormData(prev => ({ ...prev, locationType: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="home">People who live here</option>
                    <option value="recent">Recently in this location</option>
                    <option value="travel">Traveling to this location</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Demographics */}
            <div className="bg-gray-800 border border-gray-600 p-4">
              <h4 className="font-semibold text-white mb-3">Demographics</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Age Range</label>
                  <div className="flex space-x-2">
                    <select
                      value={formData.ageRange.min}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        ageRange: { ...prev.ageRange, min: parseInt(e.target.value) || 18 }
                      }))}
                      className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    >
                      {Array.from({length: 48}, (_, i) => i + 18).map(age => (
                        <option key={age} value={age}>{age}</option>
                      ))}
                    </select>
                    <span className="text-gray-400 self-center">to</span>
                    <select
                      value={formData.ageRange.max}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        ageRange: { ...prev.ageRange, max: parseInt(e.target.value) || 65 }
                      }))}
                      className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                    >
                      {Array.from({length: 48}, (_, i) => i + 18).map(age => (
                        <option key={age} value={age}>{age}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Gender</label>
                  <select
                    value={formData.gender || 'all'}
                    onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="all">All genders</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Relationship Status</label>
                  <select
                    value={formData.relationshipStatus || 'all'}
                    onChange={(e) => setFormData(prev => ({ ...prev, relationshipStatus: e.target.value }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  >
                    <option value="all">All</option>
                    <option value="single">Single</option>
                    <option value="relationship">In a relationship</option>
                    <option value="married">Married</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Interests & Behaviors */}
            <div className="bg-gray-800 border border-gray-600 p-4">
              <h4 className="font-semibold text-white mb-3">Interests & Behaviors</h4>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">Homeowner Targeting</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {[
                    'Homeowner',
                    'Property maintenance',
                    'Home improvement',
                    'Landscaping',
                    'Outdoor cleaning',
                    'House cleaning',
                    'Home renovation',
                    'Exterior maintenance'
                  ].map(interest => (
                    <label key={interest} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={formData.interests.includes(interest.toLowerCase())}
                        onChange={(e) => {
                          const interestLower = interest.toLowerCase();
                          setFormData(prev => ({
                            ...prev,
                            interests: e.target.checked
                              ? [...prev.interests, interestLower]
                              : prev.interests.filter(i => i !== interestLower)
                          }));
                        }}
                        className="h-4 w-4 text-cyan-500 bg-gray-700 border-gray-600 focus:ring-cyan-500"
                      />
                      <span className="text-gray-300">{interest}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-300 mb-2">Income Level</label>
                <select
                  value={formData.incomeLevel || 'all'}
                  onChange={(e) => setFormData(prev => ({ ...prev, incomeLevel: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                >
                  <option value="all">All income levels</option>
                  <option value="top_25">Top 25% of zip code</option>
                  <option value="top_10">Top 10% of zip code</option>
                  <option value="top_5">Top 5% of zip code</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Property Type</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {[
                    'Single family home',
                    'Townhouse',
                    'Condo',
                    'Multi-family home'
                  ].map(property => (
                    <label key={property} className="flex items-center space-x-2 text-sm">
                      <input
                        type="checkbox"
                        checked={formData.propertyTypes?.includes(property) || false}
                        onChange={(e) => {
                          setFormData(prev => ({
                            ...prev,
                            propertyTypes: e.target.checked
                              ? [...(prev.propertyTypes || []), property]
                              : (prev.propertyTypes || []).filter(p => p !== property)
                          }));
                        }}
                        className="h-4 w-4 text-cyan-500 bg-gray-700 border-gray-600 focus:ring-cyan-500"
                      />
                      <span className="text-gray-300">{property}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            {/* Audience Size Estimate */}
            <div className="bg-cyan-900/20 border border-cyan-500/30 p-4">
              <h4 className="font-semibold text-cyan-400 mb-2">Estimated Audience Size</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-white">{(formData.location.radius * 1000).toLocaleString()}</p>
                  <p className="text-xs text-gray-400">Potential Reach</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-cyan-400">{Math.round(formData.location.radius * 100).toLocaleString()}</p>
                  <p className="text-xs text-gray-400">Targeted Audience</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-400">{Math.round(formData.dailyBudget * 20)}</p>
                  <p className="text-xs text-gray-400">Daily Reach</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'budget':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Budget & Schedule</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Daily Budget</label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-400">$</span>
                  <input
                    type="number"
                    value={formData.dailyBudget}
                    onChange={(e) => setFormData(prev => ({ ...prev, dailyBudget: parseInt(e.target.value) || 50 }))}
                    className="w-full bg-gray-700 border border-gray-600 text-white pl-8 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                    min={template?.budget_range.min || 20}
                    max={template?.budget_range.max || 500}
                  />
                </div>
                {template && (
                  <p className="text-xs text-gray-500 mt-1">
                    Recommended: ${template.budget_range.min} - ${template.budget_range.max}
                  </p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">Campaign Duration (days)</label>
                <input
                  type="number"
                  value={formData.campaignDuration}
                  onChange={(e) => setFormData(prev => ({ ...prev, campaignDuration: parseInt(e.target.value) || 30 }))}
                  className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  min={1}
                  max={365}
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Bid Strategy</label>
              <select
                value={formData.bidStrategy}
                onChange={(e) => setFormData(prev => ({ ...prev, bidStrategy: e.target.value }))}
                className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
              >
                <option value="LOWEST_COST_WITH_BID_CAP">Highest Volume with Bid Cap (Recommended)</option>
                <option value="LOWEST_COST_WITHOUT_CAP">Lowest Cost</option>
                <option value="TARGET_COST">Target Cost</option>
              </select>
            </div>
            
            <div className="bg-gray-800 border border-gray-600 p-4">
              <h4 className="font-semibold text-white mb-2">Estimated Results</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-cyan-400">{Math.round(formData.dailyBudget * 20)}</p>
                  <p className="text-xs text-gray-400">Daily Reach</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-400">{Math.round(formData.dailyBudget * 0.8)}</p>
                  <p className="text-xs text-gray-400">Daily Clicks</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-yellow-400">{Math.round(formData.dailyBudget * 0.1)}</p>
                  <p className="text-xs text-gray-400">Daily Leads</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'creative':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Creative Assets</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Website URL</label>
              <input
                type="url"
                value={formData.websiteUrl}
                onChange={(e) => setFormData(prev => ({ ...prev, websiteUrl: e.target.value }))}
                className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                placeholder="https://yourwebsite.com/contact"
              />
              <p className="text-xs text-gray-500 mt-1">Where users will be directed when they click your ad</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Campaign Name</label>
              <input
                type="text"
                value={formData.campaignName}
                onChange={(e) => setFormData(prev => ({ ...prev, campaignName: e.target.value }))}
                className="w-full bg-gray-700 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                placeholder="My Pressure Washing Campaign"
              />
            </div>
          </div>
        );

      case 'preview':
        return (
          <div className="space-y-6">
            <CampaignPreview
              template={formData.selectedTemplate!}
              customData={{
                headline: formData.customHeadline,
                description: formData.customDescription,
                cta: formData.customCTA,
                websiteUrl: formData.websiteUrl,
                campaignName: formData.campaignName
              }}
            />

            <div className="bg-gray-800 border border-gray-600 p-4">
              <h4 className="font-semibold text-white mb-2">Campaign Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Campaign Name:</span>
                  <span className="text-white">{formData.campaignName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Daily Budget:</span>
                  <span className="text-white">${formData.dailyBudget}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Duration:</span>
                  <span className="text-white">{formData.campaignDuration} days</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Budget:</span>
                  <span className="text-white">${formData.dailyBudget * formData.campaignDuration}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Target Location:</span>
                  <span className="text-white">{formData.location.city}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Age Range:</span>
                  <span className="text-white">{formData.ageRange.min} - {formData.ageRange.max}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Estimated Daily Reach:</span>
                  <span className="text-white">{Math.round(formData.dailyBudget * 20).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Estimated Daily Leads:</span>
                  <span className="text-white">{Math.round(formData.dailyBudget * 0.1)}</span>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!isOpen || !template) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 border border-gray-700 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-white">Campaign Creation Wizard</h2>
            <p className="text-sm text-gray-400">Create and launch your campaign in under 3 minutes</p>
          </div>

          <div className="flex items-center space-x-4">
            {/* Quick Launch Button */}
            {currentStep === 0 && !isLoading && (
              <button
                onClick={handleQuickLaunch}
                disabled={!formData.selectedAccount || !formData.pageId || !formData.websiteUrl}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-black font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Play size={16} />
                <span>Quick Launch</span>
              </button>
            )}

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
              disabled={isLoading}
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            {wizardSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  index <= currentStep 
                    ? 'bg-cyan-500 border-cyan-500 text-black' 
                    : 'border-gray-600 text-gray-400'
                }`}>
                  {index < currentStep ? (
                    <CheckCircle size={16} />
                  ) : (
                    <span className="text-sm font-bold">{index + 1}</span>
                  )}
                </div>
                {index < wizardSteps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    index < currentStep ? 'bg-cyan-500' : 'bg-gray-600'
                  }`} />
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-2 flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white">{wizardSteps[currentStep].title}</h3>
              <p className="text-sm text-gray-400">{wizardSteps[currentStep].description}</p>
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-cyan-400">
                Step {currentStep + 1} of {wizardSteps.length}
              </p>
              <p className="text-xs text-gray-500">
                ~{Math.max(1, 3 - currentStep)} min remaining
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 bg-red-900/20 border border-red-500 text-red-400 px-4 py-3 flex items-center space-x-2">
              <AlertCircle size={16} />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {renderStepContent()}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-700">
          {/* Keyboard Shortcuts Hint */}
          <div className="px-6 py-2 bg-gray-800 border-b border-gray-700">
            <p className="text-xs text-gray-400 text-center">
              💡 Speed tips: <kbd className="bg-gray-700 px-1 rounded">Ctrl+Enter</kbd> to continue,
              <kbd className="bg-gray-700 px-1 rounded ml-1">Esc</kbd> to close,
              or use <span className="text-green-400">Quick Launch</span> for instant deployment
            </p>
          </div>

          <div className="flex justify-between items-center p-6">
            <button
              onClick={handlePrevious}
              disabled={currentStep === 0 || isLoading}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft size={16} />
              <span>Previous</span>
            </button>

            <div className="flex items-center space-x-4">
              {/* Progress indicator */}
              <div className="text-xs text-gray-400">
                {Math.round(((currentStep + 1) / wizardSteps.length) * 100)}% complete
              </div>

              {currentStep < wizardSteps.length - 1 ? (
                <button
                  onClick={handleNext}
                  disabled={isLoading || !validateCurrentStep()}
                  className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span>Next</span>
                  <ChevronRight size={16} />
                  <span className="text-xs ml-2">(Ctrl+Enter)</span>
                </button>
              ) : (
                <button
                  onClick={handleLaunch}
                  disabled={isLoading || !formData.selectedAccount || !formData.pageId || !formData.websiteUrl}
                  className="flex items-center space-x-2 px-6 py-2 bg-gradient-to-r from-green-500 to-green-600 text-black font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <>
                      <Loader className="animate-spin" size={16} />
                      <span>Launching...</span>
                    </>
                  ) : (
                    <>
                      <Play size={16} />
                      <span>Launch Campaign</span>
                      <span className="text-xs ml-2">(Ctrl+Enter)</span>
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
