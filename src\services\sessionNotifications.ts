/**
 * Session Notifications Service for PressureMax
 * Handles notifications for session expiration and other session-related events
 */

import { sessionManager } from './sessionManager';

export interface SessionNotification {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary' | 'danger';
  }>;
}

class SessionNotificationService {
  private notifications: SessionNotification[] = [];
  private listeners: Array<(notifications: SessionNotification[]) => void> = [];
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.startSessionMonitoring();
  }

  /**
   * Start monitoring session status
   */
  private startSessionMonitoring(): void {
    // Check every 5 minutes
    this.checkInterval = setInterval(() => {
      this.checkSessionStatus();
    }, 5 * 60 * 1000);

    // Initial check
    setTimeout(() => this.checkSessionStatus(), 1000);
  }

  /**
   * Check session status and create notifications if needed
   */
  private checkSessionStatus(): void {
    const isExpiringSoon = sessionManager.isSessionExpiringSoon();
    const isValid = sessionManager.isSessionValid();

    // Remove existing session notifications
    this.notifications = this.notifications.filter(n => 
      !n.id.startsWith('session-')
    );

    if (!isValid) {
      this.addNotification({
        id: 'session-expired',
        type: 'error',
        title: 'Session Expired',
        message: 'Your session has expired. Please log in again to continue.',
        timestamp: new Date(),
        actions: [
          {
            label: 'Refresh Page',
            action: () => window.location.reload(),
            style: 'primary'
          }
        ]
      });
    } else if (isExpiringSoon) {
      this.addNotification({
        id: 'session-expiring',
        type: 'warning',
        title: 'Session Expiring Soon',
        message: 'Your session will expire in less than an hour. Click to extend it.',
        timestamp: new Date(),
        actions: [
          {
            label: 'Extend Session',
            action: () => {
              sessionManager.extendSession();
              this.removeNotification('session-expiring');
              this.addNotification({
                id: 'session-extended',
                type: 'success',
                title: 'Session Extended',
                message: 'Your session has been extended successfully.',
                timestamp: new Date()
              });
              // Auto-remove success notification after 3 seconds
              setTimeout(() => this.removeNotification('session-extended'), 3000);
            },
            style: 'primary'
          },
          {
            label: 'Dismiss',
            action: () => this.removeNotification('session-expiring'),
            style: 'secondary'
          }
        ]
      });
    }
  }

  /**
   * Add a notification
   */
  addNotification(notification: SessionNotification): void {
    this.notifications.push(notification);
    this.notifyListeners();
  }

  /**
   * Remove a notification by ID
   */
  removeNotification(id: string): void {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.notifyListeners();
  }

  /**
   * Clear all notifications
   */
  clearAllNotifications(): void {
    this.notifications = [];
    this.notifyListeners();
  }

  /**
   * Get all notifications
   */
  getNotifications(): SessionNotification[] {
    return [...this.notifications];
  }

  /**
   * Subscribe to notification changes
   */
  subscribe(listener: (notifications: SessionNotification[]) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of changes
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener([...this.notifications]);
      } catch (error) {
        console.error('Error notifying session notification listener:', error);
      }
    });
  }

  /**
   * Create a Facebook session notification
   */
  notifyFacebookSessionIssue(message: string): void {
    this.addNotification({
      id: 'facebook-session-issue',
      type: 'warning',
      title: 'Facebook Session Issue',
      message,
      timestamp: new Date(),
      actions: [
        {
          label: 'Reconnect Facebook',
          action: () => {
            // This would trigger Facebook reconnection
            window.location.href = '/integrations';
          },
          style: 'primary'
        },
        {
          label: 'Dismiss',
          action: () => this.removeNotification('facebook-session-issue'),
          style: 'secondary'
        }
      ]
    });
  }

  /**
   * Create a success notification for successful operations
   */
  notifySuccess(title: string, message: string, autoRemove = true): void {
    const id = `success-${Date.now()}`;
    this.addNotification({
      id,
      type: 'success',
      title,
      message,
      timestamp: new Date()
    });

    if (autoRemove) {
      setTimeout(() => this.removeNotification(id), 5000);
    }
  }

  /**
   * Create an error notification
   */
  notifyError(title: string, message: string, actions?: SessionNotification['actions']): void {
    this.addNotification({
      id: `error-${Date.now()}`,
      type: 'error',
      title,
      message,
      timestamp: new Date(),
      actions
    });
  }

  /**
   * Create an info notification
   */
  notifyInfo(title: string, message: string, autoRemove = true): void {
    const id = `info-${Date.now()}`;
    this.addNotification({
      id,
      type: 'info',
      title,
      message,
      timestamp: new Date()
    });

    if (autoRemove) {
      setTimeout(() => this.removeNotification(id), 8000);
    }
  }

  /**
   * Cleanup when service is destroyed
   */
  destroy(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    this.listeners = [];
    this.notifications = [];
  }
}

// Export singleton instance
export const sessionNotifications = new SessionNotificationService();
