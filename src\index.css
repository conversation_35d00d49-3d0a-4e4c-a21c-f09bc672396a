@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background: #000;
  overflow-x: hidden;
}

.font-orbitron {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Custom Animations */
@keyframes pulse-slow {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes glitch {
  0% { 
    text-shadow: 1px 0 #00f5ff;
    transform: translateX(0);
  }
  20% { 
    text-shadow: -1px 1px #00f5ff;
    transform: translateX(-2px);
  }
  40% { 
    text-shadow: 1px -1px #00f5ff;
    transform: translateX(2px);
  }
  60% { 
    text-shadow: -1px -1px #00f5ff;
    transform: translateX(-1px);
  }
  80% { 
    text-shadow: 1px 1px #00f5ff;
    transform: translateX(1px);
  }
  100% { 
    text-shadow: 0 0 #00f5ff;
    transform: translateX(0);
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes circuit-flow {
  0% { background-position: 0 0; }
  100% { background-position: 50px 50px; }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-glitch {
  animation: glitch 0.3s infinite;
}

.animate-blink {
  animation: blink 1s infinite;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

/* Circuit Patterns */
.circuit-pattern {
  background-image: 
    linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: circuit-flow 20s linear infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000;
}

::-webkit-scrollbar-thumb {
  background: #00f5ff;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00d4ff;
}

/* Selection Styling */
::selection {
  background: rgba(0, 245, 255, 0.3);
  color: white;
}

/* Glow Effects */
.glow-cyan {
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
}

.text-glow-cyan {
  text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 245, 255, 0.2);
}

/* Terminal Effects */
.terminal-cursor {
  display: inline-block;
  background-color: #00f5ff;
  animation: blink 1s infinite;
}

/* Grid Patterns */
.grid-pattern {
  background-image: 
    linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
  background-size: 100px 100px;
}

/* Responsive Typography */
@media (max-width: 768px) {
  .font-orbitron {
    letter-spacing: 0.025em;
  }
}

/* Custom Utilities */
.border-glow-cyan {
  border-color: #00f5ff;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.bg-cyber {
  background: 
    radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 245, 255, 0.1) 0%, transparent 50%);
}

/* Loading States */
.loading-bar {
  position: relative;
  overflow: hidden;
  background: rgba(0, 245, 255, 0.1);
}

.loading-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.6), transparent);
  animation: loading-sweep 2s infinite;
}

@keyframes loading-sweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Focus States */
button:focus,
a:focus,
input:focus {
  outline: 2px solid #00f5ff;
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .bg-gradient-to-r {
    background: #00f5ff !important;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Custom Form Elements */
input[type="text"],
input[type="email"],
input[type="tel"],
textarea {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 245, 255, 0.3);
  color: white;
  padding: 12px 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
textarea:focus {
  border-color: #00f5ff;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
  outline: none;
}

/* Cyberpunk Button Styles */
.btn-cyber {
  position: relative;
  background: linear-gradient(45deg, #00f5ff, #00d4ff);
  color: #000;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.btn-cyber::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-cyber:hover::before {
  left: 100%;
}

.btn-cyber:hover {
  box-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
  transform: translateY(-2px);
}

/* Glitch Text Effect */
.glitch {
  position: relative;
  color: white;
  font-size: 4em;
  letter-spacing: 0.5em;
  animation: glitch-skew 1s infinite linear alternate-reverse;
}

.glitch::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  left: 2px;
  text-shadow: -2px 0 #ff00c1;
  clip: rect(44px, 450px, 56px, 0);
  animation: glitch-anim 5s infinite linear alternate-reverse;
}

.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  left: -2px;
  text-shadow: -2px 0 #00fff9, 2px 2px #ff00c1;
  animation: glitch-anim2 1s infinite linear alternate-reverse;
}

@keyframes glitch-anim {
  0% { clip: rect(31px, 9999px, 94px, 0); }
  4.166666667% { clip: rect(91px, 9999px, 43px, 0); }
  8.333333333% { clip: rect(65px, 9999px, 19px, 0); }
  12.5% { clip: rect(40px, 9999px, 105px, 0); }
  16.66666667% { clip: rect(79px, 9999px, 46px, 0); }
  20.83333333% { clip: rect(2px, 9999px, 8px, 0); }
  25% { clip: rect(95px, 9999px, 53px, 0); }
  29.16666667% { clip: rect(31px, 9999px, 149px, 0); }
  33.33333333% { clip: rect(135px, 9999px, 51px, 0); }
  37.5% { clip: rect(144px, 9999px, 21px, 0); }
  41.66666667% { clip: rect(29px, 9999px, 98px, 0); }
  45.83333333% { clip: rect(39px, 9999px, 136px, 0); }
  50% { clip: rect(132px, 9999px, 93px, 0); }
  54.16666667% { clip: rect(85px, 9999px, 2px, 0); }
  58.33333333% { clip: rect(23px, 9999px, 30px, 0); }
  62.5% { clip: rect(54px, 9999px, 105px, 0); }
  66.66666667% { clip: rect(29px, 9999px, 84px, 0); }
  70.83333333% { clip: rect(135px, 9999px, 145px, 0); }
  75% { clip: rect(103px, 9999px, 119px, 0); }
  79.16666667% { clip: rect(69px, 9999px, 56px, 0); }
  83.33333333% { clip: rect(23px, 9999px, 85px, 0); }
  87.5% { clip: rect(84px, 9999px, 20px, 0); }
  91.66666667% { clip: rect(132px, 9999px, 61px, 0); }
  95.83333333% { clip: rect(105px, 9999px, 140px, 0); }
  100% { clip: rect(81px, 9999px, 39px, 0); }
}

@keyframes glitch-anim2 {
  0% { clip: rect(65px, 9999px, 119px, 0); }
  4.166666667% { clip: rect(79px, 9999px, 109px, 0); }
  8.333333333% { clip: rect(78px, 9999px, 13px, 0); }
  12.5% { clip: rect(42px, 9999px, 83px, 0); }
  16.66666667% { clip: rect(51px, 9999px, 39px, 0); }
  20.83333333% { clip: rect(137px, 9999px, 61px, 0); }
  25% { clip: rect(38px, 9999px, 31px, 0); }
  29.16666667% { clip: rect(98px, 9999px, 15px, 0); }
  33.33333333% { clip: rect(20px, 9999px, 92px, 0); }
  37.5% { clip: rect(48px, 9999px, 3px, 0); }
  41.66666667% { clip: rect(127px, 9999px, 84px, 0); }
  45.83333333% { clip: rect(87px, 9999px, 134px, 0); }
  50% { clip: rect(26px, 9999px, 28px, 0); }
  54.16666667% { clip: rect(31px, 9999px, 100px, 0); }
  58.33333333% { clip: rect(137px, 9999px, 69px, 0); }
  62.5% { clip: rect(15px, 9999px, 76px, 0); }
  66.66666667% { clip: rect(86px, 9999px, 138px, 0); }
  70.83333333% { clip: rect(34px, 9999px, 50px, 0); }
  75% { clip: rect(63px, 9999px, 17px, 0); }
  79.16666667% { clip: rect(120px, 9999px, 130px, 0); }
  83.33333333% { clip: rect(99px, 9999px, 52px, 0); }
  87.5% { clip: rect(16px, 9999px, 104px, 0); }
  91.66666667% { clip: rect(90px, 9999px, 128px, 0); }
  95.83333333% { clip: rect(18px, 9999px, 143px, 0); }
  100% { clip: rect(29px, 9999px, 42px, 0); }
}

@keyframes glitch-skew {
  0% { transform: skew(0deg); }
  10% { transform: skew(-2deg); }
  20% { transform: skew(1deg); }
  30% { transform: skew(-1deg); }
  40% { transform: skew(2deg); }
  50% { transform: skew(-1deg); }
  60% { transform: skew(0deg); }
  70% { transform: skew(1deg); }
  80% { transform: skew(-2deg); }
  90% { transform: skew(2deg); }
  100% { transform: skew(0deg); }
}