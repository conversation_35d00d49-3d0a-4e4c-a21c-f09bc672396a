// New Lead Trigger Service - Automatically calls new leads with VAPI
import { Lead } from '../types/database';
import { vapiService } from './vapi';
import { db } from './database';

export interface NewLeadTriggerConfig {
  enabled: boolean;
  businessHoursOnly: boolean;
  businessHours: {
    start: number; // 24-hour format (e.g., 9 for 9 AM)
    end: number;   // 24-hour format (e.g., 18 for 6 PM)
  };
  maxCallsPerHour: number;
  delayBetweenCalls: number; // milliseconds
  priorityQualities: string[];
  priorityUrgencies: string[];
}

export class NewLeadTriggerService {
  private config: NewLeadTriggerConfig = {
    enabled: true,
    businessHoursOnly: true,
    businessHours: {
      start: 9,  // 9 AM
      end: 18    // 6 PM
    },
    maxCallsPerHour: 20, // Rate limiting
    delayBetweenCalls: 2000, // 2 seconds between calls
    priorityQualities: ['hot', 'warm'],
    priorityUrgencies: ['asap', 'this_week']
  };

  private callQueue: Lead[] = [];
  private isProcessingQueue = false;
  private callsThisHour = 0;
  private lastHourReset = Date.now();

  constructor(config?: Partial<NewLeadTriggerConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    // Reset call counter every hour
    setInterval(() => {
      this.callsThisHour = 0;
      this.lastHourReset = Date.now();
      console.log('🔄 New lead trigger: Call counter reset');
    }, 60 * 60 * 1000); // Every hour
  }

  // Main trigger function - call this when a new lead is created
  async triggerNewLead(lead: Lead): Promise<boolean> {
    if (!this.config.enabled) {
      console.log('🚫 New lead trigger is disabled');
      return false;
    }

    console.log(`🆕 NEW LEAD DETECTED: ${lead.name} (${lead.phone})`);

    // Check if we should call this lead
    if (!await this.shouldCallLead(lead)) {
      return false;
    }

    // Add to queue for processing
    this.callQueue.push(lead);
    console.log(`📋 Added ${lead.name} to call queue (${this.callQueue.length} leads pending)`);

    // Process queue if not already processing
    if (!this.isProcessingQueue) {
      this.processCallQueue();
    }

    return true;
  }

  // Process the call queue with rate limiting
  private async processCallQueue(): Promise<void> {
    if (this.isProcessingQueue || this.callQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log(`🔄 Processing call queue: ${this.callQueue.length} leads`);

    while (this.callQueue.length > 0) {
      // Check rate limiting
      if (this.callsThisHour >= this.config.maxCallsPerHour) {
        console.log(`⏸️ Rate limit reached (${this.callsThisHour}/${this.config.maxCallsPerHour}), pausing queue`);
        break;
      }

      // Check business hours if enabled
      if (this.config.businessHoursOnly && !this.isBusinessHours()) {
        console.log('🌙 Outside business hours, pausing queue until business hours');
        break;
      }

      const lead = this.callQueue.shift()!;
      
      try {
        console.log(`📞 Initiating automatic call for ${lead.name}...`);
        
        // Update lead status before calling
        await db.updateLead(lead.id, {
          call_status: 'calling',
          call_attempts: lead.call_attempts + 1,
          last_call_at: new Date(),
          notes: `${lead.notes || ''}\n\n🤖 Automatic VAPI call triggered at ${new Date().toLocaleString()}`
        });

        // Trigger the VAPI call
        const call = await vapiService.triggerNewLeadCall(lead);
        
        if (call) {
          // Update lead with call ID
          await db.updateLead(lead.id, {
            vapi_call_id: call.id,
            notes: `${lead.notes || ''}\n✅ Call initiated successfully: ${call.id}`
          });

          this.callsThisHour++;
          console.log(`✅ Successfully called ${lead.name} (${this.callsThisHour}/${this.config.maxCallsPerHour} calls this hour)`);
        } else {
          // Update lead with failure status
          await db.updateLead(lead.id, {
            call_status: 'failed',
            notes: `${lead.notes || ''}\n❌ Automatic call failed at ${new Date().toLocaleString()}`
          });
          console.log(`❌ Failed to call ${lead.name}`);
        }

      } catch (error) {
        console.error(`❌ Error processing call for ${lead.name}:`, error);
        
        // Update lead with error status
        await db.updateLead(lead.id, {
          call_status: 'failed',
          notes: `${lead.notes || ''}\n❌ Call error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }

      // Delay between calls
      if (this.callQueue.length > 0) {
        console.log(`⏳ Waiting ${this.config.delayBetweenCalls}ms before next call...`);
        await new Promise(resolve => setTimeout(resolve, this.config.delayBetweenCalls));
      }
    }

    this.isProcessingQueue = false;
    
    if (this.callQueue.length > 0) {
      console.log(`⏸️ Queue processing paused. ${this.callQueue.length} leads remaining.`);
      
      // Schedule retry during business hours if needed
      if (this.config.businessHoursOnly && !this.isBusinessHours()) {
        this.scheduleBusinessHoursRetry();
      }
    } else {
      console.log('✅ Call queue processing completed');
    }
  }

  // Check if we should call this specific lead
  private async shouldCallLead(lead: Lead): Promise<boolean> {
    // Only call new leads
    if (lead.status !== 'new') {
      console.log(`⏭️ Skipping ${lead.name}: Not a new lead (status: ${lead.status})`);
      return false;
    }

    // Skip if already called
    if (lead.call_attempts > 0) {
      console.log(`⏭️ Skipping ${lead.name}: Already called (${lead.call_attempts} attempts)`);
      return false;
    }

    // Check if lead is too old (should use reactivation campaign instead)
    const leadAge = Date.now() - new Date(lead.created_at).getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (leadAge > maxAge) {
      console.log(`⏰ Skipping ${lead.name}: Lead is older than 24 hours (${Math.round(leadAge / (60 * 60 * 1000))} hours old)`);
      return false;
    }

    // Prioritize high-quality leads
    const isHighPriority = 
      this.config.priorityQualities.includes(lead.quality) ||
      this.config.priorityUrgencies.includes(lead.urgency);

    if (isHighPriority) {
      console.log(`🔥 High priority lead: ${lead.name} (${lead.quality}, ${lead.urgency})`);
      return true;
    }

    // Check business hours for regular leads
    if (this.config.businessHoursOnly && !this.isBusinessHours()) {
      console.log(`🌙 Queueing ${lead.name} for business hours (current time: ${new Date().toLocaleTimeString()})`);
      // Still return true to add to queue, but processing will wait
      return true;
    }

    console.log(`✅ ${lead.name} qualifies for automatic calling`);
    return true;
  }

  // Check if current time is within business hours
  private isBusinessHours(): boolean {
    const now = new Date();
    const hour = now.getHours();
    return hour >= this.config.businessHours.start && hour <= this.config.businessHours.end;
  }

  // Schedule retry during business hours
  private scheduleBusinessHoursRetry(): void {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(this.config.businessHours.start, 0, 0, 0);
    
    const msUntilBusinessHours = tomorrow.getTime() - now.getTime();
    
    console.log(`⏰ Scheduling queue retry for business hours: ${tomorrow.toLocaleString()}`);
    
    setTimeout(() => {
      console.log('🌅 Business hours started, resuming call queue processing');
      this.processCallQueue();
    }, msUntilBusinessHours);
  }

  // Get current queue status
  getQueueStatus(): {
    queueLength: number;
    callsThisHour: number;
    maxCallsPerHour: number;
    isProcessing: boolean;
    isBusinessHours: boolean;
    config: NewLeadTriggerConfig;
  } {
    return {
      queueLength: this.callQueue.length,
      callsThisHour: this.callsThisHour,
      maxCallsPerHour: this.config.maxCallsPerHour,
      isProcessing: this.isProcessingQueue,
      isBusinessHours: this.isBusinessHours(),
      config: this.config
    };
  }

  // Update configuration
  updateConfig(newConfig: Partial<NewLeadTriggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 New lead trigger configuration updated:', this.config);
  }

  // Manual trigger for testing
  async testTrigger(lead: Lead): Promise<boolean> {
    console.log(`🧪 TEST TRIGGER: Manually triggering call for ${lead.name}`);
    return await this.triggerNewLead(lead);
  }
}

// Export singleton instance
export const newLeadTrigger = new NewLeadTriggerService();
