/**
 * Session Notifications Component for PressureMax
 * Displays session-related notifications and warnings
 */

import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, AlertCircle, Info, CheckCircle, Clock } from 'lucide-react';
import { sessionNotifications, SessionNotification } from '../services/sessionNotifications';

interface SessionNotificationsProps {
  className?: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export const SessionNotifications: React.FC<SessionNotificationsProps> = ({
  className = '',
  position = 'top-right'
}) => {
  const [notifications, setNotifications] = useState<SessionNotification[]>([]);

  useEffect(() => {
    // Subscribe to notification changes
    const unsubscribe = sessionNotifications.subscribe(setNotifications);

    // Load initial notifications
    setNotifications(sessionNotifications.getNotifications());

    return unsubscribe;
  }, []);

  const getIcon = (type: SessionNotification['type']) => {
    switch (type) {
      case 'error':
        return <AlertCircle size={20} className="text-red-400" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-yellow-400" />;
      case 'success':
        return <CheckCircle size={20} className="text-green-400" />;
      case 'info':
      default:
        return <Info size={20} className="text-blue-400" />;
    }
  };

  const getBackgroundColor = (type: SessionNotification['type']) => {
    switch (type) {
      case 'error':
        return 'bg-red-900/30 border-red-600';
      case 'warning':
        return 'bg-yellow-900/30 border-yellow-600';
      case 'success':
        return 'bg-green-900/30 border-green-600';
      case 'info':
      default:
        return 'bg-blue-900/30 border-blue-600';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
      default:
        return 'top-4 right-4';
    }
  };

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div className={`fixed ${getPositionClasses()} z-50 space-y-3 max-w-sm ${className}`}>
      {notifications.map((notification) => (
        <div
          key={notification.id}
          className={`${getBackgroundColor(notification.type)} rounded-xl border p-4 shadow-lg backdrop-blur-sm animate-in slide-in-from-right duration-300`}
        >
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-0.5">
              {getIcon(notification.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h4 className="text-white font-semibold text-sm">
                  {notification.title}
                </h4>
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-1 text-gray-400">
                    <Clock size={12} />
                    <span className="text-xs">{formatTime(notification.timestamp)}</span>
                  </div>
                  <button
                    onClick={() => sessionNotifications.removeNotification(notification.id)}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
              
              <p className="text-gray-300 text-sm mb-3">
                {notification.message}
              </p>

              {notification.actions && notification.actions.length > 0 && (
                <div className="flex items-center space-x-2">
                  {notification.actions.map((action, index) => (
                    <button
                      key={index}
                      onClick={action.action}
                      className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                        action.style === 'primary'
                          ? 'bg-cyan-600 hover:bg-cyan-700 text-white'
                          : action.style === 'danger'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                      }`}
                    >
                      {action.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Hook to use session notifications
 */
export const useSessionNotifications = () => {
  const [notifications, setNotifications] = useState<SessionNotification[]>([]);

  useEffect(() => {
    const unsubscribe = sessionNotifications.subscribe(setNotifications);
    setNotifications(sessionNotifications.getNotifications());
    return unsubscribe;
  }, []);

  return {
    notifications,
    addNotification: sessionNotifications.addNotification.bind(sessionNotifications),
    removeNotification: sessionNotifications.removeNotification.bind(sessionNotifications),
    clearAll: sessionNotifications.clearAllNotifications.bind(sessionNotifications),
    notifySuccess: sessionNotifications.notifySuccess.bind(sessionNotifications),
    notifyError: sessionNotifications.notifyError.bind(sessionNotifications),
    notifyInfo: sessionNotifications.notifyInfo.bind(sessionNotifications),
    notifyFacebookSessionIssue: sessionNotifications.notifyFacebookSessionIssue.bind(sessionNotifications)
  };
};
