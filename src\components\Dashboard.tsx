/**
 * Main Dashboard Component for PressureMax
 * Contains the authenticated user interface
 */

import React, { useState, useEffect } from 'react';
import {
  Zap,
  MessageSquare,
  Target,
  Phone,
  Layout,
  BarChart3,
  Settings,
  Plus,
  Play,
  Edit,
  Copy,
  Eye,
  User,
  LogOut,
  Shield,
  TrendingUp
} from 'lucide-react';
import { db } from '../services/database';
import { AdTemplate, Lead } from '../types/database';
import { TemplateModal } from './TemplateModal';
import { CampaignWizardPage } from './CampaignWizardPage';
import { LeadCaptureForm } from './LeadCaptureForm';
import { LeadDetailsModal } from './LeadDetailsModal';
import { analyticsService, AnalyticsSummary, LeadQualityMetrics, PerformanceByService } from '../services/analytics';
import { ABTestManager } from './ABTestManager';
import { IntegrationsHub } from './IntegrationsHub';
import { IntegrationsPage } from './IntegrationsPage';
import { MyCampaigns } from './MyCampaigns';
import { facebookIntegration } from '../services/facebookIntegration';
import { OnboardingFlow } from './OnboardingFlow';
import { HelpCenter } from './HelpCenter';
import { UserProfileModal } from './UserProfileModal';
import { LeadManagementDashboard } from './LeadManagementDashboard';
import { AdminDashboard } from './AdminDashboard';
import { facebookApi, FacebookTargeting } from '../services/facebookApi';
import { vapiService } from '../services/vapi';
import { useAuth } from '../contexts/AuthContext';
import { useAdminAuth } from '../services/adminAuth';
import { SessionStatus } from './SessionStatus';
import { automaticDataLoader } from '../services/automaticDataLoader';
import { templateOptimizer } from '../services/templateOptimizer';
import { ProductionDataStatus } from './ProductionDataStatus';
import { SessionNotifications } from './SessionNotifications';

export const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const { isAdmin } = useAdminAuth();
  const [isLoaded, setIsLoaded] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [templates, setTemplates] = useState<AdTemplate[]>([]);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<AdTemplate | null>(null);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);

  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [isLeadDetailsOpen, setIsLeadDetailsOpen] = useState(false);
  const [launchingCampaignId, setLaunchingCampaignId] = useState<string | null>(null);
  const [analyticsSummary, setAnalyticsSummary] = useState<AnalyticsSummary | null>(null);
  const [leadQualityMetrics, setLeadQualityMetrics] = useState<LeadQualityMetrics | null>(null);
  const [performanceByService, setPerformanceByService] = useState<PerformanceByService[]>([]);
  const [isOnboardingOpen, setIsOnboardingOpen] = useState(false);
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [wizardTemplate, setWizardTemplate] = useState<AdTemplate | null>(null);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 500);

    // Check onboarding completion
    const onboardingCompleted = localStorage.getItem('pressuremax_onboarding_completed');
    setHasCompletedOnboarding(onboardingCompleted === 'true');

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isLoaded) {
      loadData();
      // Initialize automatic data loading
      initializeAutomaticDataLoading();
    }
  }, [isLoaded]);

  const initializeAutomaticDataLoading = async () => {
    try {
      console.log('🚀 Initializing automatic Facebook data loading...');
      await automaticDataLoader.initialize();
      console.log('✅ Automatic data loading initialized');

      // Run template optimization after data loading
      setTimeout(async () => {
        try {
          console.log('🎯 Running template optimization...');
          const optimizations = await templateOptimizer.optimizeAllTemplates();

          if (optimizations.length > 0) {
            console.log(`💡 Found ${optimizations.length} template optimizations`);
            // Auto-apply optimizations for better performance
            await templateOptimizer.applyOptimizations(optimizations);
            console.log('✅ Template optimizations applied');

            // Reload templates to show optimized versions
            loadData();
          } else {
            console.log('ℹ️ No template optimizations needed');
          }
        } catch (error) {
          console.error('❌ Error in template optimization:', error);
        }
      }, 5000); // Wait 5 seconds for data to load

    } catch (error) {
      console.error('❌ Error initializing automatic data loading:', error);
    }
  };

  const loadData = async () => {
    try {
      const [templatesData, leadsData] = await Promise.all([
        db.getAdTemplates(),
        db.getLeads()
      ]);
      
      setTemplates(templatesData);
      setLeads(leadsData);

      // Load analytics data
      const [summary, quality, performance] = await Promise.all([
        analyticsService.getAnalyticsSummary(),
        analyticsService.getLeadQualityMetrics(),
        analyticsService.getPerformanceByService()
      ]);

      setAnalyticsSummary(summary);
      setLeadQualityMetrics(quality);
      setPerformanceByService(performance);
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleTemplateSelect = (template: AdTemplate) => {
    setSelectedTemplate(template);
    setIsTemplateModalOpen(true);
  };

  const handleCloseTemplateModal = () => {
    setIsTemplateModalOpen(false);
    setSelectedTemplate(null);
  };

  const handleDeployTemplate = (template: AdTemplate) => {
    setWizardTemplate(template);
    setActiveTab('campaign-wizard');
  };



  const handleDeploySuccess = (campaignId: string) => {
    console.log('Campaign deployed successfully:', campaignId);
    loadData(); // Reload data to show new campaign
  };

  const handleWizardSuccess = (campaignId: string) => {
    console.log('Campaign created successfully:', campaignId);
    setActiveTab('dashboard'); // Go back to dashboard
    setWizardTemplate(null);
    loadData(); // Reload data to show new campaign
  };

  const handleWizardBack = () => {
    setActiveTab('dashboard');
    setWizardTemplate(null);
  };

  const handleLeadClick = (lead: Lead) => {
    setSelectedLead(lead);
    setIsLeadDetailsOpen(true);
  };

  const handleLeadUpdate = (updatedLead: Lead) => {
    setLeads(prev => prev.map(lead =>
      lead.id === updatedLead.id ? updatedLead : lead
    ));
    setSelectedLead(updatedLead);
  };

  const handleCloseLeadDetails = () => {
    setIsLeadDetailsOpen(false);
    setSelectedLead(null);
  };

  const handleLaunchFacebookCampaign = async (template: AdTemplate) => {
    // Check if user is authenticated
    if (!user) {
      alert('Please log in to launch campaigns');
      return;
    }

    // Check if this specific campaign is already launching
    if (launchingCampaignId === template.id) return;

    // Check if Facebook is connected
    const facebookStatus = facebookIntegration.getIntegrationStatus();
    if (!facebookStatus.isConnected) {
      alert('❌ Facebook Business account not connected. Please go to Integrations → Facebook Business to connect your account.');
      setActiveTab('integrations');
      return;
    }

    // Check if user has required permissions
    if (!facebookIntegration.hasRequiredPermissions()) {
      alert('❌ Missing required Facebook permissions. Please reconnect your Facebook account with proper permissions.');
      setActiveTab('integrations');
      return;
    }

    // Check if user has ad accounts
    if (facebookStatus.adAccounts.length === 0) {
      alert('❌ No Facebook ad accounts found. Please ensure your Facebook Business account has ad accounts set up.');
      return;
    }

    setLaunchingCampaignId(template.id);

    try {
      // Use the campaign wizard instead of direct launch
      setWizardTemplate(template);
      setActiveTab('campaign-wizard');

      alert('🚀 Opening campaign wizard with your template. You can customize targeting and budget before launching.');

    } catch (error) {
      console.error('Error launching campaign:', error);
      alert(`Error launching campaign: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLaunchingCampaignId(null);
    }
  };

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Layout },
    { id: 'templates', label: 'Ad Templates', icon: Target },
    { id: 'leads', label: 'Leads', icon: MessageSquare },
    { id: 'integrations', label: 'Integrations', icon: Zap },
    { id: 'analytics', label: 'Analytics', icon: BarChart3 },
    { id: 'settings', label: 'Settings', icon: Settings },
    ...(isAdmin(user) ? [{ id: 'admin', label: 'Admin Panel', icon: Shield }] : [])
  ];

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 flex items-center justify-center">
        <div className="text-center space-y-6">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-700 border-t-cyan-500 mx-auto"></div>
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-cyan-500/20 to-blue-500/20 blur-xl"></div>
          </div>
          <div className="space-y-2">
            <p className="text-xl font-semibold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Loading PressureMax</p>
            <p className="text-gray-400">Preparing your premium dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  // If campaign wizard is active, render it instead of the dashboard
  if (activeTab === 'campaign-wizard' && wizardTemplate) {
    return (
      <CampaignWizardPage
        template={wizardTemplate}
        onSuccess={handleWizardSuccess}
        onBack={handleWizardBack}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 text-white">
      {/* Header */}
      <header className="bg-gray-900/50 backdrop-blur-xl border-b border-gray-700/50 px-6 py-4 sticky top-0 z-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-cyan-500/25">
              <Zap className="text-white" size={24} />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">PressureMax</h1>
          </div>

          <div className="flex items-center space-x-4">
            <span className="text-gray-300 font-medium">Welcome, {user?.name}</span>
            <SessionStatus showDetails={true} />
            <button
              onClick={() => setIsProfileModalOpen(true)}
              className="flex items-center space-x-2 bg-gray-800/50 hover:bg-gray-700/50 px-3 py-2 rounded-lg transition-all duration-200 border border-gray-700/50"
            >
              <User size={18} />
              <span className="text-cyan-400 font-medium">{user?.plan}</span>
            </button>
            <button
              onClick={handleLogout}
              className="flex items-center space-x-2 bg-gray-800/50 hover:bg-red-500/20 px-3 py-2 rounded-lg transition-all duration-200 border border-gray-700/50 hover:border-red-500/50"
            >
              <LogOut size={18} />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <nav className="w-64 bg-gray-900/30 backdrop-blur-xl border-r border-gray-700/50 min-h-screen">
          <div className="p-6">
            <ul className="space-y-3">
              {navigationItems.map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-all duration-200 rounded-xl ${
                      activeTab === item.id
                        ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-semibold shadow-lg shadow-cyan-500/25'
                        : 'text-gray-300 hover:bg-gray-800/50 hover:text-white hover:shadow-md'
                    }`}
                  >
                    <item.icon size={20} />
                    <span>{item.label}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-8 bg-gradient-to-br from-gray-900/20 to-slate-800/20">
          {activeTab === 'dashboard' && (
            <div className="space-y-8">
              <div className="flex items-center justify-between">
                <h2 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Dashboard</h2>
                <div className="flex gap-3">
                  <button
                    onClick={async () => {
                      console.log('🔍 Facebook Integration Debug Info:');
                      const status = facebookIntegration.getIntegrationStatus();
                      console.log('Connected:', status.isConnected);
                      console.log('Pages:', status.pages?.length || 0);
                      console.log('Ad Accounts:', status.adAccounts?.length || 0);
                      console.log('Permissions:', status.permissions);

                      if (status.isConnected) {
                        console.log('🔄 Forcing data refresh...');
                        await (window as any).facebookDataService?.forceRefreshData();
                      }
                    }}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                  >
                    Debug FB
                  </button>
                  {!hasCompletedOnboarding && (
                    <button
                      onClick={() => setIsOnboardingOpen(true)}
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-6 py-3 font-semibold rounded-xl hover:from-cyan-400 hover:to-blue-400 transition-all duration-200 shadow-lg shadow-cyan-500/25"
                    >
                      Complete Setup
                    </button>
                  )}
                </div>
              </div>

              {/* Production Data Status */}
              <ProductionDataStatus />

              {/* Quick Stats */}
              {analyticsSummary && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl p-6 rounded-2xl border border-gray-700/50 hover:border-cyan-500/50 transition-all duration-300 group">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-300">Total Leads</h3>
                      <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <MessageSquare className="text-white" size={20} />
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-cyan-400">{analyticsSummary.totalLeads}</p>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl p-6 rounded-2xl border border-gray-700/50 hover:border-green-500/50 transition-all duration-300 group">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-300">Active Campaigns</h3>
                      <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <Target className="text-white" size={20} />
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-green-400">{analyticsSummary.activeCampaigns}</p>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl p-6 rounded-2xl border border-gray-700/50 hover:border-yellow-500/50 transition-all duration-300 group">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-300">Total Spend</h3>
                      <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <BarChart3 className="text-white" size={20} />
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-yellow-400">${analyticsSummary.totalSpend}</p>
                  </div>
                  <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl p-6 rounded-2xl border border-gray-700/50 hover:border-purple-500/50 transition-all duration-300 group">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-300">Avg Cost/Lead</h3>
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <TrendingUp className="text-white" size={20} />
                      </div>
                    </div>
                    <p className="text-3xl font-bold text-purple-400">${analyticsSummary.avgCostPerLead}</p>
                  </div>
                </div>
              )}

              {/* My Campaigns */}
              <MyCampaigns
                onEditCampaign={(campaign) => {
                  // TODO: Implement campaign editing
                  console.log('Edit campaign:', campaign);
                }}
                onViewDetails={(campaign) => {
                  // TODO: Implement campaign details view
                  console.log('View campaign details:', campaign);
                }}
              />
            </div>
          )}

          {activeTab === 'templates' && (
            <div className="space-y-8">
              <div className="flex items-center justify-between">
                <h2 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Ad Templates</h2>
                <button
                  onClick={() => setIsTemplateModalOpen(true)}
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-6 py-3 font-semibold rounded-xl hover:from-cyan-400 hover:to-blue-400 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-cyan-500/25"
                >
                  <Plus size={20} />
                  <span>Create Template</span>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {templates.map((template) => (
                  <div key={template.id} className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:border-cyan-500/50 transition-all duration-300 group">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-white">{template.name}</h3>
                        <p className="text-sm text-gray-400 capitalize">{template.category}</p>
                      </div>
                      <span className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-3 py-1 text-xs font-semibold rounded-full">
                        ${template.budget_range.min}-${template.budget_range.max}
                      </span>
                    </div>

                    {/* Template Image */}
                    {template.creative.image_path && (
                      <div className="mb-4 relative overflow-hidden rounded-xl border border-cyan-500/20">
                        <img
                          src={template.creative.image_path}
                          alt={template.name}
                          className="w-full h-36 object-cover group-hover:scale-105 transition-transform duration-300"
                          onError={(e) => {
                            // Hide image if it fails to load
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                      </div>
                    )}

                    <div className="space-y-3 mb-6">
                      <p className="text-sm text-gray-200 font-medium">{template.creative.headline}</p>
                      <p className="text-xs text-gray-400 line-clamp-2">{template.creative.primary_text.substring(0, 100)}...</p>
                    </div>

                    <div className="flex space-x-3">
                      <button
                        onClick={() => handleLaunchFacebookCampaign(template)}
                        disabled={launchingCampaignId === template.id}
                        className={`flex-1 py-3 transition-all duration-300 flex items-center justify-center space-x-2 font-semibold rounded-lg ${
                          launchingCampaignId === template.id
                            ? 'bg-gray-600/50 text-gray-400 cursor-not-allowed'
                            : 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white hover:from-cyan-400 hover:to-blue-400 shadow-lg shadow-cyan-500/25'
                        }`}
                      >
                        {launchingCampaignId === template.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent"></div>
                            <span>Launching...</span>
                          </>
                        ) : (
                          <>
                            <Play size={16} />
                            <span>Launch</span>
                          </>
                        )}
                      </button>

                      <button
                        onClick={() => handleTemplateSelect(template)}
                        className="px-4 py-3 bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white transition-all duration-200 rounded-lg border border-gray-600/50"
                      >
                        <Eye size={16} />
                      </button>

                      <button
                        onClick={() => handleDeployTemplate(template)}
                        className="px-4 py-3 bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white transition-all duration-200 rounded-lg border border-gray-600/50"
                      >
                        <Edit size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'leads' && (
            <LeadManagementDashboard />
          )}

          {activeTab === 'integrations' && (
            <IntegrationsPage />
          )}

          {activeTab === 'admin' && isAdmin(user) && (
            <AdminDashboard />
          )}



          {/* Other tabs would go here */}
        </main>
      </div>

      {/* Modals */}
      <TemplateModal
        isOpen={isTemplateModalOpen}
        onClose={handleCloseTemplateModal}
        template={selectedTemplate}
        onSave={loadData}
      />



      <LeadDetailsModal
        isOpen={isLeadDetailsOpen}
        onClose={handleCloseLeadDetails}
        lead={selectedLead}
        onLeadUpdate={handleLeadUpdate}
      />

      <OnboardingFlow
        isOpen={isOnboardingOpen}
        onClose={() => setIsOnboardingOpen(false)}
        onComplete={() => {
          setIsOnboardingOpen(false);
          setHasCompletedOnboarding(true);
          localStorage.setItem('pressuremax_onboarding_completed', 'true');
        }}
      />

      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
      />

      {/* Session Notifications */}
      <SessionNotifications position="top-right" />
    </div>
  );
};
