"""
Facebook Marketing API Integration for PressureMax
Handles campaign creation, management, and analytics
"""

import os
import json
from typing import Dict, List, Optional, Any
from facebook_business.api import FacebookAdsApi
from facebook_business.adobjects.adaccount import AdAccount
from facebook_business.adobjects.campaign import Campaign
from facebook_business.adobjects.adset import AdSet
from facebook_business.adobjects.ad import Ad
from facebook_business.adobjects.adcreative import AdCreative
from facebook_business.adobjects.adimage import AdImage
from facebook_business.adobjects.user import User
from facebook_business.exceptions import FacebookRequestError

class FacebookMarketingAPI:
    def __init__(self, app_id: str, app_secret: str, access_token: str):
        """Initialize Facebook Marketing API client"""
        self.app_id = app_id
        self.app_secret = app_secret
        self.access_token = access_token
        
        # Initialize the API
        FacebookAdsApi.init(app_id, app_secret, access_token)
        self.api = FacebookAdsApi.get_default_api()
        
        # Get user and ad accounts
        self.user = User(fbid='me')
        self.ad_accounts = None
        
    def get_ad_accounts(self) -> List[Dict]:
        """Get all ad accounts for the authenticated user"""
        try:
            if not self.ad_accounts:
                accounts = list(self.user.get_ad_accounts(fields=[
                    AdAccount.Field.account_id,
                    AdAccount.Field.name,
                    AdAccount.Field.account_status,
                    AdAccount.Field.currency,
                    AdAccount.Field.timezone_name,
                    AdAccount.Field.amount_spent,
                    AdAccount.Field.balance
                ]))
                self.ad_accounts = [account._data for account in accounts]
            
            return self.ad_accounts
        except FacebookRequestError as e:
            raise Exception(f"Failed to get ad accounts: {e}")
    
    def create_campaign(self, account_id: str, campaign_data: Dict) -> Dict:
        """Create a new Facebook campaign"""
        try:
            account = AdAccount(f'act_{account_id}')
            
            # Campaign parameters
            params = {
                Campaign.Field.name: campaign_data['name'],
                Campaign.Field.objective: campaign_data.get('objective', Campaign.Objective.lead_generation),
                Campaign.Field.status: campaign_data.get('status', Campaign.Status.paused),
                Campaign.Field.special_ad_categories: campaign_data.get('special_ad_categories', [])
            }
            
            # Add budget if provided
            if 'daily_budget' in campaign_data:
                params[Campaign.Field.daily_budget] = campaign_data['daily_budget']
            
            campaign = account.create_campaign(params=params)
            return campaign._data
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to create campaign: {e}")
    
    def create_ad_set(self, campaign_id: str, adset_data: Dict) -> Dict:
        """Create an ad set within a campaign"""
        try:
            campaign = Campaign(campaign_id)
            
            # Ad set parameters
            params = {
                AdSet.Field.name: adset_data['name'],
                AdSet.Field.campaign_id: campaign_id,
                AdSet.Field.daily_budget: adset_data['daily_budget'],
                AdSet.Field.billing_event: adset_data.get('billing_event', AdSet.BillingEvent.impressions),
                AdSet.Field.optimization_goal: adset_data.get('optimization_goal', AdSet.OptimizationGoal.lead_generation),
                AdSet.Field.status: adset_data.get('status', AdSet.Status.paused),
                AdSet.Field.targeting: adset_data['targeting']
            }
            
            # Add schedule if provided
            if 'start_time' in adset_data:
                params[AdSet.Field.start_time] = adset_data['start_time']
            if 'end_time' in adset_data:
                params[AdSet.Field.end_time] = adset_data['end_time']
            
            adset = campaign.create_ad_set(params=params)
            return adset._data
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to create ad set: {e}")
    
    def upload_image(self, account_id: str, image_path: str) -> Dict:
        """Upload an image for use in ads"""
        try:
            account = AdAccount(f'act_{account_id}')
            
            image = account.create_ad_image(params={
                AdImage.Field.filename: image_path
            })
            
            return image._data
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to upload image: {e}")
    
    def create_ad_creative(self, account_id: str, creative_data: Dict) -> Dict:
        """Create an ad creative"""
        try:
            account = AdAccount(f'act_{account_id}')
            
            # Build creative spec
            creative_spec = {
                'name': creative_data['name'],
                'object_story_spec': {
                    'page_id': creative_data['page_id'],
                    'link_data': {
                        'message': creative_data['message'],
                        'link': creative_data['link'],
                        'caption': creative_data.get('caption', ''),
                        'description': creative_data.get('description', ''),
                        'name': creative_data.get('headline', ''),
                        'call_to_action': {
                            'type': creative_data.get('call_to_action', 'LEARN_MORE')
                        }
                    }
                }
            }
            
            # Add image if provided
            if 'image_hash' in creative_data:
                creative_spec['object_story_spec']['link_data']['image_hash'] = creative_data['image_hash']
            
            params = {
                AdCreative.Field.name: creative_data['name'],
                AdCreative.Field.object_story_spec: creative_spec['object_story_spec']
            }
            
            creative = account.create_ad_creative(params=params)
            return creative._data
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to create ad creative: {e}")
    
    def create_ad(self, adset_id: str, creative_id: str, ad_data: Dict) -> Dict:
        """Create an ad"""
        try:
            adset = AdSet(adset_id)
            
            params = {
                Ad.Field.name: ad_data['name'],
                Ad.Field.adset_id: adset_id,
                Ad.Field.creative: {'creative_id': creative_id},
                Ad.Field.status: ad_data.get('status', Ad.Status.paused)
            }
            
            ad = adset.create_ad(params=params)
            return ad._data
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to create ad: {e}")
    
    def get_campaign_insights(self, campaign_id: str, date_range: Dict) -> Dict:
        """Get campaign performance insights"""
        try:
            campaign = Campaign(campaign_id)
            
            insights = campaign.get_insights(
                fields=[
                    'campaign_name',
                    'impressions',
                    'clicks',
                    'spend',
                    'cpm',
                    'cpc',
                    'ctr',
                    'actions',
                    'cost_per_action_type'
                ],
                params={
                    'time_range': date_range,
                    'level': 'campaign'
                }
            )
            
            return [insight._data for insight in insights]
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to get campaign insights: {e}")
    
    def pause_campaign(self, campaign_id: str) -> Dict:
        """Pause a campaign"""
        try:
            campaign = Campaign(campaign_id)
            campaign.api_update(params={Campaign.Field.status: Campaign.Status.paused})
            return {'status': 'paused', 'campaign_id': campaign_id}
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to pause campaign: {e}")
    
    def resume_campaign(self, campaign_id: str) -> Dict:
        """Resume a campaign"""
        try:
            campaign = Campaign(campaign_id)
            campaign.api_update(params={Campaign.Field.status: Campaign.Status.active})
            return {'status': 'active', 'campaign_id': campaign_id}
            
        except FacebookRequestError as e:
            raise Exception(f"Failed to resume campaign: {e}")

# Configuration
FACEBOOK_CONFIG = {
    'app_id': '394349039883481',
    'app_secret': os.getenv('FACEBOOK_APP_SECRET'),  # Store in environment variable
    'access_token': 'EAAFmqIpwlNkBOxLrBcpEhlhpwtBZAysLZAgXqJL6wN7ZAKd0kZC3v7NyWd6nWBprAEk7Be4dDuJFwCFZBFTQyPDOWwFK3rcPTXm6Kp8SpzfzumSu4KGhVZAP3xdqZCT1s0tRql38Win88NNsUId2I1txKX8zvsyo9Fs98KA5RpkzM1eIbxgo9T4a41cZA0DkdoVEEDGpXQCZBp51YgqThsjsahodY'
}

def get_facebook_api() -> FacebookMarketingAPI:
    """Get configured Facebook Marketing API instance"""
    return FacebookMarketingAPI(
        app_id=FACEBOOK_CONFIG['app_id'],
        app_secret=FACEBOOK_CONFIG['app_secret'],
        access_token=FACEBOOK_CONFIG['access_token']
    )
