"""
Flask API Server for PressureMax Facebook Integration
Provides REST endpoints for Facebook Marketing API operations and authentication
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import traceback
from functools import wraps
from facebook_api import get_facebook_api, FacebookMarketingAPI
from auth import get_auth_service
from vapi_service import get_vapi_service

app = Flask(__name__)
CORS(app)  # Enable CORS for React frontend

# Initialize services
fb_api = None
auth_service = get_auth_service()
vapi_service = None

def init_facebook_api():
    """Initialize Facebook API with error handling"""
    global fb_api
    try:
        fb_api = get_facebook_api()
        return True
    except Exception as e:
        print(f"Failed to initialize Facebook API: {e}")
        return False

def require_auth(f):
    """Decorator to require authentication for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'Authentication required'}), 401

        token = auth_header.split(' ')[1]
        payload = auth_service.verify_token(token)

        if not payload or payload.get('type') != 'access':
            return jsonify({'error': 'Invalid or expired token'}), 401

        # Add user info to request context
        request.current_user_id = payload['user_id']
        return f(*args, **kwargs)

    return decorated_function

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({'status': 'healthy', 'service': 'PressureMax Facebook API'})

# Authentication Routes
@app.route('/api/auth/signup', methods=['POST'])
def signup():
    """User registration endpoint"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['email', 'password', 'name', 'company_name']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Check if email already exists
        if auth_service.email_exists(data['email']):
            return jsonify({'error': 'Email already registered'}), 400

        # Create user
        user = auth_service.create_user(data)

        # Generate tokens
        access_token = auth_service.create_access_token(user['id'])
        refresh_token = auth_service.create_refresh_token(user['id'])

        return jsonify({
            'user': user,
            'token': access_token,
            'refreshToken': refresh_token
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """User login endpoint"""
    try:
        data = request.get_json()
        print(f"Login attempt - Data received: {data}")

        # Validate required fields
        if not data.get('email') or not data.get('password'):
            print("Missing email or password")
            return jsonify({'error': 'Email and password are required'}), 400

        # Authenticate user
        print(f"Attempting to authenticate user: {data['email']}")
        user = auth_service.authenticate_user(data['email'], data['password'])
        print(f"Authentication result: {user}")

        if not user:
            print("Authentication failed - invalid credentials")
            return jsonify({'error': 'Invalid email or password'}), 401

        # Generate tokens
        access_token = auth_service.create_access_token(user['id'])
        refresh_token = auth_service.create_refresh_token(user['id'])

        print(f"Login successful for user: {user['email']}")
        return jsonify({
            'user': user,
            'token': access_token,
            'refreshToken': refresh_token
        })

    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/logout', methods=['POST'])
@require_auth
def logout():
    """User logout endpoint"""
    # In a real implementation, you might want to blacklist the token
    return jsonify({'message': 'Logged out successfully'})

@app.route('/api/auth/refresh', methods=['POST'])
def refresh_token():
    """Refresh access token endpoint"""
    try:
        data = request.get_json()
        refresh_token = data.get('refreshToken')

        if not refresh_token:
            return jsonify({'error': 'Refresh token required'}), 400

        # Verify refresh token
        payload = auth_service.verify_token(refresh_token)
        if not payload or payload.get('type') != 'refresh':
            return jsonify({'error': 'Invalid or expired refresh token'}), 401

        # Get user
        user = auth_service.get_user_by_id(payload['user_id'])
        if not user:
            return jsonify({'error': 'User not found'}), 404

        # Generate new tokens
        new_access_token = auth_service.create_access_token(user['id'])
        new_refresh_token = auth_service.create_refresh_token(user['id'])

        return jsonify({
            'user': user,
            'token': new_access_token,
            'refreshToken': new_refresh_token
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/me', methods=['GET'])
@require_auth
def get_current_user():
    """Get current user profile"""
    try:
        user = auth_service.get_user_by_id(request.current_user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify(user)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/profile', methods=['PUT'])
@require_auth
def update_profile():
    """Update user profile"""
    try:
        data = request.get_json()

        # Update user
        user = auth_service.update_user(request.current_user_id, data)
        if not user:
            return jsonify({'error': 'User not found'}), 404

        return jsonify(user)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/password-reset-request', methods=['POST'])
def request_password_reset():
    """Request password reset"""
    try:
        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({'error': 'Email is required'}), 400

        # Create reset token
        token = auth_service.create_password_reset_token(email)
        if token:
            # Send email (mock implementation)
            auth_service.send_password_reset_email(email, token)

        # Always return success to prevent email enumeration
        return jsonify({'message': 'If the email exists, a reset link has been sent'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/password-reset', methods=['POST'])
def reset_password():
    """Reset password with token"""
    try:
        data = request.get_json()
        token = data.get('token')
        password = data.get('password')

        if not token or not password:
            return jsonify({'error': 'Token and password are required'}), 400

        # Reset password
        success = auth_service.reset_password(token, password)
        if not success:
            return jsonify({'error': 'Invalid or expired token'}), 400

        return jsonify({'message': 'Password reset successfully'})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/accounts', methods=['GET'])
@require_auth
def get_ad_accounts():
    """Get all ad accounts for the authenticated user"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500

        accounts = fb_api.get_ad_accounts()
        return jsonify({'accounts': accounts})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/campaign', methods=['POST'])
@require_auth
def create_campaign():
    """Create a new Facebook campaign"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500

        data = request.get_json()

        # Validate required fields
        required_fields = ['account_id', 'name']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        campaign = fb_api.create_campaign(data['account_id'], data)
        return jsonify({'campaign': campaign})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/adset', methods=['POST'])
@require_auth
def create_ad_set():
    """Create a new ad set"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500

        data = request.get_json()

        # Validate required fields
        required_fields = ['campaign_id', 'name', 'daily_budget', 'targeting']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        adset = fb_api.create_ad_set(data['campaign_id'], data)
        return jsonify({'adset': adset})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/upload-image', methods=['POST'])
def upload_image():
    """Upload an image for ads"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        data = request.get_json()
        
        if 'account_id' not in data or 'image_path' not in data:
            return jsonify({'error': 'Missing account_id or image_path'}), 400
        
        image = fb_api.upload_image(data['account_id'], data['image_path'])
        return jsonify({'image': image})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/creative', methods=['POST'])
def create_ad_creative():
    """Create an ad creative"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['account_id', 'name', 'page_id', 'message', 'link']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        creative = fb_api.create_ad_creative(data['account_id'], data)
        return jsonify({'creative': creative})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/ad', methods=['POST'])
def create_ad():
    """Create an ad"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['adset_id', 'creative_id', 'name']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        ad = fb_api.create_ad(data['adset_id'], data['creative_id'], data)
        return jsonify({'ad': ad})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/campaign/<campaign_id>/insights', methods=['GET'])
def get_campaign_insights(campaign_id):
    """Get campaign performance insights"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        # Get date range from query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        if not start_date or not end_date:
            return jsonify({'error': 'Missing start_date or end_date parameters'}), 400
        
        date_range = {
            'since': start_date,
            'until': end_date
        }
        
        insights = fb_api.get_campaign_insights(campaign_id, date_range)
        return jsonify({'insights': insights})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/campaign/<campaign_id>/pause', methods=['POST'])
def pause_campaign(campaign_id):
    """Pause a campaign"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        result = fb_api.pause_campaign(campaign_id)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/campaign/<campaign_id>/resume', methods=['POST'])
def resume_campaign(campaign_id):
    """Resume a campaign"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        result = fb_api.resume_campaign(campaign_id)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/facebook/launch-campaign', methods=['POST'])
def launch_campaign_from_template():
    """Launch a complete campaign from a PressureMax template"""
    try:
        if not fb_api:
            if not init_facebook_api():
                return jsonify({'error': 'Facebook API not initialized'}), 500
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['account_id', 'template', 'targeting', 'budget']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        template = data['template']
        account_id = data['account_id']
        targeting = data['targeting']
        budget = data['budget']
        
        # Step 1: Create Campaign
        campaign_data = {
            'name': f"PressureMax - {template['name']}",
            'objective': 'LEAD_GENERATION',
            'status': 'PAUSED'  # Start paused for review
        }
        campaign = fb_api.create_campaign(account_id, campaign_data)
        
        # Step 2: Create Ad Set
        adset_data = {
            'name': f"{template['name']} - Ad Set",
            'daily_budget': budget['daily_budget'] * 100,  # Convert to cents
            'targeting': targeting,
            'optimization_goal': 'LEAD_GENERATION',
            'billing_event': 'IMPRESSIONS'
        }
        adset = fb_api.create_ad_set(campaign['id'], adset_data)
        
        # Step 3: Create Ad Creative
        creative_data = {
            'name': f"{template['name']} - Creative",
            'page_id': data.get('page_id'),
            'message': template['creative']['primary_text'],
            'headline': template['creative']['headline'],
            'description': template['creative']['description'],
            'link': data.get('landing_page_url', 'https://pressuremax.ai'),
            'call_to_action': template['creative']['call_to_action']
        }
        
        # Add image if available
        if template['creative'].get('image_path'):
            # Upload image first
            image_path = f"public{template['creative']['image_path']}"
            if os.path.exists(image_path):
                image = fb_api.upload_image(account_id, image_path)
                creative_data['image_hash'] = image['hash']
        
        creative = fb_api.create_ad_creative(account_id, creative_data)
        
        # Step 4: Create Ad
        ad_data = {
            'name': f"{template['name']} - Ad",
            'status': 'PAUSED'
        }
        ad = fb_api.create_ad(adset['id'], creative['id'], ad_data)
        
        return jsonify({
            'success': True,
            'campaign': campaign,
            'adset': adset,
            'creative': creative,
            'ad': ad,
            'message': 'Campaign created successfully! Review and activate when ready.'
        })
        
    except Exception as e:
        return jsonify({'error': str(e), 'traceback': traceback.format_exc()}), 500

# VAPI Integration Routes
@app.route('/api/vapi/call-lead', methods=['POST'])
@require_auth
def call_lead():
    """Initiate VAPI call to a lead"""
    try:
        global vapi_service
        if not vapi_service:
            vapi_service = get_vapi_service()

        data = request.get_json()

        # Validate required fields
        required_fields = ['phone', 'name', 'leadId']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Create assistant and initiate call
        assistant = vapi_service.create_pressure_washing_assistant()
        call = vapi_service.initiate_call(
            phone_number=data['phone'],
            assistant_id=assistant.id,
            lead_data=data
        )

        return jsonify({
            'success': True,
            'call_id': call.id,
            'status': call.status
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/vapi/call-status/<call_id>', methods=['GET'])
@require_auth
def get_call_status(call_id):
    """Get call status and details"""
    try:
        global vapi_service
        if not vapi_service:
            vapi_service = get_vapi_service()

        call = vapi_service.get_call(call_id)
        analysis = vapi_service.analyze_call_outcome(call_id) if call.transcript else None

        return jsonify({
            'call': {
                'id': call.id,
                'status': call.status,
                'started_at': call.started_at,
                'ended_at': call.ended_at,
                'transcript': call.transcript,
                'summary': call.summary,
                'cost': call.cost
            },
            'analysis': analysis
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/vapi/check-availability', methods=['POST'])
def check_availability():
    """Check appointment availability (called by VAPI assistant)"""
    try:
        data = request.get_json()
        service_type = data.get('serviceType', 'general')
        preferred_date = data.get('preferredDate')
        urgency = data.get('urgency', 'flexible')

        # Mock availability check - in production, integrate with calendar API
        from datetime import datetime, timedelta

        available_slots = []
        start_date = datetime.now() + timedelta(days=1)

        for i in range(7):  # Next 7 days
            date = start_date + timedelta(days=i)
            if date.weekday() < 5:  # Monday to Friday
                available_slots.extend([
                    {
                        'date': date.strftime('%Y-%m-%d'),
                        'time': '09:00',
                        'available': True
                    },
                    {
                        'date': date.strftime('%Y-%m-%d'),
                        'time': '13:00',
                        'available': True
                    },
                    {
                        'date': date.strftime('%Y-%m-%d'),
                        'time': '15:00',
                        'available': True
                    }
                ])

        return jsonify({
            'available_slots': available_slots[:6],  # Return 6 options
            'service_type': service_type,
            'urgency': urgency
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/vapi/book-appointment', methods=['POST'])
def vapi_book_appointment():
    """Book appointment (called by VAPI assistant)"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['customerName', 'customerPhone', 'appointmentDate', 'appointmentTime', 'serviceType']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Mock appointment booking - in production, integrate with calendar API
        appointment = {
            'id': f"apt_{int(__import__('time').time())}",
            'customer_name': data['customerName'],
            'customer_phone': data['customerPhone'],
            'appointment_date': data['appointmentDate'],
            'appointment_time': data['appointmentTime'],
            'service_type': data['serviceType'],
            'lead_id': data.get('leadId'),
            'status': 'confirmed',
            'created_at': __import__('datetime').datetime.now().isoformat()
        }

        print(f"📅 APPOINTMENT BOOKED: {appointment}")

        return jsonify({
            'success': True,
            'appointment': appointment,
            'confirmation_message': f"Perfect! Your free {data['serviceType']} estimate is confirmed for {data['appointmentDate']} at {data['appointmentTime']}. You'll receive a confirmation text shortly."
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Calendar Integration Routes
@app.route('/api/calendar/status', methods=['GET'])
@require_auth
def calendar_status():
    """Check calendar connection status"""
    try:
        # Mock calendar status - in production, check actual OAuth tokens
        return jsonify({
            'connected': True,
            'provider': 'google',
            'calendar_id': 'primary',
            'last_sync': '2024-01-15T10:00:00Z'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/calendar/connect/<provider>', methods=['GET'])
@require_auth
def connect_calendar(provider):
    """Initiate OAuth flow for calendar connection"""
    try:
        if provider not in ['google', 'outlook']:
            return jsonify({'error': 'Unsupported calendar provider'}), 400

        # Mock OAuth URL - in production, generate actual OAuth URL
        oauth_url = f"https://accounts.{provider}.com/oauth/authorize?client_id=your_client_id&redirect_uri=your_redirect_uri"

        return jsonify({
            'oauth_url': oauth_url,
            'provider': provider
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/calendar/availability', methods=['POST'])
@require_auth
def check_calendar_availability():
    """Check available time slots for appointments"""
    try:
        data = request.get_json()
        date = data.get('date')
        service_type = data.get('service_type', 'general')
        duration = data.get('duration', 60)

        if not date:
            return jsonify({'error': 'Date is required'}), 400

        # Mock availability - in production, check actual calendar
        from datetime import datetime, timedelta

        # Business hours: 9 AM to 5 PM
        business_start = 9
        business_end = 17

        available_slots = []

        # Generate hourly slots
        for hour in range(business_start, business_end):
            slot_time = f"{hour:02d}:00"

            # Mock some slots as unavailable (existing appointments)
            is_available = not (hour in [12, 14])  # Lunch and one appointment

            available_slots.append({
                'date': date,
                'time': slot_time,
                'duration': duration,
                'available': is_available,
                'service_type': service_type
            })

        return jsonify({
            'available_slots': available_slots,
            'date': date,
            'service_type': service_type
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/appointments/book', methods=['POST'])
@require_auth
def book_appointment():
    """Book a new appointment"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['customer_name', 'customer_phone', 'appointment_date', 'appointment_time', 'service_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Create appointment record
        appointment = {
            'id': f"apt_{int(__import__('time').time())}",
            'customer_name': data['customer_name'],
            'customer_phone': data['customer_phone'],
            'customer_email': data.get('customer_email'),
            'service_type': data['service_type'],
            'appointment_date': data['appointment_date'],
            'appointment_time': data['appointment_time'],
            'duration': data.get('duration', 60),
            'address': data.get('address'),
            'notes': data.get('notes'),
            'status': 'scheduled',
            'lead_id': data.get('lead_id'),
            'created_at': __import__('datetime').datetime.now().isoformat(),
            'calendar_event_id': f"cal_event_{int(__import__('time').time())}"
        }

        # In production, create calendar event and save to database
        print(f"📅 APPOINTMENT BOOKED: {appointment}")

        return jsonify({
            'success': True,
            'appointment': appointment,
            'message': f"Appointment scheduled for {data['appointment_date']} at {data['appointment_time']}"
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/appointments', methods=['GET'])
@require_auth
def get_appointments():
    """Get all appointments"""
    try:
        # Mock appointments - in production, fetch from database
        appointments = [
            {
                'id': 'apt_1',
                'customer_name': 'John Smith',
                'customer_phone': '(*************',
                'customer_email': '<EMAIL>',
                'service_type': 'house_washing',
                'appointment_date': '2024-01-15',
                'appointment_time': '10:00',
                'duration': 60,
                'address': '123 Main St, Anytown, ST 12345',
                'status': 'scheduled',
                'lead_id': 'lead_1',
                'created_at': '2024-01-10T09:00:00Z',
                'calendar_event_id': 'google_event_123'
            }
        ]

        return jsonify({
            'appointments': appointments,
            'total': len(appointments)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Admin Authentication Routes
@app.route('/api/auth/admin-login', methods=['POST'])
def admin_login():
    """Admin login with enhanced security"""
    try:
        data = request.get_json()
        email = data.get('email')
        password = data.get('password')
        mfa_code = data.get('mfaCode')

        # Mock admin authentication - in production, verify against database
        if email == '<EMAIL>' and password == 'admin123':
            admin_user = {
                'id': 'admin_1',
                'email': email,
                'name': 'System Administrator',
                'company_name': 'PressureMax',
                'role': 'admin',
                'permissions': [
                    'template_create',
                    'template_edit',
                    'template_delete',
                    'template_publish',
                    'user_manage',
                    'analytics_view',
                    'system_settings'
                ],
                'plan': 'scale',
                'subscription_status': 'active'
            }

            # Generate admin token
            admin_token = f"admin_token_{int(__import__('time').time())}"

            return jsonify({
                'success': True,
                'user': admin_user,
                'token': admin_token
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid admin credentials'
            }), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/validate-admin', methods=['GET'])
def validate_admin():
    """Validate admin session"""
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'No token provided'}), 401

        token = auth_header.split(' ')[1]

        # Mock token validation - in production, verify JWT
        if token.startswith('admin_token_'):
            admin_user = {
                'id': 'admin_1',
                'email': '<EMAIL>',
                'name': 'System Administrator',
                'company_name': 'PressureMax',
                'role': 'admin',
                'permissions': [
                    'template_create',
                    'template_edit',
                    'template_delete',
                    'template_publish',
                    'user_manage',
                    'analytics_view',
                    'system_settings'
                ],
                'plan': 'scale',
                'subscription_status': 'active'
            }
            return jsonify(admin_user)
        else:
            return jsonify({'error': 'Invalid token'}), 401

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Admin Template Management Routes
@app.route('/api/admin/templates', methods=['GET'])
@require_auth
def get_admin_templates():
    """Get all global templates for admin management"""
    try:
        # Mock global templates - in production, fetch from database
        global_templates = [
            {
                'id': 'template_1',
                'name': 'Spring House Washing Special',
                'category': 'Residential',
                'service': 'house_washing',
                'template_type': 'global',
                'is_public': True,
                'status': 'active',
                'is_featured': True,
                'created_by': 'admin_1',
                'created_at': '2024-01-01T00:00:00Z',
                'performance': {
                    'ctr': '2.5%',
                    'cpl': '$45',
                    'conversions': 125,
                    'total_spend': 5625,
                    'total_leads': 125
                }
            }
        ]

        return jsonify({
            'templates': global_templates,
            'total': len(global_templates)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/templates', methods=['POST'])
@require_auth
def create_admin_template():
    """Create new global template (admin only)"""
    try:
        data = request.get_json()

        # Validate admin permissions (mock)
        # In production, check user role and permissions

        new_template = {
            'id': f"template_{int(__import__('time').time())}",
            'name': data.get('name'),
            'category': data.get('category'),
            'service': data.get('service'),
            'template_type': 'global',
            'is_public': True,
            'status': 'draft',
            'is_featured': False,
            'created_by': 'admin_1',
            'created_at': __import__('datetime').datetime.now().isoformat(),
            'creative': data.get('creative', {}),
            'targeting': data.get('targeting', {}),
            'budget_range': data.get('budget_range', {})
        }

        print(f"📝 ADMIN TEMPLATE CREATED: {new_template}")

        return jsonify({
            'success': True,
            'template': new_template
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/templates/<template_id>', methods=['PUT'])
@require_auth
def update_admin_template(template_id):
    """Update global template (admin only)"""
    try:
        data = request.get_json()

        # Mock template update
        updated_template = {
            'id': template_id,
            **data,
            'updated_at': __import__('datetime').datetime.now().isoformat()
        }

        print(f"📝 ADMIN TEMPLATE UPDATED: {updated_template}")

        return jsonify({
            'success': True,
            'template': updated_template
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/templates/<template_id>', methods=['DELETE'])
@require_auth
def delete_admin_template(template_id):
    """Delete global template (admin only)"""
    try:
        print(f"🗑️ ADMIN TEMPLATE DELETED: {template_id}")

        return jsonify({
            'success': True,
            'message': 'Template deleted successfully'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/audit-log', methods=['POST'])
@require_auth
def log_admin_action():
    """Log admin action for audit trail"""
    try:
        data = request.get_json()

        log_entry = {
            'id': f"log_{int(__import__('time').time())}",
            'user_id': data.get('userId'),
            'action': data.get('action'),
            'details': data.get('details'),
            'timestamp': data.get('timestamp'),
            'ip_address': request.remote_addr,
            'user_agent': request.headers.get('User-Agent')
        }

        print(f"📋 ADMIN ACTION LOGGED: {log_entry}")

        return jsonify({
            'success': True,
            'log_id': log_entry['id']
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/stats', methods=['GET'])
@require_auth
def get_admin_stats():
    """Get admin dashboard statistics"""
    try:
        # Mock admin stats - in production, calculate from database
        stats = {
            'totalUsers': 1247,
            'totalTemplates': 45,
            'totalCampaigns': 3892,
            'totalLeads': 15634,
            'monthlyRevenue': 24750,
            'activeSubscriptions': 1189
        }

        return jsonify(stats)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Initialize Facebook API on startup
    init_facebook_api()
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 5000)),
        debug=True
    )
