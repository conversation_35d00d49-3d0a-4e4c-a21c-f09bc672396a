/**
 * Facebook Business Integration Component for PressureMax
 * Handles Facebook account connection and management
 */

import React, { useState, useEffect } from 'react';
import { 
  Facebook, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Settings, 
  Users, 
  CreditCard,
  Shield,
  Zap,
  ExternalLink
} from 'lucide-react';
import { facebookIntegration, FacebookIntegrationStatus } from '../services/facebookIntegration';

interface FacebookIntegrationProps {
  className?: string;
  onConnectionChange?: (isConnected: boolean) => void;
}

export const FacebookIntegration: React.FC<FacebookIntegrationProps> = ({
  className = '',
  onConnectionChange
}) => {
  const [status, setStatus] = useState<FacebookIntegrationStatus>({
    isConnected: false,
    pages: [],
    adAccounts: [],
    permissions: []
  });
  const [isConnecting, setIsConnecting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Load saved integration status
    const savedStatus = facebookIntegration.loadSavedIntegration();
    setStatus(savedStatus);
    
    // Test connection if connected
    if (savedStatus.isConnected) {
      testConnection();
    }
  }, []);

  const testConnection = async () => {
    try {
      const isValid = await facebookIntegration.testConnection();
      if (!isValid && status.isConnected) {
        // Connection is invalid, disconnect
        handleDisconnect();
      }
    } catch (error) {
      console.error('Connection test failed:', error);
    }
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      await facebookIntegration.connectFacebookAccount();
      const newStatus = facebookIntegration.getIntegrationStatus();
      setStatus(newStatus);
      onConnectionChange?.(newStatus.isConnected);
      
      if (newStatus.isConnected) {
        alert('✅ Facebook Business account connected successfully!');
      }
    } catch (error) {
      console.error('Facebook connection failed:', error);
      alert('❌ Failed to connect Facebook account. Please try again.');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = () => {
    if (window.confirm('Are you sure you want to disconnect your Facebook Business account?')) {
      facebookIntegration.disconnectFacebookAccount();
      setStatus(facebookIntegration.getIntegrationStatus());
      onConnectionChange?.(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await facebookIntegration.refreshAccessToken();
      const newStatus = facebookIntegration.getIntegrationStatus();
      setStatus(newStatus);
      alert('✅ Facebook connection refreshed successfully!');
    } catch (error) {
      console.error('Refresh failed:', error);
      alert('❌ Failed to refresh Facebook connection. Please reconnect.');
      handleDisconnect();
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusColor = () => {
    if (!status.isConnected) return 'text-gray-400';
    if (!facebookIntegration.hasRequiredPermissions()) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getStatusIcon = () => {
    if (!status.isConnected) return <XCircle className="text-red-400" size={20} />;
    if (!facebookIntegration.hasRequiredPermissions()) return <AlertTriangle className="text-yellow-400" size={20} />;
    return <CheckCircle className="text-green-400" size={20} />;
  };

  const getStatusText = () => {
    if (!status.isConnected) return 'Not Connected';
    if (!facebookIntegration.hasRequiredPermissions()) return 'Limited Permissions';
    return 'Connected';
  };

  return (
    <div className={`bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-blue-600 p-3 rounded-xl">
            <Facebook className="text-white" size={24} />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">Facebook Business</h3>
            <p className="text-sm text-gray-400">Connect your Facebook Business account to launch ad campaigns</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
      </div>

      {/* Connection Status */}
      {status.isConnected ? (
        <div className="space-y-4">
          {/* User Info */}
          <div className="bg-gray-800/50 rounded-xl p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-semibold">Account Information</h4>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="text-cyan-400 hover:text-cyan-300 text-sm"
              >
                {showDetails ? 'Hide Details' : 'Show Details'}
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3">
                <Users className="text-blue-400" size={16} />
                <div>
                  <p className="text-xs text-gray-400">User</p>
                  <p className="text-white text-sm">{status.user?.name || 'Unknown'}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Facebook className="text-blue-400" size={16} />
                <div>
                  <p className="text-xs text-gray-400">Pages</p>
                  <p className="text-white text-sm">{status.pages.length} connected</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <CreditCard className="text-green-400" size={16} />
                <div>
                  <p className="text-xs text-gray-400">Ad Accounts</p>
                  <p className="text-white text-sm">{status.adAccounts.length} available</p>
                </div>
              </div>
            </div>

            {showDetails && (
              <div className="mt-4 pt-4 border-t border-gray-700">
                {/* Permissions */}
                <div className="mb-4">
                  <h5 className="text-white font-medium mb-2 flex items-center">
                    <Shield className="mr-2" size={16} />
                    Permissions
                  </h5>
                  <div className="flex flex-wrap gap-2">
                    {status.permissions.map((permission) => (
                      <span
                        key={permission}
                        className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                  
                  {!facebookIntegration.hasRequiredPermissions() && (
                    <div className="mt-2 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded text-yellow-400 text-xs">
                      <AlertTriangle className="inline mr-1" size={12} />
                      Missing permissions: {facebookIntegration.getMissingPermissions().join(', ')}
                    </div>
                  )}
                </div>

                {/* Ad Accounts */}
                {status.adAccounts.length > 0 && (
                  <div className="mb-4">
                    <h5 className="text-white font-medium mb-2">Ad Accounts</h5>
                    <div className="space-y-2">
                      {status.adAccounts.slice(0, 3).map((account) => (
                        <div key={account.id} className="flex items-center justify-between text-sm">
                          <span className="text-gray-300">{account.name}</span>
                          <span className="text-gray-400">{account.currency}</span>
                        </div>
                      ))}
                      {status.adAccounts.length > 3 && (
                        <p className="text-xs text-gray-400">
                          +{status.adAccounts.length - 3} more accounts
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Token Expiry */}
                {status.expiresAt && (
                  <div className="text-xs text-gray-400">
                    Access token expires: {status.expiresAt.toLocaleDateString()}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isRefreshing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
              ) : (
                <RefreshCw size={16} />
              )}
              <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
            
            <button
              onClick={handleDisconnect}
              className="px-4 py-2 border border-red-500 text-red-400 hover:bg-red-500/10 rounded-lg font-medium transition-colors"
            >
              Disconnect
            </button>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-800/30 rounded-xl p-4">
            <h4 className="text-white font-semibold mb-3 flex items-center">
              <Zap className="mr-2 text-cyan-400" size={16} />
              Quick Actions
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <a
                href="https://business.facebook.com/settings/ad-accounts"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between p-3 bg-gray-700/50 hover:bg-gray-700 rounded-lg transition-colors group"
              >
                <span className="text-gray-300 text-sm">Manage Ad Accounts</span>
                <ExternalLink className="text-gray-400 group-hover:text-white" size={14} />
              </a>
              <a
                href="https://business.facebook.com/settings/pages"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between p-3 bg-gray-700/50 hover:bg-gray-700 rounded-lg transition-colors group"
              >
                <span className="text-gray-300 text-sm">Manage Pages</span>
                <ExternalLink className="text-gray-400 group-hover:text-white" size={14} />
              </a>
            </div>
          </div>
        </div>
      ) : (
        /* Not Connected State */
        <div className="text-center py-8">
          <div className="bg-blue-600/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <Facebook className="text-blue-400" size={32} />
          </div>
          <h4 className="text-white font-semibold mb-2">Connect Your Facebook Business Account</h4>
          <p className="text-gray-400 text-sm mb-6 max-w-md mx-auto">
            Connect your Facebook Business account to launch ad campaigns directly from PressureMax. 
            You'll need admin access to your Facebook Business Manager.
          </p>
          
          <button
            onClick={handleConnect}
            disabled={isConnecting}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 mx-auto"
          >
            {isConnecting ? (
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
            ) : (
              <Facebook size={20} />
            )}
            <span>{isConnecting ? 'Connecting...' : 'Connect Facebook'}</span>
          </button>
          
          <div className="mt-6 text-xs text-gray-500">
            <p>Required permissions: Ads Management, Business Management, Pages Management</p>
          </div>
        </div>
      )}
    </div>
  );
};
