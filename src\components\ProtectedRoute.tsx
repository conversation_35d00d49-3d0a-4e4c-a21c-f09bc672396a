/**
 * Protected Route Component for PressureMax
 * Handles route protection and authentication guards
 */

import React, { ReactNode } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Loader } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  requireAuth?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback,
  requireAuth = true
}) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader className="animate-spin text-cyan-500 mx-auto" size={48} />
          <p className="text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return fallback || (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <h2 className="text-2xl font-bold text-white">Authentication Required</h2>
          <p className="text-gray-300">
            Please sign in to access this page.
          </p>
        </div>
      </div>
    );
  }

  // If authentication is not required but user is authenticated, still show content
  // If authentication is required and user is authenticated, show content
  return <>{children}</>;
};

export default ProtectedRoute;
