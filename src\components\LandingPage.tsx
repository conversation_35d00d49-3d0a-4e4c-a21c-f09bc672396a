import React, { useState, useEffect } from 'react';
import {
  Zap,
  MessageSquare,
  Target,
  Sparkles,
  Star,
  ArrowRight,
  Phone,
  Mail,
  MapPin,
  Shield,
  BarChart3,
  Bot,
  Calendar
} from 'lucide-react';

interface LandingPageProps {
  onEnterDashboard: () => void;
}

export const LandingPage: React.FC<LandingPageProps> = ({ onEnterDashboard }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 500);
    return () => clearTimeout(timer);
  }, []);

  const features = [
    {
      icon: Target,
      title: "Ad Templates",
      subtitle: "20+ Proven Templates",
      description: "Ready-to-deploy Facebook ad templates specifically designed for pressure washing businesses. Each template is optimized for maximum lead generation and tested across thousands of campaigns.",
      metrics: [
        "3.2% average CTR",
        "$12 average cost per lead",
        "28% conversion rate"
      ]
    },
    {
      icon: Bot,
      title: "Voice Assistant",
      subtitle: "Instant Lead Response",
      description: "Our AI assistant Sarah calls every lead within 5 minutes, qualifies their needs, handles objections, and books appointments automatically. Never miss a lead again.",
      metrics: [
        "5-minute response time",
        "85% contact rate",
        "40% appointment booking rate"
      ]
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      subtitle: "Data-Driven Growth",
      description: "Track campaign performance, lead quality, and ROI with detailed analytics. A/B test your ads, optimize for better results, and scale what works.",
      metrics: [
        "Real-time reporting",
        "Lead scoring system",
        "ROI tracking"
      ]
    },
    {
      icon: MessageSquare,
      title: "Automated Follow-up",
      subtitle: "Multi-Touch Sequences",
      description: "Automated email, SMS, and voice follow-up sequences ensure no lead falls through the cracks. Nurture prospects until they're ready to buy.",
      metrics: [
        "5-touch sequences",
        "Personalized messaging",
        "Smart scheduling"
      ]
    },
    {
      icon: Calendar,
      title: "Calendar Integration",
      subtitle: "Seamless Booking",
      description: "Integrated calendar system allows leads to book appointments directly. Sync with your existing calendar and never double-book again.",
      metrics: [
        "Real-time availability",
        "Automatic confirmations",
        "Reminder notifications"
      ]
    },
    {
      icon: Shield,
      title: "CRM Integrations",
      subtitle: "Connect Everything",
      description: "Seamlessly integrate with popular CRM and FSM systems like Jobber, HousecallPro, ServiceTitan, and more. Keep all your data in sync.",
      metrics: [
        "10+ integrations",
        "Real-time sync",
        "Custom field mapping"
      ]
    }
  ];

  const testimonials = [
    {
      name: "Mike Rodriguez",
      business: "Rodriguez Pressure Wash",
      rating: 5,
      text: "This platform tripled my revenue in just 30 days. It's like having a dedicated marketing team working around the clock.",
      results: "+300% Revenue",
      status: "Verified Customer"
    },
    {
      name: "Sarah Chen",
      business: "Crystal Clean Systems",
      rating: 5,
      text: "I'm saving 3 hours of marketing work every day. The AI handles everything flawlessly, letting me focus on my customers.",
      results: "3+ Hours Saved Per Day",
      status: "Verified Customer"
    },
    {
      name: "David Thompson",
      business: "Thompson Power Washing",
      rating: 5,
      text: "The return on investment is incredible. I'm getting more qualified leads than I ever thought possible. This is the future of marketing for our industry.",
      results: "+500% ROI",
      status: "Verified Customer"
    }
  ];

  const pricingPlans = [
    {
      name: "Starter",
      price: "$198",
      period: "/month",
      description: "Perfect for getting started and automating your lead flow.",
      features: [
        "AI-Powered Lead Engagement",
        "Basic Ad Templates",
        "Email Support",
        "100 leads/month"
      ],
      cta: "Start Free Trial",
      popular: false,
    },
    {
      name: "Growth",
      price: "$398",
      period: "/month",
      description: "For businesses ready to scale their marketing efforts.",
      features: [
        "Everything in Starter, plus:",
        "Targeted Ad Campaigns",
        "Automated Content Creation",
        "Priority Support",
        "500 leads/month"
      ],
      cta: "Get Started",
      popular: true,
    },
    {
      name: "Scale",
      price: "$598",
      period: "/month",
      description: "The ultimate solution for market leaders.",
      features: [
        "Everything in Growth, plus:",
        "Advanced Analytics",
        "Dedicated Account Manager",
        "Unlimited leads",
        "Custom Integrations"
      ],
      cta: "Contact Us",
      popular: false,
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white overflow-x-hidden relative">
      {/* Background Pattern */}
      <div className="fixed inset-0 opacity-5 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-transparent to-cyan-500/5" />
      </div>

      {/* Main Content */}
      <div className={`transition-opacity duration-1000 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>

        {/* Header */}
        <header className="fixed w-full z-50 bg-black/80 backdrop-blur-sm border-b border-cyan-500/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center relative">
                  <Zap className="text-black" size={24} />
                </div>
                <span className="text-2xl font-bold tracking-wide text-cyan-400">
                  PressureMax
                </span>
              </div>

              <nav className="hidden md:flex items-center space-x-8">
                <a href="#features" className="text-gray-300 hover:text-cyan-400 transition-colors">Features</a>
                <a href="#testimonials" className="text-gray-300 hover:text-cyan-400 transition-colors">Testimonials</a>
                <a href="#pricing" className="text-gray-300 hover:text-cyan-400 transition-colors">Pricing</a>
                <button 
                  onClick={onEnterDashboard}
                  className="border border-cyan-500 text-cyan-400 px-6 py-2 hover:bg-cyan-500 hover:text-black transition-all duration-300 font-bold"
                >
                  Enter Dashboard
                </button>
              </nav>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center relative pt-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-8">
                <div className="space-y-6">
                  <div className="inline-flex items-center space-x-2 bg-cyan-500/10 border border-cyan-500/30 text-cyan-400 px-4 py-2 text-sm">
                    <Sparkles size={16} />
                    <span>AI Marketing for Service Businesses</span>
                  </div>

                  <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                    Automate Your Marketing,
                    <br />
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-cyan-600">
                      Grow Your Pressure
                    </span>
                    <br />
                    <span className="text-cyan-400">Washing Business</span>
                  </h1>

                  <p className="text-xl text-gray-300 leading-relaxed">
                    Stop wasting time on marketing. Our platform automates lead generation, follow-up, and ad campaigns, so you can focus on your customers and grow your revenue.
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <button 
                    onClick={onEnterDashboard}
                    className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-8 py-4 text-lg font-bold hover:shadow-xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-105 flex items-center justify-center space-x-2"
                  >
                    <span>Get Started Free</span>
                    <ArrowRight size={20} />
                  </button>
                  <button className="border-2 border-cyan-500 text-cyan-400 px-8 py-4 text-lg font-bold hover:bg-cyan-500 hover:text-black transition-all duration-300">
                    Watch a Demo
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-4 text-center">
                  <div className="border border-cyan-500/30 p-4 bg-cyan-500/5">
                    <div className="text-2xl font-bold text-cyan-400">+50%</div>
                    <div className="text-gray-400 text-sm">Revenue Growth</div>
                  </div>
                  <div className="border border-cyan-500/30 p-4 bg-cyan-500/5">
                    <div className="text-2xl font-bold text-cyan-400">10+</div>
                    <div className="text-gray-400 text-sm">Hours Saved Weekly</div>
                  </div>
                  <div className="border border-cyan-500/30 p-4 bg-cyan-500/5">
                    <div className="text-2xl font-bold text-cyan-400">2x</div>
                    <div className="text-gray-400 text-sm">More Qualified Leads</div>
                  </div>
                </div>
              </div>

              <div className="relative">
                <div className="relative z-10 bg-black border-2 border-cyan-500/50 p-8 backdrop-blur-sm">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-cyan-400 text-sm">Live Dashboard</span>
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                  </div>

                  <img
                    src="/images/campaigns/Driveway_Transformation_Before_After.png"
                    alt="Pressure washing operation"
                    className="w-full h-48 object-cover mb-6 transition-all duration-500 hover:scale-105"
                  />

                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">AI Engagement:</span>
                      <span className="text-green-400">Active</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">New Leads Today:</span>
                      <span className="text-cyan-400">12</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Conversion Rate:</span>
                      <span className="text-green-400">28.4%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">System Status:</span>
                      <span className="text-green-400">Operational</span>
                    </div>
                  </div>
                </div>

                <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-transparent border-2 border-cyan-500/30 transform rotate-2 -z-10"></div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold tracking-wide mb-4">
                Everything You Need to <span className="text-cyan-400">Grow</span>
              </h2>
              <p className="text-xl text-gray-300">Our platform is packed with features to automate your marketing and save you time.</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} className="bg-black border-2 border-cyan-500/30 p-8 hover:border-cyan-500 transition-all duration-500 hover:shadow-lg hover:shadow-cyan-500/25 group">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center mb-6 relative">
                        <Icon className="text-black" size={32} />
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h3 className="text-2xl font-bold tracking-wide text-white mb-2">
                            {feature.title}
                          </h3>
                          <div className="text-cyan-400 font-bold text-sm mb-4">
                            {feature.subtitle}
                          </div>
                        </div>

                        <p className="text-gray-300 leading-relaxed">
                          {feature.description}
                        </p>

                        <div className="space-y-2 pt-4 border-t border-cyan-500/20">
                          {feature.metrics.map((metric, idx) => (
                            <div key={idx} className="flex items-center space-x-3">
                              <div className="w-2 h-2 bg-cyan-400 rounded-full" />
                              <span className="text-gray-400 text-sm">{metric}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section id="testimonials" className="py-20 bg-gradient-to-br from-gray-900 to-black relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold tracking-wide mb-4">
                What Our <span className="text-cyan-400">Customers</span> Say
              </h2>
              <p className="text-xl text-gray-300">Join a community of successful business owners.</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8 mb-16">
              {testimonials.map((testimonial, index) => (
                <div key={index} className="bg-black border border-cyan-500/30 p-8 hover:border-cyan-500 transition-all duration-300 relative group">
                  <div className="absolute top-4 right-4">
                    <div className="text-xs text-cyan-400 bg-cyan-500/10 px-2 py-1 border border-cyan-500/30">
                      {testimonial.status}
                    </div>
                  </div>

                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="text-cyan-400 fill-current" size={16} />
                    ))}
                  </div>

                  <p className="text-gray-300 mb-6 leading-relaxed text-sm">
                    "{testimonial.text}"
                  </p>

                  <div className="border-t border-cyan-500/20 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-bold text-white">{testimonial.name}</div>
                        <div className="text-gray-400 text-sm">{testimonial.business}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-cyan-400 font-bold">{testimonial.results}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Network Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">2,500+</div>
                <div className="text-gray-400 text-sm">Businesses Served</div>
              </div>
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">$50M+</div>
                <div className="text-gray-400 text-sm">Revenue Generated</div>
              </div>
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">98%</div>
                <div className="text-gray-400 text-sm">Customer Satisfaction</div>
              </div>
              <div className="border border-cyan-500/30 p-6 bg-cyan-500/5">
                <div className="text-3xl font-bold text-cyan-400">24/7</div>
                <div className="text-gray-400 text-sm">Support</div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-5xl font-bold tracking-wide mb-4">
                Simple, Transparent <span className="text-cyan-400">Pricing</span>
              </h2>
              <p className="text-xl text-gray-300">Choose the plan that's right for your business.</p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {pricingPlans.map((plan) => (
                <div key={plan.name} className={`relative bg-black border-2 p-8 transition-all duration-300 hover:shadow-lg ${
                  plan.popular
                    ? 'border-cyan-500 bg-cyan-500/5 scale-105 shadow-cyan-500/25'
                    : 'border-cyan-500/30 hover:border-cyan-500'
                  }`}>
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-black px-6 py-2 font-bold text-sm">
                        MOST POPULAR
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold tracking-wide text-white mb-4">{plan.name}</h3>
                    <p className="text-gray-400 mb-6 text-sm">{plan.description}</p>
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-bold text-white">{plan.price}</span>
                      <span className="text-gray-400 ml-2">{plan.period}</span>
                    </div>
                  </div>

                  <ul className="space-y-4 mb-8">
                    {plan.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-cyan-400 rounded-full" />
                        <span className="text-gray-300 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={onEnterDashboard}
                    className={`w-full block text-center py-4 font-bold tracking-wide transition-all duration-300 ${
                      plan.popular
                        ? 'bg-gradient-to-r from-cyan-500 to-cyan-600 text-black hover:shadow-lg hover:shadow-cyan-500/25 transform hover:scale-105'
                        : 'border-2 border-cyan-500 text-cyan-400 hover:bg-cyan-500 hover:text-black'
                    }`}
                  >
                    {plan.cta}
                  </button>
                </div>
              ))}
            </div>

            <div className="text-center mt-12">
              <p className="text-gray-400 mb-4">All plans include a 14-day free trial. No credit card required.</p>
              <div className="flex items-center justify-center space-x-8 text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <Shield className="text-cyan-400" size={16} />
                  <span>Secure & Reliable</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="text-cyan-400" size={16} />
                  <span>30-Day Money-Back Guarantee</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="text-cyan-400" size={16} />
                  <span>Free Onboarding Support</span>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-black border-t border-cyan-500/30 py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-4 gap-8 mb-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center">
                    <Zap className="text-black" size={24} />
                  </div>
                  <span className="text-2xl font-bold tracking-wide text-cyan-400">
                    PressureMax
                  </span>
                </div>
                <p className="text-gray-400 text-sm">
                  Automate your pressure washing business marketing with AI-powered lead generation and follow-up systems.
                </p>
                <div className="flex space-x-4">
                  <div className="w-8 h-8 bg-cyan-500/10 border border-cyan-500/30 flex items-center justify-center hover:bg-cyan-500 hover:text-black transition-colors cursor-pointer">
                    <span className="text-xs">f</span>
                  </div>
                  <div className="w-8 h-8 bg-cyan-500/10 border border-cyan-500/30 flex items-center justify-center hover:bg-cyan-500 hover:text-black transition-colors cursor-pointer">
                    <span className="text-xs">t</span>
                  </div>
                  <div className="w-8 h-8 bg-cyan-500/10 border border-cyan-500/30 flex items-center justify-center hover:bg-cyan-500 hover:text-black transition-colors cursor-pointer">
                    <span className="text-xs">in</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-white font-bold mb-4">Product</h4>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Features</a></li>
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Pricing</a></li>
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Integrations</a></li>
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">API</a></li>
                </ul>
              </div>

              <div>
                <h4 className="text-white font-bold mb-4">Support</h4>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Help Center</a></li>
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Documentation</a></li>
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Contact Us</a></li>
                  <li><a href="#" className="hover:text-cyan-400 transition-colors">Status</a></li>
                </ul>
              </div>

              <div>
                <h4 className="text-white font-bold mb-4">Contact</h4>
                <div className="space-y-3 text-sm text-gray-400">
                  <div className="flex items-center space-x-3">
                    <Phone size={16} className="text-cyan-400" />
                    <span>1-800-PRESSURE</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail size={16} className="text-cyan-400" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin size={16} className="text-cyan-400" />
                    <span>Austin, TX</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-cyan-500/20 pt-8 flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm">
                © 2024 PressureMax. All rights reserved.
              </div>
              <div className="flex space-x-6 text-sm text-gray-400 mt-4 md:mt-0">
                <a href="#" className="hover:text-cyan-400 transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-cyan-400 transition-colors">Terms of Service</a>
                <a href="#" className="hover:text-cyan-400 transition-colors">Cookie Policy</a>
              </div>
            </div>
          </div>
        </footer>

      </div>
    </div>
  );
};
