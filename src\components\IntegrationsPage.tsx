/**
 * Integrations Page for PressureMax
 * Central hub for all integrations including Facebook, CRM, and FSM systems
 */

import React, { useState, useEffect } from 'react';
import { 
  Zap, 
  Facebook, 
  Settings, 
  CheckCircle, 
  AlertTriangle,
  Plus,
  ArrowRight,
  ExternalLink,
  Shield,
  Smartphone,
  Mail,
  Calendar,
  Users
} from 'lucide-react';
import { FacebookIntegration } from './FacebookIntegration';
import { IntegrationsHub } from './IntegrationsHub';
import { facebookIntegration } from '../services/facebookIntegration';

interface IntegrationsPageProps {
  className?: string;
}

export const IntegrationsPage: React.FC<IntegrationsPageProps> = ({
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'facebook' | 'crm' | 'marketing'>('overview');
  const [facebookConnected, setFacebookConnected] = useState(false);

  useEffect(() => {
    // Check Facebook connection status
    const status = facebookIntegration.loadSavedIntegration();
    setFacebookConnected(status.isConnected);
  }, []);

  const integrationCategories = [
    {
      id: 'facebook',
      name: 'Facebook Business',
      description: 'Launch ad campaigns directly from PressureMax',
      icon: Facebook,
      color: 'bg-blue-600',
      status: facebookConnected ? 'connected' : 'not_connected',
      required: true
    },
    {
      id: 'crm',
      name: 'CRM & FSM Systems',
      description: 'Sync leads with your business management tools',
      icon: Users,
      color: 'bg-purple-600',
      status: 'available',
      required: false
    },
    {
      id: 'marketing',
      name: 'Marketing Tools',
      description: 'Email marketing, SMS, and automation platforms',
      icon: Mail,
      color: 'bg-green-600',
      status: 'coming_soon',
      required: false
    },
    {
      id: 'calendar',
      name: 'Calendar & Scheduling',
      description: 'Appointment booking and calendar integrations',
      icon: Calendar,
      color: 'bg-orange-600',
      status: 'coming_soon',
      required: false
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="text-green-400" size={16} />;
      case 'not_connected':
        return <AlertTriangle className="text-yellow-400" size={16} />;
      case 'available':
        return <Settings className="text-blue-400" size={16} />;
      default:
        return <Settings className="text-gray-400" size={16} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'not_connected':
        return 'Not Connected';
      case 'available':
        return 'Available';
      case 'coming_soon':
        return 'Coming Soon';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'text-green-400 bg-green-500/20';
      case 'not_connected':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'available':
        return 'text-blue-400 bg-blue-500/20';
      default:
        return 'text-gray-400 bg-gray-500/20';
    }
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent mb-4">
          Integrations Hub
        </h1>
        <p className="text-xl text-gray-400 max-w-2xl mx-auto">
          Connect PressureMax with your favorite tools and platforms to streamline your workflow
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Integrations</p>
              <p className="text-2xl font-bold text-white">{integrationCategories.length}</p>
            </div>
            <Zap className="text-cyan-400" size={24} />
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Connected</p>
              <p className="text-2xl font-bold text-green-400">
                {integrationCategories.filter(cat => cat.status === 'connected').length}
              </p>
            </div>
            <CheckCircle className="text-green-400" size={24} />
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Available</p>
              <p className="text-2xl font-bold text-blue-400">
                {integrationCategories.filter(cat => cat.status === 'available').length}
              </p>
            </div>
            <Settings className="text-blue-400" size={24} />
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Coming Soon</p>
              <p className="text-2xl font-bold text-gray-400">
                {integrationCategories.filter(cat => cat.status === 'coming_soon').length}
              </p>
            </div>
            <Plus className="text-gray-400" size={24} />
          </div>
        </div>
      </div>

      {/* Integration Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {integrationCategories.map((category) => {
          const IconComponent = category.icon;
          return (
            <div
              key={category.id}
              className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 hover:border-cyan-500/50 transition-all duration-300 group cursor-pointer"
              onClick={() => category.status !== 'coming_soon' && setActiveTab(category.id as any)}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`${category.color} p-3 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="text-white" size={24} />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">{category.name}</h3>
                    <p className="text-sm text-gray-400">{category.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {getStatusIcon(category.status)}
                  <span className={`px-2 py-1 text-xs font-medium rounded ${getStatusColor(category.status)}`}>
                    {getStatusText(category.status)}
                  </span>
                </div>
              </div>

              {category.required && category.status !== 'connected' && (
                <div className="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                  <div className="flex items-center space-x-2 text-yellow-400 text-sm">
                    <Shield size={14} />
                    <span>Required for campaign launching</span>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-400">
                  {category.status === 'connected' && 'Ready to use'}
                  {category.status === 'not_connected' && 'Click to connect'}
                  {category.status === 'available' && 'Multiple options available'}
                  {category.status === 'coming_soon' && 'Available soon'}
                </div>
                
                {category.status !== 'coming_soon' && (
                  <ArrowRight className="text-cyan-400 group-hover:translate-x-1 transition-transform duration-300" size={16} />
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Setup */}
      {!facebookConnected && (
        <div className="bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-500/30 rounded-2xl p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-white mb-2">🚀 Quick Setup</h3>
              <p className="text-gray-400">
                Connect your Facebook Business account to start launching campaigns immediately
              </p>
            </div>
            <button
              onClick={() => setActiveTab('facebook')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors flex items-center space-x-2"
            >
              <Facebook size={20} />
              <span>Connect Facebook</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Zap },
    { id: 'facebook', label: 'Facebook Business', icon: Facebook },
    { id: 'crm', label: 'CRM & FSM', icon: Users },
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-800/50 p-1 rounded-xl">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-cyan-500 text-black'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
              >
                <IconComponent size={18} />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="space-y-8">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'facebook' && (
            <FacebookIntegration 
              onConnectionChange={(connected) => setFacebookConnected(connected)}
            />
          )}
          {activeTab === 'crm' && <IntegrationsHub />}
        </div>
      </div>
    </div>
  );
};
