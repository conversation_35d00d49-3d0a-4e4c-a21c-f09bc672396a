import React, { useState, useEffect } from 'react';
import { X, Play, AlertCircle, CheckCircle, Loader } from 'lucide-react';
import { AdTemplate } from '../types/database';
import { facebookAdsService, FacebookAdAccount } from '../services/facebookAds';
import { facebookIntegration } from '../services/facebookIntegration';
import { facebookCampaignService } from '../services/facebookCampaignService';
import { db } from '../services/database';

interface CampaignDeployModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: AdTemplate | null;
  onSuccess: (campaignId: string) => void;
}

export const CampaignDeployModal: React.FC<CampaignDeployModalProps> = ({
  isOpen,
  onClose,
  template,
  onSuccess
}) => {
  const [adAccounts, setAdAccounts] = useState<FacebookAdAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [pageId, setPageId] = useState('');
  const [budget, setBudget] = useState(0);
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [isDeploying, setIsDeploying] = useState(false);
  const [deploymentStatus, setDeploymentStatus] = useState<'idle' | 'deploying' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (isOpen && template) {
      setBudget(template.budget_range.suggested);
      loadAdAccounts();
    }
  }, [isOpen, template]);

  const loadAdAccounts = async () => {
    try {
      // Check if Facebook is connected
      const facebookStatus = facebookIntegration.getIntegrationStatus();
      if (!facebookStatus.isConnected) {
        setErrorMessage('Facebook Business account not connected. Please connect your account in the Integrations page.');
        return;
      }

      // Use ad accounts from the integration
      const accounts = facebookStatus.adAccounts.map(account => ({
        id: account.account_id,
        account_id: account.account_id,
        name: account.name,
        account_status: account.account_status,
        currency: account.currency,
        timezone_name: account.timezone_name
      }));

      setAdAccounts(accounts);
      if (accounts.length > 0) {
        setSelectedAccount(accounts[0].id);
      }

      // Auto-select first page if available
      if (facebookStatus.pages.length > 0) {
        setPageId(facebookStatus.pages[0].id);
      }
    } catch (error) {
      console.error('Error loading ad accounts:', error);
      setErrorMessage('Failed to load Facebook ad accounts. Please check your connection.');
    }
  };

  const handleDeploy = async () => {
    if (!template || !selectedAccount || !pageId || !websiteUrl) {
      setErrorMessage('Please fill in all required fields.');
      return;
    }

    setIsDeploying(true);
    setDeploymentStatus('deploying');
    setErrorMessage('');

    try {
      // Deploy to Facebook using direct Graph API
      const result = await facebookCampaignService.createCampaignFromTemplate(
        template,
        selectedAccount,
        pageId,
        {
          budget,
          websiteUrl,
          targeting: {
            geo_locations: {
              countries: ['US']
            },
            age_min: 25,
            age_max: 65
          }
        }
      );

      if (result.success && result.campaign_id) {
        // Save campaign to database
        const campaign = await db.createCampaign({
          template_id: template.id,
          name: `${template.name} - ${new Date().toLocaleDateString()}`,
          facebook_campaign_id: result.campaign_id,
        budget,
        start_date: new Date(),
        custom_creative: {
          primary_text: template.creative.primary_text,
          headline: template.creative.headline,
          description: template.creative.description,
          call_to_action: template.creative.call_to_action,
          media_requirements: template.creative.media_requirements,
        },
        metrics: {
          impressions: 0,
          clicks: 0,
          ctr: 0,
          cpc: 0,
          cpl: 0,
          leads_generated: 0,
          spend: 0,
          last_sync: new Date(),
        },
        status: 'active',
      });

      setDeploymentStatus('success');
      setTimeout(() => {
        onSuccess(campaign.id);
        onClose();
      }, 2000);
    } else {
      throw new Error(result.error || 'Failed to create campaign');
    }

    } catch (error) {
      console.error('Error deploying campaign:', error);
      setDeploymentStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Failed to deploy campaign');
    } finally {
      setIsDeploying(false);
    }
  };

  const resetModal = () => {
    setDeploymentStatus('idle');
    setErrorMessage('');
    setIsDeploying(false);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen || !template) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-black border border-cyan-500/30 max-w-2xl w-full">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Deploy Campaign</h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-white"
              disabled={isDeploying}
            >
              <X size={24} />
            </button>
          </div>

          {/* Template Info */}
          <div className="bg-gray-900 border border-gray-700 p-4 mb-6">
            <h3 className="text-lg font-semibold text-white mb-2">{template.name}</h3>
            <p className="text-gray-300 text-sm mb-2">{template.creative.headline}</p>
            <div className="flex items-center space-x-4 text-xs text-gray-400">
              <span>{template.category} • {template.service}</span>
              <span>Suggested Budget: ${template.budget_range.suggested}/day</span>
            </div>
          </div>

          {deploymentStatus === 'idle' && (
            <div className="space-y-4">
              {/* Facebook Ad Account */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Facebook Ad Account *
                </label>
                <select
                  value={selectedAccount}
                  onChange={(e) => setSelectedAccount(e.target.value)}
                  className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  disabled={isDeploying}
                >
                  <option value="">Select Ad Account</option>
                  {adAccounts.map(account => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.id})
                    </option>
                  ))}
                </select>
              </div>

              {/* Facebook Page */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Facebook Page *
                </label>
                <select
                  value={pageId}
                  onChange={(e) => setPageId(e.target.value)}
                  className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  disabled={isDeploying}
                >
                  <option value="">Select Facebook Page</option>
                  {facebookIntegration.getIntegrationStatus().pages.map(page => (
                    <option key={page.id} value={page.id}>
                      {page.name} ({page.category})
                    </option>
                  ))}
                </select>
                {facebookIntegration.getIntegrationStatus().pages.length === 0 && (
                  <p className="text-xs text-yellow-400 mt-1">
                    No Facebook pages found. Please ensure your Facebook Business account has pages set up.
                  </p>
                )}
              </div>

              {/* Website URL */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Website URL *
                </label>
                <input
                  type="url"
                  value={websiteUrl}
                  onChange={(e) => setWebsiteUrl(e.target.value)}
                  className="w-full bg-gray-900 border border-gray-700 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
                  placeholder="https://yourwebsite.com/contact"
                  disabled={isDeploying}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Where users will be directed when they click your ad
                </p>
              </div>

              {/* Daily Budget */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Daily Budget *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-400">$</span>
                  <input
                    type="number"
                    value={budget}
                    onChange={(e) => setBudget(parseInt(e.target.value) || 0)}
                    className="w-full bg-gray-900 border border-gray-700 text-white pl-8 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                    min={template.budget_range.min}
                    max={template.budget_range.max}
                    disabled={isDeploying}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Recommended: ${template.budget_range.min} - ${template.budget_range.max} per day
                </p>
              </div>

              {/* Error Message */}
              {errorMessage && (
                <div className="flex items-center space-x-2 text-red-400 bg-red-500/10 border border-red-500/30 p-3">
                  <AlertCircle size={16} />
                  <span className="text-sm">{errorMessage}</span>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
                <button
                  onClick={handleClose}
                  className="px-6 py-2 border border-gray-600 text-gray-300 hover:text-white hover:border-gray-500 transition-colors"
                  disabled={isDeploying}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeploy}
                  className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 flex items-center space-x-2"
                  disabled={isDeploying || !selectedAccount || !pageId || !websiteUrl}
                >
                  <Play size={16} />
                  <span>Deploy Campaign</span>
                </button>
              </div>
            </div>
          )}

          {/* Deploying State */}
          {deploymentStatus === 'deploying' && (
            <div className="text-center py-8">
              <Loader className="animate-spin text-cyan-400 mx-auto mb-4" size={48} />
              <h3 className="text-lg font-semibold text-white mb-2">Deploying Campaign...</h3>
              <p className="text-gray-400">Creating your Facebook ad campaign. This may take a moment.</p>
            </div>
          )}

          {/* Success State */}
          {deploymentStatus === 'success' && (
            <div className="text-center py-8">
              <CheckCircle className="text-green-400 mx-auto mb-4" size={48} />
              <h3 className="text-lg font-semibold text-white mb-2">Campaign Deployed Successfully!</h3>
              <p className="text-gray-400">Your campaign has been created and is ready to go live.</p>
            </div>
          )}

          {/* Error State */}
          {deploymentStatus === 'error' && (
            <div className="text-center py-8">
              <AlertCircle className="text-red-400 mx-auto mb-4" size={48} />
              <h3 className="text-lg font-semibold text-white mb-2">Deployment Failed</h3>
              <p className="text-gray-400 mb-4">{errorMessage}</p>
              <button
                onClick={resetModal}
                className="px-6 py-2 bg-gradient-to-r from-cyan-500 to-cyan-600 text-black font-bold hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300"
              >
                Try Again
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
