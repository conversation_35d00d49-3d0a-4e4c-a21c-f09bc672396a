/**
 * Data Synchronization Service for PressureMax
 * Replaces all mock/placeholder data with real Facebook campaign data
 */

import { facebookDataService, FacebookCampaignData, FacebookLead } from './facebookDataService';
import { db } from './database';
import { Campaign, Lead } from '../types/database';

export interface SyncResult {
  success: boolean;
  campaignsSynced: number;
  leadsSynced: number;
  errors: string[];
  lastSync: Date;
}

export interface SyncProgress {
  stage: 'campaigns' | 'leads' | 'cleanup' | 'complete';
  current: number;
  total: number;
  currentItem: string;
}

class DataSyncService {
  private isSync = false;
  private lastSyncTime: Date | null = null;
  private syncListeners: Array<(progress: SyncProgress) => void> = [];

  /**
   * Subscribe to sync progress updates
   */
  onSyncProgress(listener: (progress: SyncProgress) => void): () => void {
    this.syncListeners.push(listener);
    return () => {
      this.syncListeners = this.syncListeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify sync progress listeners
   */
  private notifyProgress(progress: SyncProgress): void {
    this.syncListeners.forEach(listener => {
      try {
        listener(progress);
      } catch (error) {
        console.error('Error notifying sync progress listener:', error);
      }
    });
  }

  /**
   * Check if sync is currently running
   */
  isSyncing(): boolean {
    return this.isSync;
  }

  /**
   * Get last sync time
   */
  getLastSyncTime(): Date | null {
    return this.lastSyncTime;
  }

  /**
   * Convert Facebook campaign data to local campaign format
   */
  private convertFacebookCampaignToLocal(fbCampaign: FacebookCampaignData): Omit<Campaign, 'id' | 'created_at' | 'updated_at'> {
    // Find the primary ad for creative data
    const primaryAd = fbCampaign.adSets[0]?.ads[0];
    const creative = primaryAd?.creative;

    return {
      template_id: 'facebook-sync',
      name: fbCampaign.name,
      facebook_campaign_id: fbCampaign.id,
      budget: fbCampaign.daily_budget ? fbCampaign.daily_budget / 100 : 0,
      start_date: new Date(fbCampaign.created_time),
      end_date: undefined,
      custom_creative: {
        headline: creative?.title || fbCampaign.name,
        description: creative?.body || `Campaign synced from Facebook Ads Manager`,
        primary_text: creative?.body || '',
        call_to_action: creative?.call_to_action_type || 'Learn More'
      },
      custom_targeting: {
        facebook_settings: {
          objective: fbCampaign.objective,
          account_id: fbCampaign.account_id,
          account_name: fbCampaign.account_name,
          adSets: fbCampaign.adSets.map(adSet => ({
            id: adSet.id,
            name: adSet.name,
            bid_strategy: adSet.bid_strategy,
            bid_amount: adSet.bid_amount,
            optimization_goal: adSet.optimization_goal,
            billing_event: adSet.billing_event,
            targeting: adSet.targeting,
            daily_budget: adSet.daily_budget,
            ads: adSet.ads.map(ad => ({
              id: ad.id,
              name: ad.name,
              creative: ad.creative
            }))
          }))
        }
      },
      metrics: {
        impressions: fbCampaign.metrics.impressions,
        clicks: fbCampaign.metrics.clicks,
        ctr: fbCampaign.metrics.ctr,
        cpc: fbCampaign.metrics.cpc,
        cpl: fbCampaign.metrics.cpl,
        leads_generated: fbCampaign.metrics.leads,
        spend: fbCampaign.metrics.spend,
        last_sync: new Date()
      },
      status: this.mapFacebookStatus(fbCampaign.status)
    };
  }

  /**
   * Convert Facebook lead to local lead format
   */
  private convertFacebookLeadToLocal(fbLead: FacebookLead, campaignName: string): Omit<Lead, 'id' | 'created_at' | 'updated_at'> {
    // Extract contact information from field_data
    const fieldData = fbLead.field_data || [];
    const getFieldValue = (name: string): string => {
      const field = fieldData.find(f => f.name.toLowerCase().includes(name.toLowerCase()));
      return field?.values[0] || '';
    };

    const firstName = getFieldValue('first_name') || getFieldValue('name');
    const lastName = getFieldValue('last_name') || getFieldValue('surname');
    const email = getFieldValue('email');
    const phone = getFieldValue('phone') || getFieldValue('mobile');

    return {
      name: `${firstName} ${lastName}`.trim() || 'Facebook Lead',
      email: email || '',
      phone: phone || '',
      service_type: 'pressure_washing', // Default service type
      property_type: 'residential', // Default property type
      property_size: 'medium', // Default size
      urgency: 'within_week', // Default urgency
      budget_range: '$200-500', // Default budget
      additional_notes: `Lead from Facebook campaign: ${campaignName}`,
      lead_source: 'facebook_ads',
      lead_quality: this.calculateLeadQuality(fbLead),
      facebook_lead_id: fbLead.id,
      facebook_campaign_id: fbLead.campaign_id,
      facebook_ad_id: fbLead.ad_id,
      facebook_adset_id: fbLead.adset_id,
      facebook_form_id: fbLead.form_id,
      facebook_field_data: fieldData,
      status: 'new'
    };
  }

  /**
   * Calculate lead quality score based on Facebook lead data
   */
  private calculateLeadQuality(fbLead: FacebookLead): number {
    let score = 50; // Base score

    // Higher quality if not organic (paid lead)
    if (!fbLead.is_organic) score += 20;

    // Check for complete contact information
    const fieldData = fbLead.field_data || [];
    const hasEmail = fieldData.some(f => f.name.toLowerCase().includes('email') && f.values[0]);
    const hasPhone = fieldData.some(f => f.name.toLowerCase().includes('phone') && f.values[0]);
    const hasName = fieldData.some(f => f.name.toLowerCase().includes('name') && f.values[0]);

    if (hasEmail) score += 10;
    if (hasPhone) score += 15;
    if (hasName) score += 5;

    // Platform bonus
    if (fbLead.platform === 'facebook') score += 5;

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Map Facebook campaign status to local status
   */
  private mapFacebookStatus(fbStatus: string): 'draft' | 'active' | 'paused' | 'completed' | 'error' {
    switch (fbStatus.toUpperCase()) {
      case 'ACTIVE':
        return 'active';
      case 'PAUSED':
        return 'paused';
      case 'DELETED':
      case 'ARCHIVED':
        return 'completed';
      default:
        return 'paused';
    }
  }

  /**
   * Perform full data synchronization
   */
  async syncAllData(): Promise<SyncResult> {
    if (this.isSync) {
      throw new Error('Sync already in progress');
    }

    this.isSync = true;
    const errors: string[] = [];
    let campaignsSynced = 0;
    let leadsSynced = 0;

    try {
      // Stage 1: Sync Campaigns
      this.notifyProgress({
        stage: 'campaigns',
        current: 0,
        total: 0,
        currentItem: 'Fetching Facebook campaigns...'
      });

      const facebookCampaigns = await facebookDataService.getAllCampaignData();
      
      this.notifyProgress({
        stage: 'campaigns',
        current: 0,
        total: facebookCampaigns.length,
        currentItem: `Found ${facebookCampaigns.length} campaigns`
      });

      // Get existing campaigns to avoid duplicates
      const existingCampaigns = await db.getCampaigns();
      const existingFbIds = new Set(existingCampaigns.map(c => c.facebook_campaign_id).filter(Boolean));

      for (let i = 0; i < facebookCampaigns.length; i++) {
        const fbCampaign = facebookCampaigns[i];
        
        this.notifyProgress({
          stage: 'campaigns',
          current: i + 1,
          total: facebookCampaigns.length,
          currentItem: fbCampaign.name
        });

        try {
          if (!existingFbIds.has(fbCampaign.id)) {
            const localCampaign = this.convertFacebookCampaignToLocal(fbCampaign);
            await db.createCampaign(localCampaign);
            campaignsSynced++;
          } else {
            // Update existing campaign metrics
            const existingCampaign = existingCampaigns.find(c => c.facebook_campaign_id === fbCampaign.id);
            if (existingCampaign) {
              await db.updateCampaign(existingCampaign.id, {
                metrics: {
                  impressions: fbCampaign.metrics.impressions,
                  clicks: fbCampaign.metrics.clicks,
                  ctr: fbCampaign.metrics.ctr,
                  cpc: fbCampaign.metrics.cpc,
                  cpl: fbCampaign.metrics.cpl,
                  leads_generated: fbCampaign.metrics.leads,
                  spend: fbCampaign.metrics.spend,
                  last_sync: new Date()
                },
                status: this.mapFacebookStatus(fbCampaign.status)
              });
              campaignsSynced++;
            }
          }
        } catch (error) {
          errors.push(`Failed to sync campaign "${fbCampaign.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Stage 2: Sync Leads
      this.notifyProgress({
        stage: 'leads',
        current: 0,
        total: 0,
        currentItem: 'Fetching Facebook leads...'
      });

      const facebookLeads = await facebookDataService.getAllLeads();
      
      this.notifyProgress({
        stage: 'leads',
        current: 0,
        total: facebookLeads.length,
        currentItem: `Found ${facebookLeads.length} leads`
      });

      // Get existing leads to avoid duplicates
      const existingLeads = await db.getLeads();
      const existingFbLeadIds = new Set(existingLeads.map(l => l.facebook_lead_id).filter(Boolean));

      for (let i = 0; i < facebookLeads.length; i++) {
        const fbLead = facebookLeads[i];
        
        this.notifyProgress({
          stage: 'leads',
          current: i + 1,
          total: facebookLeads.length,
          currentItem: `Lead ${fbLead.id}`
        });

        try {
          if (!existingFbLeadIds.has(fbLead.id)) {
            // Find campaign name for this lead
            const campaign = facebookCampaigns.find(c => c.id === fbLead.campaign_id);
            const campaignName = campaign?.name || 'Unknown Campaign';
            
            const localLead = this.convertFacebookLeadToLocal(fbLead, campaignName);
            await db.createLead(localLead);
            leadsSynced++;
          }
        } catch (error) {
          errors.push(`Failed to sync lead "${fbLead.id}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Stage 3: Cleanup
      this.notifyProgress({
        stage: 'cleanup',
        current: 1,
        total: 1,
        currentItem: 'Finalizing sync...'
      });

      this.lastSyncTime = new Date();

      // Stage 4: Complete
      this.notifyProgress({
        stage: 'complete',
        current: 1,
        total: 1,
        currentItem: 'Sync completed successfully'
      });

      return {
        success: true,
        campaignsSynced,
        leadsSynced,
        errors,
        lastSync: this.lastSyncTime
      };

    } catch (error) {
      errors.push(`Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        success: false,
        campaignsSynced,
        leadsSynced,
        errors,
        lastSync: new Date()
      };
    } finally {
      this.isSync = false;
    }
  }

  /**
   * Quick sync - only update metrics for existing campaigns
   */
  async quickSync(): Promise<SyncResult> {
    if (this.isSync) {
      throw new Error('Sync already in progress');
    }

    this.isSync = true;
    const errors: string[] = [];
    let campaignsSynced = 0;

    try {
      const existingCampaigns = await db.getCampaigns();
      const facebookCampaigns = existingCampaigns.filter(c => c.facebook_campaign_id);

      for (const campaign of facebookCampaigns) {
        try {
          // Get updated metrics from Facebook
          const fbCampaignData = await facebookDataService.getAllCampaignData();
          const fbCampaign = fbCampaignData.find(c => c.id === campaign.facebook_campaign_id);

          if (fbCampaign) {
            await db.updateCampaign(campaign.id, {
              metrics: {
                impressions: fbCampaign.metrics.impressions,
                clicks: fbCampaign.metrics.clicks,
                ctr: fbCampaign.metrics.ctr,
                cpc: fbCampaign.metrics.cpc,
                cpl: fbCampaign.metrics.cpl,
                leads_generated: fbCampaign.metrics.leads,
                spend: fbCampaign.metrics.spend,
                last_sync: new Date()
              },
              status: this.mapFacebookStatus(fbCampaign.status)
            });
            campaignsSynced++;
          }
        } catch (error) {
          errors.push(`Failed to update campaign "${campaign.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      this.lastSyncTime = new Date();

      return {
        success: true,
        campaignsSynced,
        leadsSynced: 0,
        errors,
        lastSync: this.lastSyncTime
      };

    } catch (error) {
      errors.push(`Quick sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return {
        success: false,
        campaignsSynced,
        leadsSynced: 0,
        errors,
        lastSync: new Date()
      };
    } finally {
      this.isSync = false;
    }
  }
}

// Export singleton instance
export const dataSyncService = new DataSyncService();
