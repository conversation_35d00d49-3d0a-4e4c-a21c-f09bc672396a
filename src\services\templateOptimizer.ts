/**
 * Template Optimizer for PressureMax
 * Analyzes Facebook campaign performance to optimize templates with proven successful configurations
 */

import { supabaseDb } from './supabaseDatabase';
import { facebookDataService } from './facebookDataService';
import { AdTemplate, Campaign } from '../types/database';
import type { FacebookCampaignData } from './facebookDataService';

export interface PerformanceAnalysis {
  topPerformingCampaigns: Campaign[];
  bestCreatives: {
    headline: string;
    description: string;
    callToAction: string;
    performance: {
      ctr: number;
      cpl: number;
      leads: number;
    };
  }[];
  optimalBudgets: {
    service: string;
    averageBudget: number;
    bestPerformingBudget: number;
  }[];
  recommendations: string[];
}

export interface OptimizedTemplate {
  templateId: string;
  originalTemplate: AdTemplate;
  optimizedTemplate: AdTemplate;
  improvements: string[];
  expectedPerformanceGain: number;
}

class TemplateOptimizer {
  private readonly PERFORMANCE_THRESHOLD = 0.02; // 2% CTR minimum
  private readonly MIN_LEADS_THRESHOLD = 5; // Minimum leads for analysis
  private readonly MIN_SPEND_THRESHOLD = 100; // Minimum spend for reliable data

  /**
   * Analyze campaign performance and optimize templates
   */
  async optimizeAllTemplates(): Promise<OptimizedTemplate[]> {
    try {
      console.log('🔍 Starting template optimization analysis...');

      // Get all campaigns and templates
      const [campaigns, templates] = await Promise.all([
        supabaseDb.getCampaigns(),
        supabaseDb.getAdTemplates()
      ]);

      // Filter campaigns with sufficient performance data
      const qualifiedCampaigns = campaigns.filter(campaign => 
        campaign.metrics.spend >= this.MIN_SPEND_THRESHOLD &&
        campaign.metrics.leads_generated >= this.MIN_LEADS_THRESHOLD &&
        campaign.metrics.ctr >= this.PERFORMANCE_THRESHOLD
      );

      console.log(`📊 Analyzing ${qualifiedCampaigns.length} qualified campaigns for optimization`);

      if (qualifiedCampaigns.length === 0) {
        console.log('⚠️ No campaigns meet performance thresholds for optimization');
        return [];
      }

      // Analyze performance patterns
      const analysis = await this.analyzePerformancePatterns(qualifiedCampaigns);

      // Optimize each template
      const optimizedTemplates: OptimizedTemplate[] = [];
      for (const template of templates) {
        const optimized = await this.optimizeTemplate(template, analysis);
        if (optimized) {
          optimizedTemplates.push(optimized);
        }
      }

      console.log(`✅ Optimized ${optimizedTemplates.length} templates`);
      return optimizedTemplates;

    } catch (error) {
      console.error('❌ Error in template optimization:', error);
      throw error;
    }
  }

  /**
   * Analyze performance patterns from successful campaigns
   */
  private async analyzePerformancePatterns(campaigns: Campaign[]): Promise<PerformanceAnalysis> {
    // Sort campaigns by performance score (combination of CTR, CPL, and leads)
    const sortedCampaigns = campaigns.sort((a, b) => {
      const scoreA = this.calculatePerformanceScore(a);
      const scoreB = this.calculatePerformanceScore(b);
      return scoreB - scoreA;
    });

    const topPerformingCampaigns = sortedCampaigns.slice(0, Math.min(10, Math.ceil(campaigns.length * 0.3)));

    // Analyze creative elements
    const bestCreatives = this.analyzeBestCreatives(topPerformingCampaigns);

    // Analyze budget patterns
    const optimalBudgets = this.analyzeOptimalBudgets(topPerformingCampaigns);

    // Generate recommendations
    const recommendations = this.generateRecommendations(topPerformingCampaigns, bestCreatives, optimalBudgets);

    return {
      topPerformingCampaigns,
      bestCreatives,
      optimalBudgets,
      recommendations
    };
  }

  /**
   * Calculate performance score for campaign ranking
   */
  private calculatePerformanceScore(campaign: Campaign): number {
    const { ctr, cpl, leads_generated, spend } = campaign.metrics;
    
    // Normalize metrics (higher CTR and leads are better, lower CPL is better)
    const ctrScore = ctr * 100; // Convert to percentage
    const cplScore = cpl > 0 ? 100 / cpl : 0; // Inverse of CPL
    const leadsScore = leads_generated;
    const efficiencyScore = spend > 0 ? leads_generated / spend * 100 : 0;

    // Weighted combination
    return (ctrScore * 0.3) + (cplScore * 0.3) + (leadsScore * 0.2) + (efficiencyScore * 0.2);
  }

  /**
   * Analyze best performing creative elements
   */
  private analyzeBestCreatives(campaigns: Campaign[]): PerformanceAnalysis['bestCreatives'] {
    const creativeMap = new Map<string, {
      headline: string;
      description: string;
      callToAction: string;
      totalCtr: number;
      totalCpl: number;
      totalLeads: number;
      count: number;
    }>();

    campaigns.forEach(campaign => {
      const creative = campaign.custom_creative;
      if (!creative) return;

      const key = `${creative.headline}_${creative.description}_${creative.call_to_action}`;
      const existing = creativeMap.get(key);

      if (existing) {
        existing.totalCtr += campaign.metrics.ctr;
        existing.totalCpl += campaign.metrics.cpl;
        existing.totalLeads += campaign.metrics.leads_generated;
        existing.count++;
      } else {
        creativeMap.set(key, {
          headline: creative.headline,
          description: creative.description,
          callToAction: creative.call_to_action,
          totalCtr: campaign.metrics.ctr,
          totalCpl: campaign.metrics.cpl,
          totalLeads: campaign.metrics.leads_generated,
          count: 1
        });
      }
    });

    // Convert to array and calculate averages
    return Array.from(creativeMap.values())
      .map(item => ({
        headline: item.headline,
        description: item.description,
        callToAction: item.callToAction,
        performance: {
          ctr: item.totalCtr / item.count,
          cpl: item.totalCpl / item.count,
          leads: item.totalLeads / item.count
        }
      }))
      .sort((a, b) => this.calculateCreativeScore(b.performance) - this.calculateCreativeScore(a.performance))
      .slice(0, 5); // Top 5 creatives
  }

  /**
   * Calculate creative performance score
   */
  private calculateCreativeScore(performance: { ctr: number; cpl: number; leads: number }): number {
    const ctrScore = performance.ctr * 100;
    const cplScore = performance.cpl > 0 ? 100 / performance.cpl : 0;
    const leadsScore = performance.leads;
    return (ctrScore * 0.4) + (cplScore * 0.4) + (leadsScore * 0.2);
  }

  /**
   * Analyze optimal budget patterns by service type
   */
  private analyzeOptimalBudgets(campaigns: Campaign[]): PerformanceAnalysis['optimalBudgets'] {
    const serviceMap = new Map<string, { budgets: number[]; performances: number[] }>();

    campaigns.forEach(campaign => {
      // Infer service type from campaign name or template
      const service = this.inferServiceType(campaign.name);
      const budget = campaign.budget;
      const performance = this.calculatePerformanceScore(campaign);

      if (!serviceMap.has(service)) {
        serviceMap.set(service, { budgets: [], performances: [] });
      }

      serviceMap.get(service)!.budgets.push(budget);
      serviceMap.get(service)!.performances.push(performance);
    });

    return Array.from(serviceMap.entries()).map(([service, data]) => {
      const averageBudget = data.budgets.reduce((sum, b) => sum + b, 0) / data.budgets.length;
      
      // Find budget with best performance
      const bestIndex = data.performances.indexOf(Math.max(...data.performances));
      const bestPerformingBudget = data.budgets[bestIndex];

      return {
        service,
        averageBudget: Math.round(averageBudget),
        bestPerformingBudget: Math.round(bestPerformingBudget)
      };
    });
  }

  /**
   * Infer service type from campaign name
   */
  private inferServiceType(campaignName: string): string {
    const name = campaignName.toLowerCase();
    
    if (name.includes('house wash') || name.includes('residential')) return 'House Washing';
    if (name.includes('driveway') || name.includes('concrete')) return 'Driveway Cleaning';
    if (name.includes('roof') || name.includes('gutter')) return 'Roof & Gutter Cleaning';
    if (name.includes('commercial') || name.includes('business')) return 'Commercial Cleaning';
    if (name.includes('deck') || name.includes('fence')) return 'Deck & Fence Cleaning';
    
    return 'General Pressure Washing';
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(
    campaigns: Campaign[], 
    creatives: PerformanceAnalysis['bestCreatives'], 
    budgets: PerformanceAnalysis['optimalBudgets']
  ): string[] {
    const recommendations: string[] = [];

    // Creative recommendations
    if (creatives.length > 0) {
      const bestCreative = creatives[0];
      recommendations.push(`Use headline pattern: "${bestCreative.headline}" (avg CTR: ${(bestCreative.performance.ctr * 100).toFixed(2)}%)`);
      recommendations.push(`Optimize descriptions with: "${bestCreative.description.substring(0, 50)}..." pattern`);
      recommendations.push(`Best performing CTA: "${bestCreative.callToAction}"`);
    }

    // Budget recommendations
    budgets.forEach(budget => {
      recommendations.push(`${budget.service}: Optimal budget $${budget.bestPerformingBudget}/day (avg: $${budget.averageBudget})`);
    });

    // Performance insights
    const avgCtr = campaigns.reduce((sum, c) => sum + c.metrics.ctr, 0) / campaigns.length;
    const avgCpl = campaigns.reduce((sum, c) => sum + c.metrics.cpl, 0) / campaigns.length;
    
    recommendations.push(`Target CTR above ${(avgCtr * 100).toFixed(2)}% for optimal performance`);
    recommendations.push(`Aim for CPL below $${avgCpl.toFixed(2)} based on successful campaigns`);

    return recommendations;
  }

  /**
   * Optimize a single template based on analysis
   */
  private async optimizeTemplate(template: AdTemplate, analysis: PerformanceAnalysis): Promise<OptimizedTemplate | null> {
    try {
      const improvements: string[] = [];
      let expectedGain = 0;

      // Clone template for optimization
      const optimizedTemplate: AdTemplate = JSON.parse(JSON.stringify(template));

      // Optimize creative elements
      if (analysis.bestCreatives.length > 0) {
        const bestCreative = analysis.bestCreatives[0];
        
        // Update headline if current one is underperforming
        if (template.headline !== bestCreative.headline) {
          optimizedTemplate.headline = bestCreative.headline;
          improvements.push(`Updated headline to top-performing pattern`);
          expectedGain += 15; // Estimated 15% improvement
        }

        // Update description
        if (template.description !== bestCreative.description) {
          optimizedTemplate.description = bestCreative.description;
          improvements.push(`Optimized description based on successful campaigns`);
          expectedGain += 10;
        }

        // Update call to action
        if (template.call_to_action !== bestCreative.callToAction) {
          optimizedTemplate.call_to_action = bestCreative.callToAction;
          improvements.push(`Updated CTA to "${bestCreative.callToAction}"`);
          expectedGain += 8;
        }
      }

      // Optimize budget based on service type
      const serviceType = this.inferServiceType(template.name);
      const budgetData = analysis.optimalBudgets.find(b => b.service === serviceType);
      
      if (budgetData && template.default_budget !== budgetData.bestPerformingBudget) {
        optimizedTemplate.default_budget = budgetData.bestPerformingBudget;
        improvements.push(`Adjusted budget to optimal $${budgetData.bestPerformingBudget}/day`);
        expectedGain += 12;
      }

      // Only return optimization if there are meaningful improvements
      if (improvements.length === 0) {
        return null;
      }

      return {
        templateId: template.id,
        originalTemplate: template,
        optimizedTemplate,
        improvements,
        expectedPerformanceGain: Math.min(expectedGain, 50) // Cap at 50% expected gain
      };

    } catch (error) {
      console.error(`❌ Error optimizing template ${template.name}:`, error);
      return null;
    }
  }

  /**
   * Apply optimizations to templates in database
   */
  async applyOptimizations(optimizations: OptimizedTemplate[]): Promise<void> {
    try {
      console.log(`🔄 Applying ${optimizations.length} template optimizations...`);

      for (const optimization of optimizations) {
        await supabaseDb.updateAdTemplate(optimization.templateId, optimization.optimizedTemplate);
        console.log(`✅ Applied optimization to ${optimization.originalTemplate.name}`);
      }

      console.log('✅ All template optimizations applied successfully');
    } catch (error) {
      console.error('❌ Error applying template optimizations:', error);
      throw error;
    }
  }
}

export const templateOptimizer = new TemplateOptimizer();
