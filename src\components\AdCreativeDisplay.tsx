/**
 * Ad Creative Display Component
 * Displays Facebook ad creatives including images, videos, carousel assets, and copy text
 */

import React, { useState } from 'react';
import { 
  Image, 
  Play, 
  ExternalLink, 
  Copy, 
  Download,
  Eye,
  ChevronLeft,
  ChevronRight,
  FileText,
  Video,
  ImageIcon
} from 'lucide-react';

export interface AdCreative {
  id: string;
  name: string;
  title: string;
  body: string;
  call_to_action_type: string;
  image_url?: string;
  video_id?: string;
  thumbnail_url?: string;
  link_url?: string;
  object_story_spec?: any;
  asset_feed_spec?: any;
  image_hash?: string;
  image_crops?: any;
  video_data?: {
    video_id: string;
    image_url: string;
    video_url: string;
  };
}

interface AdCreativeDisplayProps {
  creative: AdCreative;
  className?: string;
  showDetails?: boolean;
  onImageClick?: (imageUrl: string) => void;
  onVideoClick?: (videoUrl: string) => void;
}

export const AdCreativeDisplay: React.FC<AdCreativeDisplayProps> = ({
  creative,
  className = '',
  showDetails = true,
  onImageClick,
  onVideoClick
}) => {
  const [currentAssetIndex, setCurrentAssetIndex] = useState(0);
  const [showFullText, setShowFullText] = useState(false);

  // Extract assets from creative
  const getAssets = () => {
    const assets: Array<{ type: 'image' | 'video'; url: string; thumbnail?: string }> = [];

    // Single image
    if (creative.image_url) {
      assets.push({ type: 'image', url: creative.image_url });
    }

    // Single video
    if (creative.video_data?.video_url) {
      assets.push({ 
        type: 'video', 
        url: creative.video_data.video_url,
        thumbnail: creative.video_data.image_url || creative.thumbnail_url
      });
    }

    // Carousel assets from processed carousel_assets
    if (creative.carousel_assets) {
      creative.carousel_assets.forEach((asset) => {
        assets.push({
          type: asset.type,
          url: asset.url,
          thumbnail: asset.thumbnail_url
        });
      });
    }

    return assets;
  };

  const assets = getAssets();
  const currentAsset = assets[currentAssetIndex];

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
    // Could add a toast notification here
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return showFullText ? text : `${text.substring(0, maxLength)}...`;
  };

  return (
    <div className={`bg-gray-800/30 rounded-xl p-4 ${className}`}>
      {/* Creative Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="bg-purple-500 p-1.5 rounded">
            <ImageIcon size={16} className="text-white" />
          </div>
          <div>
            <h4 className="text-white font-medium">{creative.name || 'Untitled Creative'}</h4>
            <p className="text-xs text-gray-400">Creative ID: {creative.id}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {creative.link_url && (
            <button
              onClick={() => window.open(creative.link_url, '_blank')}
              className="p-1.5 text-gray-400 hover:text-white transition-colors"
              title="Open link"
            >
              <ExternalLink size={16} />
            </button>
          )}
        </div>
      </div>

      {/* Media Assets */}
      {assets.length > 0 && (
        <div className="mb-4">
          <div className="relative bg-gray-900/50 rounded-lg overflow-hidden">
            {currentAsset.type === 'image' ? (
              <img
                src={currentAsset.url}
                alt={creative.title || 'Ad creative'}
                className="w-full h-48 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                onClick={() => onImageClick?.(currentAsset.url)}
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/placeholder-image.png';
                }}
              />
            ) : (
              <div 
                className="relative w-full h-48 bg-gray-900 flex items-center justify-center cursor-pointer hover:bg-gray-800 transition-colors"
                onClick={() => onVideoClick?.(currentAsset.url)}
              >
                {currentAsset.thumbnail ? (
                  <img
                    src={currentAsset.thumbnail}
                    alt="Video thumbnail"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Video size={48} className="text-gray-400" />
                )}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-black/50 rounded-full p-3">
                    <Play size={24} className="text-white ml-1" />
                  </div>
                </div>
              </div>
            )}

            {/* Asset Navigation */}
            {assets.length > 1 && (
              <>
                <button
                  onClick={() => setCurrentAssetIndex(Math.max(0, currentAssetIndex - 1))}
                  disabled={currentAssetIndex === 0}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-1 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft size={16} />
                </button>
                <button
                  onClick={() => setCurrentAssetIndex(Math.min(assets.length - 1, currentAssetIndex + 1))}
                  disabled={currentAssetIndex === assets.length - 1}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-1 rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight size={16} />
                </button>
                
                {/* Asset Indicators */}
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
                  {assets.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full ${
                        index === currentAssetIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </>
            )}

            {/* Asset Type Badge */}
            <div className="absolute top-2 left-2">
              <span className={`px-2 py-1 text-xs font-medium rounded ${
                currentAsset.type === 'video' 
                  ? 'bg-red-500 text-white' 
                  : 'bg-blue-500 text-white'
              }`}>
                {currentAsset.type === 'video' ? 'Video' : 'Image'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Creative Text Content */}
      {showDetails && (
        <div className="space-y-3">
          {/* Headline */}
          {creative.title && (
            <div>
              <div className="flex items-center justify-between mb-1">
                <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Headline</label>
                <button
                  onClick={() => handleCopyText(creative.title)}
                  className="text-gray-400 hover:text-white transition-colors"
                  title="Copy headline"
                >
                  <Copy size={12} />
                </button>
              </div>
              <p className="text-white text-sm font-medium">{creative.title}</p>
            </div>
          )}

          {/* Body Text */}
          {creative.body && (
            <div>
              <div className="flex items-center justify-between mb-1">
                <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Body Text</label>
                <div className="flex items-center space-x-2">
                  {creative.body.length > 100 && (
                    <button
                      onClick={() => setShowFullText(!showFullText)}
                      className="text-xs text-cyan-400 hover:text-cyan-300 transition-colors"
                    >
                      {showFullText ? 'Show Less' : 'Show More'}
                    </button>
                  )}
                  <button
                    onClick={() => handleCopyText(creative.body)}
                    className="text-gray-400 hover:text-white transition-colors"
                    title="Copy body text"
                  >
                    <Copy size={12} />
                  </button>
                </div>
              </div>
              <p className="text-gray-300 text-sm leading-relaxed">
                {truncateText(creative.body)}
              </p>
            </div>
          )}

          {/* Call to Action */}
          {creative.call_to_action_type && (
            <div>
              <label className="text-xs font-medium text-gray-400 uppercase tracking-wide">Call to Action</label>
              <div className="mt-1">
                <span className="inline-block bg-cyan-500 text-black px-3 py-1 rounded text-sm font-medium">
                  {creative.call_to_action_type.replace(/_/g, ' ')}
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdCreativeDisplay;
