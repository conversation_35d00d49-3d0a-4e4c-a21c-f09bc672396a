"""
VAPI Integration Service for PressureMax
Handles AI voice calling, appointment scheduling, and lead management
"""

import os
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass

@dataclass
class VAPICall:
    id: str
    assistant_id: str
    phone_number: str
    status: str
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None
    cost: Optional[float] = None
    transcript: Optional[str] = None
    summary: Optional[str] = None
    analysis: Optional[Dict] = None

@dataclass
class VAPIAssistant:
    id: str
    name: str
    model: Dict
    voice: Dict
    first_message: str
    recording_enabled: bool
    end_call_message: str
    end_call_phrases: List[str]
    max_duration_seconds: int

class VAPIService:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.vapi.ai"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def _make_request(self, endpoint: str, method: str = "GET", data: Dict = None) -> Dict:
        """Make HTTP request to VAPI API"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=self.headers)
            elif method == "POST":
                response = requests.post(url, headers=self.headers, json=data)
            elif method == "PUT":
                response = requests.put(url, headers=self.headers, json=data)
            elif method == "DELETE":
                response = requests.delete(url, headers=self.headers)
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"VAPI API Error: {e}")
            raise

    def create_pressure_washing_assistant(self) -> VAPIAssistant:
        """Create specialized assistant for pressure washing appointment booking"""
        assistant_data = {
            "name": "Sarah - PressureMax Appointment Assistant",
            "model": {
                "provider": "openai",
                "model": "gpt-4o",
                "messages": [
                    {
                        "role": "system",
                        "content": """You are Sarah, a professional appointment scheduling assistant for PressureMax pressure washing services.

WORKFLOW:
1. Greet warmly and confirm you're speaking with the right person
2. Verify they're still interested in pressure washing services
3. Ask about specific services needed (house washing, driveway, deck, roof, commercial)
4. Gather property details (type, size, urgency)
5. Check availability and offer 2-3 time slots
6. Book the appointment with all details
7. Confirm appointment and provide next steps

GUIDELINES:
- Keep responses under 35 words
- Be warm, professional, and efficient
- Emphasize FREE estimates with no obligation
- Always try to schedule a specific appointment
- Handle objections professionally
- End calls within 10 minutes

OBJECTION HANDLING:
- "Too expensive": "That's why we offer free estimates - no cost, no pressure"
- "Need to think": "What concerns can I address? Our estimates are always free"
- "Not ready": "When would work better? I can schedule a callback"

SUCCESS METRICS: Appointments booked and customer satisfaction"""
                    }
                ],
                "tools": [
                    {
                        "type": "function",
                        "function": {
                            "name": "checkAvailability",
                            "description": "Check available appointment slots",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "preferredDate": {"type": "string"},
                                    "serviceType": {"type": "string"},
                                    "urgency": {"type": "string", "enum": ["asap", "this_week", "this_month", "flexible"]}
                                },
                                "required": ["serviceType"]
                            }
                        }
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "bookAppointment",
                            "description": "Book confirmed appointment",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "customerName": {"type": "string"},
                                    "customerPhone": {"type": "string"},
                                    "appointmentDate": {"type": "string"},
                                    "appointmentTime": {"type": "string"},
                                    "serviceType": {"type": "string"},
                                    "leadId": {"type": "string"}
                                },
                                "required": ["customerName", "customerPhone", "appointmentDate", "appointmentTime", "serviceType"]
                            }
                        }
                    }
                ]
            },
            "voice": {
                "provider": "elevenlabs",
                "voiceId": "sarah"
            },
            "firstMessage": "Hi! This is Sarah from PressureMax calling about your pressure washing inquiry. I can help schedule your free estimate today. Do you have a quick minute?",
            "recordingEnabled": True,
            "endCallMessage": "Perfect! Your free estimate is confirmed. You'll get a confirmation text shortly. Thanks for choosing PressureMax!",
            "endCallPhrases": ["goodbye", "bye", "not interested", "no thank you"],
            "maxDurationSeconds": 600
        }

        result = self._make_request("/assistant", "POST", assistant_data)
        return VAPIAssistant(**result)

    def initiate_call(self, phone_number: str, assistant_id: str, lead_data: Dict = None) -> VAPICall:
        """Initiate a call to a lead"""
        call_data = {
            "assistantId": assistant_id,
            "phoneNumber": phone_number,
            "metadata": lead_data or {}
        }

        result = self._make_request("/call", "POST", call_data)
        return VAPICall(**result)

    def get_call(self, call_id: str) -> VAPICall:
        """Get call details and transcript"""
        result = self._make_request(f"/call/{call_id}")
        return VAPICall(**result)

    def get_calls(self, assistant_id: str = None) -> List[VAPICall]:
        """Get all calls for an assistant"""
        endpoint = f"/call?assistantId={assistant_id}" if assistant_id else "/call"
        result = self._make_request(endpoint)
        return [VAPICall(**call) for call in result.get("data", [])]

    def analyze_call_outcome(self, call_id: str) -> Dict:
        """Analyze call outcome for lead qualification"""
        call = self.get_call(call_id)
        
        if not call.transcript:
            return {
                "sentiment": "neutral",
                "intent": "unknown",
                "appointment_scheduled": False,
                "follow_up_needed": True,
                "lead_quality": "cold"
            }

        transcript = call.transcript.lower()
        
        # Sentiment analysis
        positive_words = ["yes", "interested", "sounds good", "perfect", "great", "awesome"]
        negative_words = ["no", "not interested", "busy", "expensive", "can't afford"]
        
        positive_count = sum(1 for word in positive_words if word in transcript)
        negative_count = sum(1 for word in negative_words if word in transcript)
        
        if positive_count > negative_count:
            sentiment = "positive"
        elif negative_count > positive_count:
            sentiment = "negative"
        else:
            sentiment = "neutral"

        # Intent detection
        intent = "information_gathering"
        if "schedule" in transcript or "appointment" in transcript:
            intent = "appointment_scheduling"
        elif "price" in transcript or "cost" in transcript or "estimate" in transcript:
            intent = "pricing_inquiry"
        elif "not interested" in transcript or "no thank" in transcript:
            intent = "rejection"

        # Appointment detection
        appointment_scheduled = ("schedule" in transcript and 
                               any(day in transcript for day in ["monday", "tuesday", "wednesday", "thursday", "friday", "tomorrow"]))

        # Follow-up assessment
        follow_up_needed = not appointment_scheduled and sentiment != "negative"

        # Lead quality
        if appointment_scheduled or (sentiment == "positive" and intent == "appointment_scheduling"):
            lead_quality = "hot"
        elif sentiment == "positive" or intent == "pricing_inquiry":
            lead_quality = "warm"
        else:
            lead_quality = "cold"

        return {
            "sentiment": sentiment,
            "intent": intent,
            "appointment_scheduled": appointment_scheduled,
            "follow_up_needed": follow_up_needed,
            "lead_quality": lead_quality
        }

    def trigger_new_lead_call(self, lead_data: Dict) -> Optional[VAPICall]:
        """Automatically call new leads"""
        try:
            print(f"🔥 NEW LEAD TRIGGER: Initiating call for {lead_data.get('name')}")
            
            # Create or get assistant
            assistant = self.create_pressure_washing_assistant()
            
            # Initiate call
            call = self.initiate_call(
                phone_number=lead_data["phone"],
                assistant_id=assistant.id,
                lead_data=lead_data
            )
            
            print(f"✅ Automatic call initiated: {call.id}")
            return call
            
        except Exception as e:
            print(f"❌ Failed to trigger automatic call: {e}")
            return None

    def run_bulk_call_campaign(self, leads: List[Dict], assistant_id: str) -> List[VAPICall]:
        """Run bulk calling campaign for lead reactivation"""
        calls = []
        
        print(f"🔄 REACTIVATION CAMPAIGN: Starting bulk calls for {len(leads)} leads")
        
        for lead in leads:
            try:
                call = self.initiate_call(
                    phone_number=lead["phone"],
                    assistant_id=assistant_id,
                    lead_data=lead
                )
                calls.append(call)
                
                print(f"📞 Reactivation call initiated for {lead.get('name')}: {call.id}")
                
                # Rate limiting - wait between calls
                import time
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ Failed to call {lead.get('name')}: {e}")
        
        print(f"✅ Reactivation campaign completed: {len(calls)}/{len(leads)} calls initiated")
        return calls

# Global VAPI service instance
vapi_service = None

def get_vapi_service():
    """Get VAPI service instance"""
    global vapi_service
    if not vapi_service:
        api_key = os.getenv('VAPI_API_KEY')
        if not api_key:
            raise ValueError("VAPI_API_KEY environment variable not set")
        vapi_service = VAPIService(api_key)
    return vapi_service
