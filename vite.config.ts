import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import fs from 'fs';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  server: {
    https: process.env.NODE_ENV === 'development' ? {
      key: fs.existsSync(path.resolve(__dirname, 'localhost-key.pem'))
        ? fs.readFileSync(path.resolve(__dirname, 'localhost-key.pem'))
        : undefined,
      cert: fs.existsSync(path.resolve(__dirname, 'localhost.pem'))
        ? fs.readFileSync(path.resolve(__dirname, 'localhost.pem'))
        : undefined,
    } : false,
    host: true,
    port: 5174
  }
});
