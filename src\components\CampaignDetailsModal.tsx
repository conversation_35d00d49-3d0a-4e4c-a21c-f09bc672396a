/**
 * Campaign Details Modal for PressureMax
 * Shows detailed Facebook campaign settings for imported campaigns
 */

import React, { useState } from 'react';
import { X, ExternalLink, Target, DollarSign, BarChart3, Settings, Image as ImageIcon } from 'lucide-react';
import { Campaign } from '../types/database';
import AdCreativeDisplay from './AdCreativeDisplay';
import MediaViewerModal from './MediaViewerModal';

interface CampaignDetailsModalProps {
  campaign: Campaign | null;
  isOpen: boolean;
  onClose: () => void;
}

export const CampaignDetailsModal: React.FC<CampaignDetailsModalProps> = ({
  campaign,
  isOpen,
  onClose
}) => {
  const [selectedMediaUrl, setSelectedMediaUrl] = useState<string>('');
  const [selectedMediaType, setSelectedMediaType] = useState<'image' | 'video'>('image');
  const [isMediaViewerOpen, setIsMediaViewerOpen] = useState(false);

  if (!isOpen || !campaign) return null;

  const facebookSettings = campaign.custom_targeting?.facebook_settings;

  const handleImageClick = (imageUrl: string) => {
    setSelectedMediaUrl(imageUrl);
    setSelectedMediaType('image');
    setIsMediaViewerOpen(true);
  };

  const handleVideoClick = (videoUrl: string) => {
    setSelectedMediaUrl(videoUrl);
    setSelectedMediaType('video');
    setIsMediaViewerOpen(true);
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="bg-cyan-500 p-2 rounded-lg">
              <BarChart3 className="text-black" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{campaign.name}</h2>
              <p className="text-sm text-gray-400">Campaign Details</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Basic Campaign Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="bg-gray-800/30 rounded-xl p-4">
              <h3 className="text-white font-semibold mb-3 flex items-center space-x-2">
                <Target size={16} />
                <span>Campaign Overview</span>
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className={`font-medium ${
                    campaign.status === 'active' ? 'text-green-400' :
                    campaign.status === 'paused' ? 'text-yellow-400' :
                    'text-gray-400'
                  }`}>
                    {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Budget:</span>
                  <span className="text-white">${campaign.budget.toFixed(2)}/day</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Created:</span>
                  <span className="text-white">{campaign.start_date.toLocaleDateString()}</span>
                </div>
                {campaign.facebook_campaign_id && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Facebook ID:</span>
                    <span className="text-white font-mono text-xs">{campaign.facebook_campaign_id}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-800/30 rounded-xl p-4">
              <h3 className="text-white font-semibold mb-3 flex items-center space-x-2">
                <DollarSign size={16} />
                <span>Performance</span>
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Spend:</span>
                  <span className="text-white">${campaign.metrics.spend.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Impressions:</span>
                  <span className="text-white">{campaign.metrics.impressions.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Clicks:</span>
                  <span className="text-white">{campaign.metrics.clicks.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Leads:</span>
                  <span className="text-white">{campaign.metrics.leads_generated}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Facebook Settings for Imported Campaigns */}
          {facebookSettings && (
            <div className="bg-gray-800/30 rounded-xl p-6">
              <h3 className="text-white font-semibold mb-4 flex items-center space-x-2">
                <Settings size={16} />
                <span>Facebook Campaign Settings</span>
                <ExternalLink size={14} className="text-gray-400" />
              </h3>

              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">Campaign Objective:</span>
                  <span className="text-cyan-400 font-medium">{facebookSettings.objective}</span>
                </div>
              </div>

              {/* Ad Sets */}
              {facebookSettings.adSets && facebookSettings.adSets.length > 0 && (
                <div>
                  <h4 className="text-white font-medium mb-3">Ad Sets ({facebookSettings.adSets.length})</h4>
                  <div className="space-y-4">
                    {facebookSettings.adSets.map((adSet: any, index: number) => (
                      <div key={adSet.id} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="text-white font-medium">{adSet.name}</h5>
                          <span className="text-xs text-gray-400 font-mono">{adSet.id}</span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <h6 className="text-gray-300 font-medium mb-2">Bidding & Budget</h6>
                            <div className="space-y-1">
                              <div className="flex justify-between">
                                <span className="text-gray-400">Bid Strategy:</span>
                                <span className="text-cyan-400 font-medium">{adSet.bid_strategy}</span>
                              </div>
                              {adSet.bid_amount && (
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Bid Amount:</span>
                                  <span className="text-white">${(adSet.bid_amount / 100).toFixed(2)}</span>
                                </div>
                              )}
                              {adSet.daily_budget && (
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Daily Budget:</span>
                                  <span className="text-white">${(adSet.daily_budget / 100).toFixed(2)}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div>
                            <h6 className="text-gray-300 font-medium mb-2">Optimization</h6>
                            <div className="space-y-1">
                              <div className="flex justify-between">
                                <span className="text-gray-400">Goal:</span>
                                <span className="text-white">{adSet.optimization_goal}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-400">Billing Event:</span>
                                <span className="text-white">{adSet.billing_event}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Targeting Info */}
                        {adSet.targeting && (
                          <div className="mt-4 pt-3 border-t border-gray-600">
                            <h6 className="text-gray-300 font-medium mb-2">Targeting</h6>
                            <div className="text-xs text-gray-400">
                              {adSet.targeting.geo_locations && (
                                <div className="mb-1">
                                  <span className="font-medium">Location: </span>
                                  {adSet.targeting.geo_locations.countries && 
                                    `Countries: ${adSet.targeting.geo_locations.countries.join(', ')}`}
                                  {adSet.targeting.geo_locations.custom_locations && 
                                    `Custom locations: ${adSet.targeting.geo_locations.custom_locations.length} area(s)`}
                                </div>
                              )}
                              {(adSet.targeting.age_min || adSet.targeting.age_max) && (
                                <div className="mb-1">
                                  <span className="font-medium">Age: </span>
                                  {adSet.targeting.age_min || 18} - {adSet.targeting.age_max || 65}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Ad Creatives Section */}
          {campaign.facebook_campaign_id && (
            <div className="mt-6">
              <div className="flex items-center space-x-2 mb-4">
                <ImageIcon className="text-purple-400" size={20} />
                <h3 className="text-lg font-semibold text-white">Ad Creatives</h3>
              </div>

              {/* Mock creative data - in real implementation, this would come from Facebook API */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <AdCreativeDisplay
                  creative={{
                    id: `creative_${campaign.facebook_campaign_id}_1`,
                    name: `${campaign.name} - Primary Creative`,
                    title: campaign.custom_creative?.headline || 'Professional Pressure Washing Services',
                    body: campaign.custom_creative?.primary_text || campaign.custom_creative?.description || 'Transform your property with our expert pressure washing services. Get a free estimate today!',
                    call_to_action_type: campaign.custom_creative?.call_to_action || 'GET_QUOTE',
                    image_url: '/api/placeholder/600/400', // Placeholder - would be real Facebook image URL
                  }}
                  onImageClick={handleImageClick}
                  onVideoClick={handleVideoClick}
                />

                {/* Additional creatives would be displayed here */}
                <div className="bg-gray-800/30 rounded-xl p-4 border-2 border-dashed border-gray-600 flex items-center justify-center">
                  <div className="text-center">
                    <ImageIcon className="text-gray-400 mx-auto mb-2" size={32} />
                    <p className="text-gray-400 text-sm">Additional creatives</p>
                    <p className="text-gray-500 text-xs">Available when connected to Facebook</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-700">
            {campaign.facebook_campaign_id && (
              <a
                href={`https://business.facebook.com/adsmanager/manage/campaigns?act=${campaign.facebook_campaign_id}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <ExternalLink size={16} />
                <span>View in Facebook</span>
              </a>
            )}
            <button
              onClick={onClose}
              className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>

      {/* Media Viewer Modal */}
      <MediaViewerModal
        isOpen={isMediaViewerOpen}
        onClose={() => setIsMediaViewerOpen(false)}
        mediaUrl={selectedMediaUrl}
        mediaType={selectedMediaType}
        title={`${campaign.name} - Creative Asset`}
      />
    </div>
  );
};
