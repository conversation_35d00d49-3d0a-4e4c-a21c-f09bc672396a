/**
 * Admin Login Component for PressureMax
 * Secure admin authentication with enhanced security features
 */

import React, { useState } from 'react';
import { Shield, Eye, EyeOff, Lock, Mail, AlertTriangle } from 'lucide-react';
import { adminAuth } from '../services/adminAuth';
import { useAuth } from '../contexts/AuthContext';

interface AdminLoginProps {
  isOpen: boolean;
  onSuccess: () => void;
  onCancel: () => void;
}

export const AdminLogin: React.FC<AdminLoginProps> = ({
  isOpen,
  onSuccess,
  onCancel
}) => {
  const { setUser } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    mfaCode: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [requiresMFA, setRequiresMFA] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = await adminAuth.adminLogin(
        formData.email,
        formData.password,
        formData.mfaCode
      );

      if (result.success && result.user && result.token) {
        // Store admin token
        localStorage.setItem('pressuremax_admin_token', result.token);
        localStorage.setItem('pressuremax_token', result.token);
        
        // Set user in auth context
        setUser(result.user);
        
        // Log admin login
        await adminAuth.logAdminAction(result.user.id, 'admin_login', {
          timestamp: new Date(),
          ip: 'unknown' // In production, get from request
        });

        onSuccess();
      } else if (result.requiresMFA) {
        setRequiresMFA(true);
        setError('Please enter your MFA code');
      } else {
        setError(result.error || 'Invalid admin credentials');
      }
    } catch (error) {
      setError('Login failed. Please try again.');
      console.error('Admin login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user types
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-gray-900 border border-gray-700 max-w-md w-full mx-4">
        {/* Header */}
        <div className="bg-red-900/20 border-b border-red-500/30 p-6">
          <div className="flex items-center space-x-3">
            <Shield className="text-red-400" size={24} />
            <div>
              <h2 className="text-xl font-bold text-white">Admin Access</h2>
              <p className="text-sm text-red-400">Restricted Area - Authorized Personnel Only</p>
            </div>
          </div>
        </div>

        {/* Security Warning */}
        <div className="bg-yellow-900/20 border-b border-yellow-500/30 p-4">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="text-yellow-400 mt-0.5" size={16} />
            <div className="text-sm text-yellow-400">
              <p className="font-medium">Security Notice</p>
              <p>All admin actions are logged and monitored. Unauthorized access attempts will be reported.</p>
            </div>
          </div>
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Admin Email
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-3 py-2 focus:border-red-500 focus:outline-none"
                placeholder="<EMAIL>"
                required
                autoComplete="username"
              />
            </div>
          </div>

          {/* Password */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-10 py-2 focus:border-red-500 focus:outline-none"
                placeholder="Enter admin password"
                required
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
          </div>

          {/* MFA Code (if required) */}
          {requiresMFA && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                MFA Code
              </label>
              <input
                type="text"
                value={formData.mfaCode}
                onChange={(e) => handleInputChange('mfaCode', e.target.value)}
                className="w-full bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-red-500 focus:outline-none"
                placeholder="Enter 6-digit code"
                maxLength={6}
                required
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-900/20 border border-red-500/30 p-3">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="text-red-400" size={16} />
                <span className="text-red-400 text-sm">{error}</span>
              </div>
            </div>
          )}

          {/* Demo Credentials */}
          <div className="bg-blue-900/20 border border-blue-500/30 p-3">
            <p className="text-blue-400 text-sm font-medium mb-1">Demo Credentials:</p>
            <p className="text-blue-300 text-xs">Email: <EMAIL></p>
            <p className="text-blue-300 text-xs">Password: admin123</p>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !formData.email || !formData.password}
              className="flex-1 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white py-2 px-4 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  <span>Authenticating...</span>
                </>
              ) : (
                <>
                  <Shield size={16} />
                  <span>Admin Login</span>
                </>
              )}
            </button>
          </div>
        </form>

        {/* Footer */}
        <div className="bg-gray-800 border-t border-gray-700 p-4">
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>PressureMax Admin Panel v1.0</span>
            <span>Secure Connection</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Admin Access Button Component
interface AdminAccessButtonProps {
  onClick: () => void;
  className?: string;
}

export const AdminAccessButton: React.FC<AdminAccessButtonProps> = ({
  onClick,
  className = ''
}) => {
  return (
    <button
      onClick={onClick}
      className={`fixed bottom-4 right-4 bg-red-600 hover:bg-red-700 text-white p-3 shadow-lg hover:shadow-xl transition-all duration-300 z-40 ${className}`}
      title="Admin Access"
    >
      <Shield size={20} />
    </button>
  );
};
