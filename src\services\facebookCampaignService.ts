/**
 * Facebook Campaign Service for PressureMax
 * Direct Facebook Graph API integration for creating campaigns
 */

import { facebookIntegration } from './facebookIntegration';
import { AdTemplate } from '../types/database';

export interface FacebookCampaignData {
  name: string;
  objective: string;
  status: string;
  daily_budget?: number;
  special_ad_categories?: string[];
}

export interface FacebookAdSetData {
  name: string;
  campaign_id: string;
  daily_budget: number;
  billing_event: string;
  optimization_goal: string;
  bid_strategy: string;
  bid_amount?: number;
  status: string;
  targeting: any;
  start_time?: string;
  end_time?: string;
}

export interface FacebookCreativeData {
  name: string;
  object_story_spec: {
    page_id: string;
    link_data: {
      link: string;
      message: string;
      name: string;
      description?: string;
      call_to_action?: {
        type: string;
        value?: {
          link: string;
        };
      };
    };
  };
}

export interface FacebookAdData {
  name: string;
  adset_id: string;
  creative: {
    creative_id: string;
  };
  status: string;
}

export interface CampaignCreationResult {
  success: boolean;
  campaign_id?: string;
  adset_id?: string;
  creative_id?: string;
  ad_id?: string;
  error?: string;
  facebook_campaign_id?: string;
}

export class FacebookCampaignService {
  private readonly API_VERSION = 'v23.0';
  private readonly BASE_URL = 'https://graph.facebook.com';
  private lastRequestTime = 0;
  private readonly MIN_REQUEST_INTERVAL = 2000; // 2 seconds between requests
  private retryCount = 0;
  private readonly MAX_RETRIES = 3;

  /**
   * Rate limiting helper with exponential backoff
   */
  private async waitForRateLimit(isRetry: boolean = false): Promise<void> {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    let waitTime = this.MIN_REQUEST_INTERVAL;

    // Add exponential backoff for retries
    if (isRetry) {
      waitTime = this.MIN_REQUEST_INTERVAL * Math.pow(2, this.retryCount);
      console.log(`⏱️ Exponential backoff: waiting ${waitTime}ms (retry ${this.retryCount})`);
    }

    if (timeSinceLastRequest < waitTime) {
      const actualWaitTime = waitTime - timeSinceLastRequest;
      console.log(`⏱️ Rate limiting: waiting ${actualWaitTime}ms before next request`);
      await new Promise(resolve => setTimeout(resolve, actualWaitTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * Make a Facebook Graph API call with rate limiting and retry logic
   */
  private async makeGraphAPICall(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any,
    isRetry: boolean = false
  ): Promise<any> {
    // Apply rate limiting
    await this.waitForRateLimit(isRetry);

    const facebookStatus = facebookIntegration.getIntegrationStatus();

    if (!facebookStatus.isConnected || !facebookStatus.accessToken) {
      throw new Error('Facebook account not connected. Please connect your Facebook Business account first.');
    }

    const url = `${this.BASE_URL}/${this.API_VERSION}/${endpoint}`;

    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    // Add access token to URL for GET requests, or to body for POST requests
    if (method === 'GET') {
      const urlWithToken = new URL(url);
      urlWithToken.searchParams.append('access_token', facebookStatus.accessToken);
      
      if (data) {
        Object.keys(data).forEach(key => {
          urlWithToken.searchParams.append(key, data[key]);
        });
      }
      
      const response = await fetch(urlWithToken.toString(), options);
      const result = await response.json();

      if (result.error) {
        // Handle rate limiting with retry
        if (result.error.code === 4 || result.error.message.includes('rate limit') || result.error.message.includes('request limit')) {
          if (this.retryCount < this.MAX_RETRIES) {
            this.retryCount++;
            console.warn(`⚠️ Facebook API rate limit reached, retrying (${this.retryCount}/${this.MAX_RETRIES})`);
            return await this.makeGraphAPICall(endpoint, method, data, true);
          } else {
            this.retryCount = 0;
            throw new Error(`Facebook API Error: User request limit reached - max retries exceeded`);
          }
        }
        throw new Error(`Facebook API Error (${result.error.code}/${result.error.error_subcode}): ${result.error.type} - ${result.error.error_user_title || result.error.message} - ${result.error.error_user_msg || ''}`);
      }

      // Reset retry count on success
      this.retryCount = 0;
      
      return result;
    } else {
      // POST/PUT requests - use URL-encoded approach for Facebook API
      const urlWithParams = new URL(url);
      urlWithParams.searchParams.append('access_token', facebookStatus.accessToken);

      // Add all data fields as URL parameters
      Object.keys(data).forEach(key => {
        const value = data[key];
        if (value !== undefined && value !== null) {
          if (typeof value === 'object') {
            urlWithParams.searchParams.append(key, JSON.stringify(value));
          } else {
            urlWithParams.searchParams.append(key, value.toString());
          }
        }
      });

      // Use POST with empty body but parameters in URL
      options.method = 'POST';
      options.headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
      };
      options.body = '';

      console.log('Facebook API URL:', urlWithParams.toString());

      const response = await fetch(urlWithParams.toString(), options);
      const result = await response.json();

      if (result.error) {
        console.error('Facebook API Error Details:', JSON.stringify(result.error, null, 2));
        console.error('Full Facebook Response:', JSON.stringify(result, null, 2));
        const errorMessage = result.error.message || 'Unknown error';
        const errorCode = result.error.code || 'Unknown code';
        const errorSubcode = result.error.error_subcode || '';
        const errorUserTitle = result.error.error_user_title || '';
        const errorUserMsg = result.error.error_user_msg || '';
        throw new Error(`Facebook API Error (${errorCode}${errorSubcode ? '/' + errorSubcode : ''}): ${errorMessage}${errorUserTitle ? ' - ' + errorUserTitle : ''}${errorUserMsg ? ' - ' + errorUserMsg : ''}`);
      }

      return result;
    }
  }

  /**
   * Create a complete Facebook campaign from template
   */
  async createCampaignFromTemplate(
    template: AdTemplate,
    adAccountId: string,
    pageId: string,
    options: {
      budget: number;
      targeting: any;
      websiteUrl: string;
      customHeadline?: string;
      customDescription?: string;
      customCTA?: string;
    }
  ): Promise<CampaignCreationResult> {
    try {
      console.log('🚀 Creating Facebook campaign from template:', template.name);
      
      // Step 1: Create Campaign
      const campaignData: FacebookCampaignData = {
        name: `${template.name} - ${new Date().toLocaleDateString()}`,
        objective: 'OUTCOME_LEADS',
        status: 'PAUSED',
        daily_budget: Math.round(options.budget * 100), // Facebook expects cents
        special_ad_categories: []
      };

      console.log('📝 Creating campaign...');
      const campaign = await this.makeGraphAPICall(
        `act_${adAccountId}/campaigns`,
        'POST',
        campaignData
      );

      console.log('✅ Campaign created:', campaign.id);

      // Step 2: Create Ad Set - With proper bid strategy
      const dailyBudgetCents = Math.max(Math.round(options.budget * 100), 1000); // $10.00 minimum

      // Calculate a reasonable bid cap - typically 20-30% of daily budget for lead generation
      const bidCapCents = Math.max(Math.round(dailyBudgetCents * 0.25), 500); // Minimum $5.00 bid cap

      const adSetData = {
        name: `${template.name} AdSet`,
        campaign_id: campaign.id,
        daily_budget: dailyBudgetCents,
        billing_event: 'IMPRESSIONS',
        optimization_goal: 'LEAD_GENERATION',
        bid_strategy: 'LOWEST_COST_WITH_BID_CAP', // Highest volume with bid cap (required for lead generation)
        bid_amount: bidCapCents, // Set bid cap to 25% of daily budget
        status: 'PAUSED',
        targeting: {
          geo_locations: {
            countries: ['US']
          },
          age_min: 25,
          age_max: 65
        }
      };

      console.log('📝 Creating ad set...');
      console.log('Ad Set Data:', JSON.stringify(adSetData, null, 2));
      console.log('Campaign ID:', campaign.id);
      console.log('Daily Budget (cents):', dailyBudgetCents);
      console.log('Bid Strategy:', adSetData.bid_strategy);
      console.log('Bid Cap (cents):', bidCapCents);
      const adSet = await this.makeGraphAPICall(
        `act_${adAccountId}/adsets`,
        'POST',
        adSetData
      );

      console.log('✅ Ad set created:', adSet.id);

      // Step 3: Create Ad Creative
      const creativeData: FacebookCreativeData = {
        name: `${template.name} - Creative`,
        object_story_spec: {
          page_id: pageId,
          link_data: {
            link: options.websiteUrl,
            message: options.customDescription || template.creative.description,
            name: options.customHeadline || template.creative.headline,
            description: template.creative.description,
            call_to_action: {
              type: options.customCTA || template.creative.call_to_action || 'LEARN_MORE'
            }
          }
        }
      };

      console.log('📝 Creating ad creative...');
      const creative = await this.makeGraphAPICall(
        `act_${adAccountId}/adcreatives`,
        'POST',
        creativeData
      );

      console.log('✅ Ad creative created:', creative.id);

      // Step 4: Create Ad
      const adData: FacebookAdData = {
        name: `${template.name} - Ad`,
        adset_id: adSet.id,
        creative: {
          creative_id: creative.id
        },
        status: 'PAUSED'
      };

      console.log('📝 Creating ad...');
      const ad = await this.makeGraphAPICall(
        `act_${adAccountId}/ads`,
        'POST',
        adData
      );

      console.log('✅ Ad created:', ad.id);

      console.log('🎉 Campaign creation completed successfully!');

      return {
        success: true,
        campaign_id: campaign.id,
        adset_id: adSet.id,
        creative_id: creative.id,
        ad_id: ad.id,
        facebook_campaign_id: campaign.id
      };

    } catch (error) {
      console.error('❌ Error creating Facebook campaign:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get campaign performance data
   */
  async getCampaignInsights(campaignId: string): Promise<any> {
    try {
      const insights = await this.makeGraphAPICall(
        `${campaignId}/insights`,
        'GET',
        {
          fields: 'impressions,clicks,spend,conversions,cost_per_result,ctr,cpm'
        }
      );

      return insights.data?.[0] || null;
    } catch (error) {
      console.error('Error fetching campaign insights:', error);
      return null;
    }
  }

  /**
   * Update campaign status (pause/resume)
   */
  async updateCampaignStatus(campaignId: string, status: 'ACTIVE' | 'PAUSED'): Promise<boolean> {
    try {
      await this.makeGraphAPICall(
        campaignId,
        'POST',
        { status }
      );
      return true;
    } catch (error) {
      console.error('Error updating campaign status:', error);
      return false;
    }
  }

  /**
   * Get all campaigns from a Facebook ad account
   */
  async getCampaigns(adAccountId: string): Promise<any[]> {
    try {
      const campaigns = await this.makeGraphAPICall(
        `act_${adAccountId}/campaigns`,
        'GET',
        {
          fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,special_ad_categories'
        }
      );

      return campaigns.data || [];
    } catch (error) {
      console.error('Error fetching campaigns:', error);
      return [];
    }
  }

  /**
   * Get detailed campaign information including ad sets and their settings
   */
  async getCampaignDetails(campaignId: string): Promise<any> {
    try {
      // Get campaign basic info
      const campaign = await this.makeGraphAPICall(
        campaignId,
        'GET',
        {
          fields: 'id,name,objective,status,created_time,updated_time,daily_budget,lifetime_budget,special_ad_categories'
        }
      );

      // Get ad sets for this campaign
      const adSets = await this.makeGraphAPICall(
        `${campaignId}/adsets`,
        'GET',
        {
          fields: 'id,name,status,daily_budget,lifetime_budget,billing_event,optimization_goal,bid_strategy,bid_amount,targeting,created_time,updated_time'
        }
      );

      // Get ads for this campaign
      const ads = await this.makeGraphAPICall(
        `${campaignId}/ads`,
        'GET',
        {
          fields: 'id,name,status,creative,created_time,updated_time'
        }
      );

      return {
        campaign,
        adSets: adSets.data || [],
        ads: ads.data || []
      };
    } catch (error) {
      console.error('Error fetching campaign details:', error);
      throw error;
    }
  }

  /**
   * Delete campaign
   */
  async deleteCampaign(campaignId: string): Promise<boolean> {
    try {
      await this.makeGraphAPICall(
        campaignId,
        'DELETE'
      );
      return true;
    } catch (error) {
      console.error('Error deleting campaign:', error);
      return false;
    }
  }
}

// Export singleton instance
export const facebookCampaignService = new FacebookCampaignService();
