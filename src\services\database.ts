// Mock database service for PressureMax
import {
  ServiceType,
  PressureService,
  AdTemplate,
  Campaign,
  Lead,
  ServiceTypeWithServices
} from '../types/database';
import { newLeadTrigger } from './newLeadTrigger';

// Mock data - Service Types
export const mockServiceTypes: ServiceType[] = [
  {
    id: 'st-1',
    name: 'Residential',
    icon: 'home',
    color: '#10B981',
    sort_order: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 'st-2',
    name: 'Commercial',
    icon: 'building',
    color: '#3B82F6',
    sort_order: 2,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 'st-3',
    name: 'Special<PERSON>',
    icon: 'star',
    color: '#F59E0B',
    sort_order: 3,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  }
];

// Mock data - Pressure Services
export const mockPressureServices: PressureService[] = [
  // Residential Services
  {
    id: 'ps-1',
    service_type_id: 'st-1',
    name: 'House Washing',
    description: 'Complete exterior house cleaning including siding, windows, and trim',
    typical_pricing: '$300-600',
    season_preference: 'Spring/Summer',
    equipment_needed: ['Soft wash system', 'Extension ladder', 'Biodegradable detergent'],
    sort_order: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 'ps-2',
    service_type_id: 'st-1',
    name: 'Driveway Cleaning',
    description: 'Concrete and asphalt driveway pressure washing and stain removal',
    typical_pricing: '$150-300',
    season_preference: 'Year-round',
    equipment_needed: ['Surface cleaner', 'Hot water unit', 'Degreaser'],
    sort_order: 2,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 'ps-3',
    service_type_id: 'st-1',
    name: 'Deck Restoration',
    description: 'Deck cleaning, staining, and sealing services',
    typical_pricing: '$400-800',
    season_preference: 'Spring/Fall',
    equipment_needed: ['Deck cleaner', 'Stain applicator', 'Sanding equipment'],
    sort_order: 3,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  // Commercial Services
  {
    id: 'ps-4',
    service_type_id: 'st-2',
    name: 'Building Washing',
    description: 'Commercial building exterior cleaning and maintenance',
    typical_pricing: '$0.15-0.30/sqft',
    season_preference: 'Year-round',
    equipment_needed: ['High-reach equipment', 'Commercial detergents', 'Safety gear'],
    sort_order: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  {
    id: 'ps-5',
    service_type_id: 'st-2',
    name: 'Parking Lot Cleaning',
    description: 'Large area concrete cleaning and maintenance',
    typical_pricing: '$0.08-0.15/sqft',
    season_preference: 'Year-round',
    equipment_needed: ['Surface cleaner', 'Hot water trailer', 'Line striping equipment'],
    sort_order: 2,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  },
  // Specialty Services
  {
    id: 'ps-6',
    service_type_id: 'st-3',
    name: 'Roof Cleaning',
    description: 'Soft wash roof cleaning for algae and moss removal',
    typical_pricing: '$400-800',
    season_preference: 'Spring/Summer',
    equipment_needed: ['Soft wash system', 'Roof-safe chemicals', 'Safety equipment'],
    sort_order: 1,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01')
  }
];

// Mock data - Ad Templates
export const mockAdTemplates: AdTemplate[] = [
  {
    id: 'template-1',
    name: 'House Washing Spring Special',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Transform your home\'s curb appeal in just one day! Professional house washing that removes years of dirt, mildew, and stains. See the dramatic difference!',
      headline: 'Professional House Washing - Spring Special',
      description: 'Get your home ready for spring with our professional house washing service.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: true,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/House_Washing_Spring_Special.png'
    },
    targeting: {
      location_radius: '15_miles',
      age_range: { min: 35, max: 65 },
      home_value_range: '$200k_plus',
      interests: ['home_improvement', 'property_maintenance', 'homeownership'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 50, max: 150, suggested: 75 },
    seasonal_timing: ['spring', 'summer'],
    target_customer: 'homeowner',
    pricing_strategy: 'discount',
    performance: {
      ctr: '3.2%',
      cpl: '$12',
      conversions: 24,
      total_spend: 1800,
      total_leads: 150,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: true,
    template_type: 'global',
    is_public: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-2',
    name: 'Driveway Transformation',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-2',
    category: 'Residential',
    service: 'Driveway Cleaning',
    creative: {
      primary_text: 'Make your driveway look brand new again! Remove oil stains, dirt, and grime. Increase your property value instantly with professional driveway cleaning.',
      headline: 'Driveway Cleaning - Like New Again',
      description: 'Professional driveway cleaning that removes years of buildup.',
      call_to_action: 'Book Now',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Driveway_Transformation_Before_After.png'
    },
    targeting: {
      location_radius: '10_miles',
      age_range: { min: 30, max: 60 },
      home_value_range: '$150k_plus',
      interests: ['home_improvement', 'diy', 'property_value'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 30, max: 100, suggested: 50 },
    seasonal_timing: ['spring', 'summer', 'fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '2.8%',
      cpl: '$15',
      conversions: 18,
      total_spend: 900,
      total_leads: 60,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    template_type: 'global',
    is_public: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-3',
    name: 'Commercial Building Refresh',
    service_type_id: 'st-2',
    pressure_service_id: 'ps-4',
    category: 'Commercial',
    service: 'Building Washing',
    creative: {
      primary_text: 'Maintain your professional image with regular building cleaning services. Attract more customers with a clean, welcoming exterior.',
      headline: 'Professional Building Washing Services',
      description: 'Keep your business looking its best with professional exterior cleaning.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Commercial_Building_Refresh_Professional.png'
    },
    targeting: {
      location_radius: '25_miles',
      age_range: { min: 25, max: 65 },
      home_value_range: 'all',
      interests: ['business_owner', 'property_management', 'commercial_real_estate'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 100, max: 300, suggested: 150 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '1.9%',
      cpl: '$45',
      conversions: 8,
      total_spend: 1200,
      total_leads: 27,
      last_updated: new Date()
    },
    status: 'draft',
    is_featured: false,
    template_type: 'global',
    is_public: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-4',
    name: 'Deck Restoration Spring Package',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-3',
    category: 'Residential',
    service: 'Deck Restoration',
    creative: {
      primary_text: 'Get your deck ready for summer entertaining! Professional deck cleaning, staining, and sealing. Transform your outdoor space into the perfect gathering spot.',
      headline: 'Deck Restoration - Summer Ready',
      description: 'Complete deck restoration service including cleaning, staining, and sealing.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: true,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Deck_Restoration_Summer_Ready.png'
    },
    targeting: {
      location_radius: '20_miles',
      age_range: { min: 35, max: 65 },
      home_value_range: '$250k_plus',
      interests: ['home_improvement', 'outdoor_living', 'deck_maintenance'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 75, max: 200, suggested: 100 },
    seasonal_timing: ['spring', 'summer'],
    target_customer: 'homeowner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '2.5%',
      cpl: '$25',
      conversions: 12,
      total_spend: 1500,
      total_leads: 60,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    template_type: 'global',
    is_public: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-5',
    name: 'Roof Cleaning Special Offer',
    service_type_id: 'st-3',
    pressure_service_id: 'ps-6',
    category: 'Specialty',
    service: 'Roof Cleaning',
    creative: {
      primary_text: 'Protect your biggest investment! Professional roof cleaning removes harmful algae, moss, and stains. Extend your roof life and boost curb appeal.',
      headline: 'Professional Roof Cleaning - Protect Your Investment',
      description: 'Safe, effective roof cleaning that extends roof life and improves appearance.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Roof_Cleaning_Safety_Professional.png'
    },
    targeting: {
      location_radius: '25_miles',
      age_range: { min: 40, max: 70 },
      home_value_range: '$300k_plus',
      interests: ['home_maintenance', 'property_value', 'roof_care'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 100, max: 250, suggested: 150 },
    seasonal_timing: ['spring', 'summer', 'fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '1.8%',
      cpl: '$35',
      conversions: 6,
      total_spend: 1050,
      total_leads: 30,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-6',
    name: 'Parking Lot Maintenance',
    service_type_id: 'st-2',
    pressure_service_id: 'ps-5',
    category: 'Commercial',
    service: 'Parking Lot Cleaning',
    creative: {
      primary_text: 'First impressions matter! Keep your business parking lot clean and professional. Regular maintenance prevents costly repairs and attracts more customers.',
      headline: 'Professional Parking Lot Cleaning',
      description: 'Maintain a professional image with regular parking lot cleaning services.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Parking_Lot_Maintenance_Professional.png'
    },
    targeting: {
      location_radius: '30_miles',
      age_range: { min: 25, max: 65 },
      home_value_range: 'all',
      interests: ['business_owner', 'property_management', 'facility_maintenance'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 150, max: 400, suggested: 200 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '1.5%',
      cpl: '$55',
      conversions: 4,
      total_spend: 1100,
      total_leads: 20,
      last_updated: new Date()
    },
    status: 'draft',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-7',
    name: 'Sidewalk & Walkway Cleaning',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-2',
    category: 'Residential',
    service: 'Driveway Cleaning',
    creative: {
      primary_text: 'Safe walkways start with clean walkways! Remove slippery algae, moss, and stains from your sidewalks and walkways. Improve safety and curb appeal.',
      headline: 'Sidewalk Cleaning - Safety First',
      description: 'Professional sidewalk and walkway cleaning for safety and appearance.',
      call_to_action: 'Book Now',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Sidewalk_Cleaning_Safety_First.png'
    },
    targeting: {
      location_radius: '15_miles',
      age_range: { min: 30, max: 70 },
      home_value_range: '$150k_plus',
      interests: ['home_safety', 'property_maintenance', 'walkway_cleaning'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 40, max: 120, suggested: 60 },
    seasonal_timing: ['spring', 'summer', 'fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '3.1%',
      cpl: '$18',
      conversions: 15,
      total_spend: 900,
      total_leads: 50,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-8',
    name: 'Patio & Pool Area Cleaning',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-2',
    category: 'Residential',
    service: 'Driveway Cleaning',
    creative: {
      primary_text: 'Get your outdoor entertaining space ready! Professional patio and pool area cleaning removes dirt, algae, and stains. Perfect for summer gatherings.',
      headline: 'Patio & Pool Area Cleaning - Summer Ready',
      description: 'Transform your outdoor space with professional patio and pool area cleaning.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: true,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Patio_Pool_Area_Summer_Ready.png'
    },
    targeting: {
      location_radius: '20_miles',
      age_range: { min: 30, max: 60 },
      home_value_range: '$200k_plus',
      interests: ['outdoor_living', 'pool_maintenance', 'entertaining'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 60, max: 150, suggested: 80 },
    seasonal_timing: ['spring', 'summer'],
    target_customer: 'homeowner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '2.9%',
      cpl: '$22',
      conversions: 18,
      total_spend: 1200,
      total_leads: 55,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-9',
    name: 'Fleet Vehicle Washing',
    service_type_id: 'st-2',
    pressure_service_id: 'ps-4',
    category: 'Commercial',
    service: 'Building Washing',
    creative: {
      primary_text: 'Keep your fleet looking professional! Regular vehicle washing maintains your brand image and extends vehicle life. Mobile service available.',
      headline: 'Professional Fleet Vehicle Washing',
      description: 'Mobile fleet washing services that keep your vehicles looking professional.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Fleet_Vehicle_Washing_Mobile.png'
    },
    targeting: {
      location_radius: '50_miles',
      age_range: { min: 25, max: 65 },
      home_value_range: 'all',
      interests: ['fleet_management', 'business_owner', 'vehicle_maintenance'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 200, max: 500, suggested: 300 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '1.2%',
      cpl: '$75',
      conversions: 3,
      total_spend: 1500,
      total_leads: 20,
      last_updated: new Date()
    },
    status: 'draft',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-10',
    name: 'Fence Cleaning & Restoration',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Restore your fence to like-new condition! Professional fence cleaning removes years of dirt, mildew, and weathering. Protect your investment.',
      headline: 'Fence Cleaning & Restoration',
      description: 'Professional fence cleaning that restores and protects your investment.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Fence_Cleaning_Restoration.png'
    },
    targeting: {
      location_radius: '15_miles',
      age_range: { min: 35, max: 65 },
      home_value_range: '$180k_plus',
      interests: ['home_improvement', 'fence_maintenance', 'property_value'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 50, max: 130, suggested: 70 },
    seasonal_timing: ['spring', 'summer', 'fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '2.7%',
      cpl: '$20',
      conversions: 14,
      total_spend: 980,
      total_leads: 49,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  // Seasonal Templates
  {
    id: 'template-11',
    name: 'Spring Cleaning Package',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Spring has arrived! Get your home ready with our complete spring cleaning package. House washing, driveway cleaning, and more. Fresh start guaranteed!',
      headline: 'Spring Cleaning Package - Fresh Start',
      description: 'Complete spring cleaning package for your entire property.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: true,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Spring_Cleaning_Package_Fresh_Start.png'
    },
    targeting: {
      location_radius: '20_miles',
      age_range: { min: 30, max: 65 },
      home_value_range: '$200k_plus',
      interests: ['spring_cleaning', 'home_improvement', 'seasonal_maintenance'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 80, max: 200, suggested: 120 },
    seasonal_timing: ['spring'],
    target_customer: 'homeowner',
    pricing_strategy: 'discount',
    performance: {
      ctr: '3.5%',
      cpl: '$16',
      conversions: 28,
      total_spend: 1680,
      total_leads: 105,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-12',
    name: 'Fall Maintenance Special',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Prepare for winter! Fall cleaning removes summer buildup and protects your home through the cold months. Don\'t let dirt and grime damage your property.',
      headline: 'Fall Maintenance - Winter Prep',
      description: 'Prepare your home for winter with professional fall cleaning services.',
      call_to_action: 'Book Now',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Fall_Maintenance_Winter_Prep.png'
    },
    targeting: {
      location_radius: '18_miles',
      age_range: { min: 35, max: 70 },
      home_value_range: '$180k_plus',
      interests: ['home_maintenance', 'winter_prep', 'seasonal_cleaning'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 60, max: 160, suggested: 90 },
    seasonal_timing: ['fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '2.8%',
      cpl: '$19',
      conversions: 22,
      total_spend: 1330,
      total_leads: 70,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-13',
    name: 'Holiday Ready Home',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Get your home holiday-ready! Professional cleaning before family gatherings and celebrations. Make the best impression this holiday season.',
      headline: 'Holiday Ready Home Cleaning',
      description: 'Professional home cleaning for the perfect holiday presentation.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Holiday_Ready_Home_Cleaning.png'
    },
    targeting: {
      location_radius: '15_miles',
      age_range: { min: 30, max: 65 },
      home_value_range: '$200k_plus',
      interests: ['holiday_entertaining', 'family_gatherings', 'home_presentation'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 70, max: 180, suggested: 100 },
    seasonal_timing: ['winter'],
    target_customer: 'homeowner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '3.0%',
      cpl: '$24',
      conversions: 16,
      total_spend: 1200,
      total_leads: 50,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  // Commercial Templates
  {
    id: 'template-14',
    name: 'Restaurant Exterior Cleaning',
    service_type_id: 'st-2',
    pressure_service_id: 'ps-4',
    category: 'Commercial',
    service: 'Building Washing',
    creative: {
      primary_text: 'First impressions matter in the restaurant business! Keep your exterior spotless with regular professional cleaning. Attract more customers with a clean, inviting appearance.',
      headline: 'Restaurant Exterior Cleaning',
      description: 'Professional exterior cleaning services for restaurants and food service businesses.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Restaurant_Exterior_Cleaning_Professional.png'
    },
    targeting: {
      location_radius: '25_miles',
      age_range: { min: 25, max: 65 },
      home_value_range: 'all',
      interests: ['restaurant_owner', 'food_service', 'business_maintenance'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 120, max: 300, suggested: 180 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '1.6%',
      cpl: '$48',
      conversions: 7,
      total_spend: 1260,
      total_leads: 26,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-15',
    name: 'Retail Store Front Cleaning',
    service_type_id: 'st-2',
    pressure_service_id: 'ps-4',
    category: 'Commercial',
    service: 'Building Washing',
    creative: {
      primary_text: 'Boost foot traffic with a spotless storefront! Professional cleaning removes dirt, grime, and weather stains. A clean store attracts more customers.',
      headline: 'Retail Store Front Cleaning',
      description: 'Professional storefront cleaning that attracts more customers.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Retail_Store_Front_Cleaning.png'
    },
    targeting: {
      location_radius: '20_miles',
      age_range: { min: 25, max: 60 },
      home_value_range: 'all',
      interests: ['retail_business', 'store_owner', 'customer_attraction'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 100, max: 250, suggested: 150 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '1.8%',
      cpl: '$42',
      conversions: 9,
      total_spend: 1260,
      total_leads: 30,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  // Specialty & Premium Templates
  {
    id: 'template-16',
    name: 'Graffiti Removal Service',
    service_type_id: 'st-3',
    pressure_service_id: 'ps-6',
    category: 'Specialty',
    service: 'Roof Cleaning',
    creative: {
      primary_text: 'Remove unwanted graffiti quickly and completely! Professional graffiti removal that restores your property\'s appearance without damage.',
      headline: 'Professional Graffiti Removal',
      description: 'Fast, effective graffiti removal that restores your property.',
      call_to_action: 'Contact Us',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Graffiti_Removal_Service_Professional.png'
    },
    targeting: {
      location_radius: '30_miles',
      age_range: { min: 25, max: 65 },
      home_value_range: 'all',
      interests: ['property_management', 'business_owner', 'graffiti_removal'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 80, max: 200, suggested: 120 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '2.1%',
      cpl: '$38',
      conversions: 8,
      total_spend: 1140,
      total_leads: 30,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-17',
    name: 'Gutter Cleaning & Washing',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Clean gutters protect your home! Professional gutter cleaning and exterior washing prevents water damage and maintains your home\'s value.',
      headline: 'Gutter Cleaning & House Washing',
      description: 'Complete gutter cleaning and house washing service.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Gutter_Cleaning_Service_Professional.png'
    },
    targeting: {
      location_radius: '15_miles',
      age_range: { min: 35, max: 70 },
      home_value_range: '$200k_plus',
      interests: ['home_maintenance', 'gutter_cleaning', 'water_damage_prevention'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 60, max: 150, suggested: 85 },
    seasonal_timing: ['spring', 'fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'competitive',
    performance: {
      ctr: '2.6%',
      cpl: '$21',
      conversions: 19,
      total_spend: 1275,
      total_leads: 61,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-18',
    name: 'Window Cleaning Add-On',
    service_type_id: 'st-1',
    pressure_service_id: 'ps-1',
    category: 'Residential',
    service: 'House Washing',
    creative: {
      primary_text: 'Crystal clear windows complete the look! Add professional window cleaning to your house washing service for the ultimate curb appeal.',
      headline: 'House Washing + Window Cleaning',
      description: 'Complete exterior cleaning including house washing and window cleaning.',
      call_to_action: 'Get Free Estimate',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: false,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Window_Cleaning_Service_Crystal_Clear.png'
    },
    targeting: {
      location_radius: '12_miles',
      age_range: { min: 30, max: 65 },
      home_value_range: '$250k_plus',
      interests: ['home_improvement', 'window_cleaning', 'premium_services'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 70, max: 180, suggested: 110 },
    seasonal_timing: ['spring', 'summer', 'fall'],
    target_customer: 'homeowner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '2.4%',
      cpl: '$28',
      conversions: 13,
      total_spend: 1540,
      total_leads: 55,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-19',
    name: 'Post-Construction Cleanup',
    service_type_id: 'st-3',
    pressure_service_id: 'ps-6',
    category: 'Specialty',
    service: 'Roof Cleaning',
    creative: {
      primary_text: 'Construction complete? We\'ll handle the cleanup! Professional post-construction pressure washing removes cement, paint, and construction debris.',
      headline: 'Post-Construction Cleanup',
      description: 'Professional cleanup after construction projects.',
      call_to_action: 'Request Quote',
      media_requirements: {
        before_after_photos: true,
        action_video: true,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Post_Construction_Cleanup_Professional.png'
    },
    targeting: {
      location_radius: '40_miles',
      age_range: { min: 25, max: 65 },
      home_value_range: 'all',
      interests: ['construction', 'contractors', 'post_construction_cleanup'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 150, max: 400, suggested: 250 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'business_owner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '1.4%',
      cpl: '$65',
      conversions: 5,
      total_spend: 1625,
      total_leads: 25,
      last_updated: new Date()
    },
    status: 'draft',
    is_featured: false,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  },
  {
    id: 'template-20',
    name: 'Emergency Storm Cleanup',
    service_type_id: 'st-3',
    pressure_service_id: 'ps-6',
    category: 'Specialty',
    service: 'Roof Cleaning',
    creative: {
      primary_text: 'Storm damage? We respond fast! Emergency pressure washing services to clean up storm debris, mud, and damage. Available 24/7.',
      headline: 'Emergency Storm Cleanup - 24/7 Response',
      description: '24/7 emergency pressure washing for storm cleanup and damage.',
      call_to_action: 'Contact Us',
      media_requirements: {
        before_after_photos: true,
        action_video: false,
        equipment_shots: true,
        image_specs: { width: 1200, height: 628, format: 'jpg' }
      },
      image_path: '/images/campaigns/Emergency_Storm_Cleanup_Urgent.png'
    },
    targeting: {
      location_radius: '50_miles',
      age_range: { min: 25, max: 70 },
      home_value_range: 'all',
      interests: ['storm_damage', 'emergency_services', 'property_restoration'],
      exclude_competitors: true,
      custom_audiences: [],
      lookalike_audiences: []
    },
    budget_range: { min: 200, max: 500, suggested: 300 },
    seasonal_timing: ['spring', 'summer', 'fall', 'winter'],
    target_customer: 'homeowner',
    pricing_strategy: 'premium',
    performance: {
      ctr: '1.8%',
      cpl: '$55',
      conversions: 6,
      total_spend: 1650,
      total_leads: 30,
      last_updated: new Date()
    },
    status: 'active',
    is_featured: false,
    template_type: 'global',
    is_public: true,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    created_by: 'user-1'
  }
].map(template => ({
  ...template,
  template_type: template.template_type || 'global',
  is_public: template.is_public !== undefined ? template.is_public : true
}));

// Mock data - Campaigns
const mockCampaigns: Campaign[] = [
  {
    id: 'campaign-1',
    name: 'House Washing Spring Special - 12/24/2024',
    template_id: 'template-1',
    facebook_campaign_id: 'fb_123456789',
    budget: 50,
    start_date: new Date('2024-12-20'),
    end_date: new Date('2025-01-20'),
    custom_creative: {
      headline: 'Spring House Washing Special - 30% Off!',
      description: 'Get your home ready for spring with our professional house washing service.',
      primary_text: 'Transform your home\'s exterior with our professional house washing service.',
      call_to_action: 'Get Quote'
    },
    metrics: {
      impressions: 12450,
      clicks: 234,
      ctr: 1.88,
      cpc: 1.05,
      cpl: 13.65,
      leads_generated: 18,
      spend: 245.67,
      last_sync: new Date()
    },
    status: 'active',
    created_at: new Date('2024-12-20'),
    updated_at: new Date('2024-12-24'),
    launched_at: new Date('2024-12-20')
  },
  {
    id: 'campaign-2',
    name: 'Driveway Cleaning - Holiday Special',
    template_id: 'template-2',
    facebook_campaign_id: 'fb_987654321',
    budget: 35,
    start_date: new Date('2024-12-15'),
    end_date: new Date('2025-01-15'),
    custom_creative: {
      headline: 'Transform Your Driveway This Holiday Season',
      description: 'Professional driveway cleaning that makes a lasting impression.',
      primary_text: 'Get your driveway looking like new with our professional cleaning service.',
      call_to_action: 'Book Now'
    },
    metrics: {
      impressions: 8920,
      clicks: 156,
      ctr: 1.75,
      cpc: 3.64,
      cpl: 47.32,
      leads_generated: 12,
      spend: 567.89,
      last_sync: new Date()
    },
    status: 'paused',
    created_at: new Date('2024-12-15'),
    updated_at: new Date('2024-12-22'),
    launched_at: new Date('2024-12-15')
  },
  {
    id: 'campaign-3',
    name: 'Commercial Building Refresh',
    template_id: 'template-3',
    facebook_campaign_id: 'fb_456789123',
    budget: 75,
    start_date: new Date('2024-11-01'),
    end_date: new Date('2024-12-01'),
    custom_creative: {
      headline: 'Professional Commercial Building Cleaning',
      description: 'Maintain your business image with our commercial cleaning services.',
      primary_text: 'Keep your commercial property looking professional with our expert cleaning services.',
      call_to_action: 'Get Estimate'
    },
    metrics: {
      impressions: 15670,
      clicks: 298,
      ctr: 1.90,
      cpc: 7.55,
      cpl: 93.75,
      leads_generated: 24,
      spend: 2250,
      last_sync: new Date()
    },
    status: 'completed',
    created_at: new Date('2024-11-01'),
    updated_at: new Date('2024-12-01'),
    launched_at: new Date('2024-11-01')
  }
];

// Mock Database Service Class
export class MockDatabaseService {
  private serviceTypes: ServiceType[] = [...mockServiceTypes];
  private pressureServices: PressureService[] = [...mockPressureServices];
  private adTemplates: AdTemplate[] = [...mockAdTemplates];
  private campaigns: Campaign[] = [...mockCampaigns];
  private leads: Lead[] = [];

  // Service Types
  async getServiceTypes(): Promise<ServiceType[]> {
    return this.serviceTypes;
  }

  async getServiceTypeById(id: string): Promise<ServiceType | null> {
    return this.serviceTypes.find(st => st.id === id) || null;
  }

  async getServiceTypesWithServices(): Promise<ServiceTypeWithServices[]> {
    return this.serviceTypes.map(serviceType => ({
      ...serviceType,
      services: this.pressureServices.filter(ps => ps.service_type_id === serviceType.id)
    }));
  }

  // Pressure Services
  async getPressureServices(): Promise<PressureService[]> {
    return this.pressureServices;
  }

  async getPressureServicesByType(serviceTypeId: string): Promise<PressureService[]> {
    return this.pressureServices.filter(ps => ps.service_type_id === serviceTypeId);
  }

  async getPressureServiceById(id: string): Promise<PressureService | null> {
    return this.pressureServices.find(ps => ps.id === id) || null;
  }

  // Ad Templates
  async getAdTemplates(): Promise<AdTemplate[]> {
    return this.adTemplates;
  }

  async getAdTemplateById(id: string): Promise<AdTemplate | null> {
    return this.adTemplates.find(template => template.id === id) || null;
  }

  async getAdTemplatesByCategory(category: string): Promise<AdTemplate[]> {
    return this.adTemplates.filter(template => template.category === category);
  }

  async getAdTemplatesByService(serviceId: string): Promise<AdTemplate[]> {
    return this.adTemplates.filter(template => template.pressure_service_id === serviceId);
  }

  async createAdTemplate(template: Omit<AdTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<AdTemplate> {
    const newTemplate: AdTemplate = {
      ...template,
      id: `template-${Date.now()}`,
      created_at: new Date(),
      updated_at: new Date()
    };
    this.adTemplates.push(newTemplate);
    return newTemplate;
  }

  async updateAdTemplate(id: string, updates: Partial<AdTemplate>): Promise<AdTemplate | null> {
    const index = this.adTemplates.findIndex(template => template.id === id);
    if (index === -1) return null;
    
    this.adTemplates[index] = {
      ...this.adTemplates[index],
      ...updates,
      updated_at: new Date()
    };
    return this.adTemplates[index];
  }

  async deleteAdTemplate(id: string): Promise<boolean> {
    const index = this.adTemplates.findIndex(template => template.id === id);
    if (index === -1) return false;
    
    this.adTemplates.splice(index, 1);
    return true;
  }

  // Campaigns
  async getCampaigns(): Promise<Campaign[]> {
    try {
      // Try to get real campaigns from Supabase first
      const { supabaseDb } = await import('./supabaseDatabase');
      const realCampaigns = await supabaseDb.getCampaigns();

      if (realCampaigns.length > 0) {
        console.log(`📊 Loaded ${realCampaigns.length} real campaigns from database`);
        return realCampaigns;
      }

      // If no real campaigns, trigger automatic data loading
      const { automaticDataLoader } = await import('./automaticDataLoader');
      const loadResult = await automaticDataLoader.loadAllData();

      if (loadResult.success && loadResult.campaignsLoaded > 0) {
        // Try again to get the newly loaded campaigns
        const newCampaigns = await supabaseDb.getCampaigns();
        console.log(`📊 Loaded ${newCampaigns.length} campaigns after automatic import`);
        return newCampaigns;
      }

      // Fallback to mock data only if everything else fails
      console.log('⚠️ Using mock campaign data as fallback');
      return this.campaigns;
    } catch (error) {
      console.error('❌ Error loading campaigns, using mock data:', error);
      return this.campaigns;
    }
  }

  async getCampaignById(id: string): Promise<Campaign | null> {
    return this.campaigns.find(campaign => campaign.id === id) || null;
  }

  async createCampaign(campaign: Omit<Campaign, 'id' | 'created_at' | 'updated_at'>): Promise<Campaign> {
    const newCampaign: Campaign = {
      ...campaign,
      id: `campaign-${Date.now()}`,
      created_at: new Date(),
      updated_at: new Date()
    };
    this.campaigns.push(newCampaign);
    return newCampaign;
  }

  // Leads
  async getLeads(): Promise<Lead[]> {
    return this.leads;
  }

  async getLeadById(id: string): Promise<Lead | null> {
    return this.leads.find(lead => lead.id === id) || null;
  }

  async getLeadsByCampaign(campaignId: string): Promise<Lead[]> {
    return this.leads.filter(lead => lead.campaign_id === campaignId);
  }

  async createLead(lead: Omit<Lead, 'id' | 'created_at' | 'updated_at'>): Promise<Lead> {
    const newLead: Lead = {
      ...lead,
      id: `lead-${Date.now()}`,
      created_at: new Date(),
      updated_at: new Date()
    };
    this.leads.push(newLead);

    // 🔥 AUTOMATIC NEW LEAD CALLING - Trigger VAPI call immediately for new leads
    try {
      console.log(`🆕 NEW LEAD CREATED: ${newLead.name} - Triggering automatic VAPI call`);

      // Call the backend to initiate VAPI call
      const response = await fetch('http://localhost:5000/api/vapi/call-lead', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_token')}`
        },
        body: JSON.stringify({
          phone: newLead.phone,
          name: newLead.name,
          leadId: newLead.id,
          source: newLead.source,
          service: newLead.service,
          message: newLead.message
        })
      });

      if (response.ok) {
        const callResult = await response.json();
        console.log(`✅ Automatic call initiated: ${callResult.call_id}`);

        // Update lead with call information
        newLead.vapi_call_id = callResult.call_id;
        newLead.call_status = 'initiated';
        newLead.last_contact_attempt = new Date();
      } else {
        console.error('❌ Failed to initiate automatic call:', await response.text());
      }
    } catch (error) {
      console.error('❌ Error triggering automatic call for new lead:', error);
      // Don't fail lead creation if call trigger fails
    }

    return newLead;
  }

  async updateLead(id: string, updates: Partial<Lead>): Promise<Lead | null> {
    const index = this.leads.findIndex(lead => lead.id === id);
    if (index === -1) return null;
    
    this.leads[index] = {
      ...this.leads[index],
      ...updates,
      updated_at: new Date()
    };
    return this.leads[index];
  }
}

// Export singleton instance
export const db = new MockDatabaseService();
