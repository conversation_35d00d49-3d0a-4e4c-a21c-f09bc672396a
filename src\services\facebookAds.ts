// Facebook Ads API Integration Service
import { AdTemplate, Campaign } from '../types/database';

export interface FacebookAdAccount {
  id: string;
  name: string;
  account_status: number;
  currency: string;
  timezone_name: string;
}

export interface FacebookCampaignData {
  name: string;
  objective: string;
  status: string;
  daily_budget?: number;
  lifetime_budget?: number;
  start_time?: string;
  end_time?: string;
}

export interface FacebookAdSetData {
  name: string;
  campaign_id: string;
  targeting: {
    geo_locations: {
      location_types: string[];
      custom_locations?: Array<{
        latitude: number;
        longitude: number;
        radius: number;
        distance_unit: string;
      }>;
    };
    age_min?: number;
    age_max?: number;
    genders?: number[];
    interests?: Array<{ id: string; name: string }>;
    behaviors?: Array<{ id: string; name: string }>;
    custom_audiences?: string[];
    excluded_custom_audiences?: string[];
  };
  billing_event: string;
  optimization_goal: string;
  bid_amount?: number;
  daily_budget?: number;
  status: string;
}

export interface FacebookAdCreativeData {
  name: string;
  object_story_spec: {
    page_id: string;
    link_data: {
      link: string;
      message: string;
      name: string;
      description: string;
      call_to_action: {
        type: string;
        value: {
          link: string;
        };
      };
      image_hash?: string;
      video_id?: string;
    };
  };
}

export interface FacebookAdData {
  name: string;
  adset_id: string;
  creative: {
    creative_id: string;
  };
  status: string;
}

export class FacebookAdsService {
  private accessToken: string;
  private apiVersion: string = 'v23.0';
  private baseUrl: string = 'https://graph.facebook.com';

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  // Helper method to make API requests
  private async makeRequest(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET', data?: any) {
    const url = `${this.baseUrl}/${this.apiVersion}/${endpoint}`;
    const options: RequestInit = {
      method,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(`Facebook API Error: ${result.error?.message || 'Unknown error'}`);
      }
      
      return result;
    } catch (error) {
      console.error('Facebook API request failed:', error);
      throw error;
    }
  }

  // Get user's ad accounts
  async getAdAccounts(): Promise<FacebookAdAccount[]> {
    const response = await this.makeRequest('me/adaccounts', 'GET');
    return response.data || [];
  }

  // Create a campaign from template
  async createCampaignFromTemplate(
    template: AdTemplate,
    adAccountId: string,
    pageId: string,
    customizations?: {
      budget?: number;
      targeting?: Partial<FacebookAdSetData['targeting']>;
      creative?: Partial<FacebookAdCreativeData>;
    }
  ): Promise<{ campaignId: string; adSetId: string; adId: string }> {
    try {
      // 1. Create Campaign
      const campaignData: FacebookCampaignData = {
        name: `${template.name} - ${new Date().toLocaleDateString()}`,
        objective: 'LEAD_GENERATION',
        status: 'PAUSED', // Start paused for review
        daily_budget: (customizations?.budget || template.budget_range.suggested) * 100, // Convert to cents
      };

      const campaign = await this.makeRequest(`${adAccountId}/campaigns`, 'POST', campaignData);

      // 2. Create Ad Set
      const adSetData: FacebookAdSetData = {
        name: `${template.name} - AdSet`,
        campaign_id: campaign.id,
        targeting: this.buildTargetingFromTemplate(template, customizations?.targeting),
        billing_event: 'IMPRESSIONS',
        optimization_goal: 'LEAD_GENERATION',
        daily_budget: (customizations?.budget || template.budget_range.suggested) * 100,
        status: 'PAUSED',
      };

      const adSet = await this.makeRequest(`${adAccountId}/adsets`, 'POST', adSetData);

      // 3. Create Ad Creative
      const creativeData: FacebookAdCreativeData = {
        name: `${template.name} - Creative`,
        object_story_spec: {
          page_id: pageId,
          link_data: {
            link: customizations?.creative?.object_story_spec?.link_data?.link || 'https://example.com/contact',
            message: template.creative.primary_text,
            name: template.creative.headline,
            description: template.creative.description,
            call_to_action: {
              type: this.mapCallToActionType(template.creative.call_to_action),
              value: {
                link: customizations?.creative?.object_story_spec?.link_data?.link || 'https://example.com/contact',
              },
            },
          },
        },
      };

      const creative = await this.makeRequest(`${adAccountId}/adcreatives`, 'POST', creativeData);

      // 4. Create Ad
      const adData: FacebookAdData = {
        name: `${template.name} - Ad`,
        adset_id: adSet.id,
        creative: {
          creative_id: creative.id,
        },
        status: 'PAUSED',
      };

      const ad = await this.makeRequest(`${adAccountId}/ads`, 'POST', adData);

      return {
        campaignId: campaign.id,
        adSetId: adSet.id,
        adId: ad.id,
      };
    } catch (error) {
      console.error('Error creating Facebook campaign:', error);
      throw error;
    }
  }

  // Build targeting object from template
  private buildTargetingFromTemplate(
    template: AdTemplate,
    customTargeting?: Partial<FacebookAdSetData['targeting']>
  ): FacebookAdSetData['targeting'] {
    const targeting: FacebookAdSetData['targeting'] = {
      geo_locations: {
        location_types: ['home'],
        custom_locations: [{
          latitude: 40.7128, // Default to NYC, should be customized
          longitude: -74.0060,
          radius: parseInt(template.targeting.location_radius.split('_')[0]),
          distance_unit: 'mile',
        }],
      },
      age_min: template.targeting.age_range.min,
      age_max: template.targeting.age_range.max,
      genders: [1, 2], // All genders
    };

    // Add interests if available
    if (template.targeting.interests.length > 0) {
      targeting.interests = template.targeting.interests.map(interest => ({
        id: this.mapInterestToFacebookId(interest),
        name: interest,
      }));
    }

    // Merge with custom targeting
    if (customTargeting) {
      Object.assign(targeting, customTargeting);
    }

    return targeting;
  }

  // Map call to action types to Facebook's format
  private mapCallToActionType(cta: string): string {
    const ctaMap: { [key: string]: string } = {
      'Get Free Estimate': 'LEARN_MORE',
      'Book Now': 'BOOK_TRAVEL',
      'Request Quote': 'LEARN_MORE',
      'Learn More': 'LEARN_MORE',
      'Contact Us': 'CONTACT_US',
    };
    return ctaMap[cta] || 'LEARN_MORE';
  }

  // Map interests to Facebook interest IDs (simplified - in real implementation, use Facebook's targeting API)
  private mapInterestToFacebookId(interest: string): string {
    const interestMap: { [key: string]: string } = {
      'home_improvement': '6003107902433',
      'property_maintenance': '6003107902434',
      'homeownership': '6003107902435',
      'diy': '6003107902436',
      'property_value': '6003107902437',
      'business_owner': '6003107902438',
      'property_management': '6003107902439',
      'commercial_real_estate': '6003107902440',
    };
    return interestMap[interest] || '6003107902433'; // Default to home improvement
  }

  // Get campaign performance metrics
  async getCampaignMetrics(campaignId: string): Promise<any> {
    const fields = [
      'impressions',
      'clicks',
      'ctr',
      'cpc',
      'spend',
      'actions',
      'cost_per_action_type',
    ].join(',');

    return await this.makeRequest(`${campaignId}/insights?fields=${fields}`);
  }

  // Update campaign status
  async updateCampaignStatus(campaignId: string, status: 'ACTIVE' | 'PAUSED'): Promise<void> {
    await this.makeRequest(`${campaignId}`, 'POST', { status });
  }

  // Delete campaign
  async deleteCampaign(campaignId: string): Promise<void> {
    await this.makeRequest(`${campaignId}`, 'DELETE');
  }
}

// Mock Facebook Ads Service for development/testing
export class MockFacebookAdsService extends FacebookAdsService {
  constructor() {
    super('mock_token');
  }

  async getAdAccounts(): Promise<FacebookAdAccount[]> {
    return [
      {
        id: 'act_123456789',
        name: 'Test Ad Account',
        account_status: 1,
        currency: 'USD',
        timezone_name: 'America/New_York',
      },
    ];
  }

  async createCampaignFromTemplate(): Promise<{ campaignId: string; adSetId: string; adId: string }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    return {
      campaignId: `campaign_${Date.now()}`,
      adSetId: `adset_${Date.now()}`,
      adId: `ad_${Date.now()}`,
    };
  }

  async getCampaignMetrics(): Promise<any> {
    return {
      data: [{
        impressions: Math.floor(Math.random() * 10000),
        clicks: Math.floor(Math.random() * 500),
        ctr: (Math.random() * 5).toFixed(2),
        cpc: (Math.random() * 3 + 0.5).toFixed(2),
        spend: (Math.random() * 200).toFixed(2),
      }],
    };
  }

  async updateCampaignStatus(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  async deleteCampaign(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Export the appropriate service based on environment and Facebook connection status
const createFacebookAdsService = () => {
  // Always try to use real service if Facebook is connected
  try {
    const { facebookIntegration } = require('./facebookIntegration');
    const status = facebookIntegration.getIntegrationStatus();

    if (status.isConnected && status.accessToken) {
      console.log('✅ Using real Facebook Ads Service');
      return new FacebookAdsService(status.accessToken);
    }
  } catch (error) {
    console.warn('⚠️ Could not check Facebook integration status:', error);
  }

  // Fallback to mock service for development or when not connected
  console.log('🔧 Using Mock Facebook Ads Service');
  return new MockFacebookAdsService();
};

export const facebookAdsService = createFacebookAdsService();
