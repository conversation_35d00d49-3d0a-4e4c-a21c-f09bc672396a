import React, { useEffect, useState, useRef } from 'react';

const CustomCursor: React.FC = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [targetPosition, setTargetPosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);
  const animationRef = useRef<number>();

  useEffect(() => {
    const updateTargetPosition = (e: MouseEvent) => {
      setTargetPosition({ x: e.clientX, y: e.clientY });
    };

    const handleMouseOver = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'BUTTON' || target.closest('button') || target.style.cursor === 'pointer') {
        setIsHovering(true);
      } else {
        setIsHovering(false);
      }
    };

    document.addEventListener('mousemove', updateTargetPosition);
    document.addEventListener('mouseover', handleMouseOver);

    return () => {
      document.removeEventListener('mousemove', updateTargetPosition);
      document.removeEventListener('mouseover', handleMouseOver);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  useEffect(() => {
    const smoothMove = () => {
      setPosition(prevPosition => {
        const dx = targetPosition.x - prevPosition.x;
        const dy = targetPosition.y - prevPosition.y;
        
        // Smooth interpolation with easing
        const ease = 0.15;
        const newX = prevPosition.x + dx * ease;
        const newY = prevPosition.y + dy * ease;
        
        return { x: newX, y: newY };
      });
      
      animationRef.current = requestAnimationFrame(smoothMove);
    };

    animationRef.current = requestAnimationFrame(smoothMove);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [targetPosition]);

  return (
    <>
      {/* Main cursor */}
      <div
        className={`fixed pointer-events-none z-50 transition-transform duration-200 ease-out ${
          isHovering ? 'scale-150' : 'scale-100'
        }`}
        style={{
          left: position.x - 10,
          top: position.y - 10,
          transform: 'translate(-50%, -50%)'
        }}
      >
        <div className={`w-5 h-5 border-2 rounded-full transition-colors duration-200 ${
          isHovering ? 'border-red-500 bg-red-500/20' : 'border-white/50'
        }`} />
      </div>
      
      {/* Trailing effect with more delay */}
      <div
        className="fixed pointer-events-none z-40 w-2 h-2 bg-red-500/30 rounded-full transition-all duration-700 ease-out"
        style={{
          left: position.x - 4,
          top: position.y - 4,
          transform: 'translate(-50%, -50%)'
        }}
      />
    </>
  );
};

export default CustomCursor;