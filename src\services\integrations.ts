// Integration Hub for FSM/CRM Systems
import { Lead } from '../types/database';

export interface Integration {
  id: string;
  name: string;
  type: 'crm' | 'fsm' | 'email' | 'sms' | 'calendar';
  provider: string;
  status: 'connected' | 'disconnected' | 'error';
  config: {
    apiKey?: string;
    apiUrl?: string;
    webhookUrl?: string;
    fieldMappings: Record<string, string>;
    syncSettings: {
      autoSync: boolean;
      syncFrequency: 'realtime' | 'hourly' | 'daily';
      syncDirection: 'export' | 'import' | 'bidirectional';
    };
  };
  lastSync?: Date;
  syncStats: {
    totalSynced: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastError?: string;
  };
}

export interface IntegrationProvider {
  id: string;
  name: string;
  type: 'crm' | 'fsm' | 'email' | 'sms' | 'calendar';
  description: string;
  logo: string;
  features: string[];
  authType: 'api_key' | 'oauth' | 'webhook';
  fieldMappings: {
    required: string[];
    optional: string[];
    custom: boolean;
  };
}

export interface SyncResult {
  success: boolean;
  recordsProcessed: number;
  recordsSuccessful: number;
  recordsFailed: number;
  errors: string[];
  duration: number;
}

export class IntegrationsService {
  private integrations: Integration[] = [];
  private providers: IntegrationProvider[] = [];

  constructor() {
    this.initializeProviders();
  }

  // Initialize supported integration providers
  private initializeProviders() {
    this.providers = [
      {
        id: 'jobber',
        name: 'Jobber',
        type: 'fsm',
        description: 'Field service management platform for home service businesses',
        logo: '/logos/jobber.png',
        features: ['Lead Management', 'Job Scheduling', 'Customer Database', 'Invoicing'],
        authType: 'api_key',
        fieldMappings: {
          required: ['name', 'phone', 'email', 'address'],
          optional: ['notes', 'service_type', 'urgency'],
          custom: true
        }
      },
      {
        id: 'housecallpro',
        name: 'HousecallPro',
        type: 'fsm',
        description: 'All-in-one software for home service professionals',
        logo: '/logos/housecallpro.png',
        features: ['Customer Management', 'Scheduling', 'Estimates', 'Payments'],
        authType: 'api_key',
        fieldMappings: {
          required: ['name', 'phone'],
          optional: ['email', 'address', 'notes', 'service_interest'],
          custom: true
        }
      },
      {
        id: 'quoteiq',
        name: 'QuoteIQ',
        type: 'crm',
        description: 'CRM and quoting software for contractors',
        logo: '/logos/quoteiq.png',
        features: ['Lead Tracking', 'Quote Generation', 'Customer Portal', 'Analytics'],
        authType: 'api_key',
        fieldMappings: {
          required: ['name', 'phone', 'service_type'],
          optional: ['email', 'address', 'budget_range', 'notes'],
          custom: false
        }
      },
      {
        id: 'servicetitan',
        name: 'ServiceTitan',
        type: 'fsm',
        description: 'Complete business software for the trades',
        logo: '/logos/servicetitan.png',
        features: ['CRM', 'Dispatch', 'Inventory', 'Marketing', 'Reporting'],
        authType: 'oauth',
        fieldMappings: {
          required: ['name', 'phone'],
          optional: ['email', 'address', 'service_interest', 'notes'],
          custom: true
        }
      },
      {
        id: 'salesforce',
        name: 'Salesforce',
        type: 'crm',
        description: 'World\'s #1 CRM platform',
        logo: '/logos/salesforce.png',
        features: ['Lead Management', 'Opportunity Tracking', 'Automation', 'Analytics'],
        authType: 'oauth',
        fieldMappings: {
          required: ['name', 'phone', 'email'],
          optional: ['address', 'service_interest', 'budget_range', 'notes'],
          custom: true
        }
      },
      {
        id: 'hubspot',
        name: 'HubSpot',
        type: 'crm',
        description: 'Inbound marketing, sales, and service platform',
        logo: '/logos/hubspot.png',
        features: ['Contact Management', 'Deal Pipeline', 'Email Marketing', 'Analytics'],
        authType: 'oauth',
        fieldMappings: {
          required: ['name', 'email'],
          optional: ['phone', 'address', 'service_interest', 'notes'],
          custom: true
        }
      }
    ];
  }

  // Get all available providers
  getProviders(): IntegrationProvider[] {
    return this.providers;
  }

  // Get providers by type
  getProvidersByType(type: IntegrationProvider['type']): IntegrationProvider[] {
    return this.providers.filter(p => p.type === type);
  }

  // Get all integrations
  getIntegrations(): Integration[] {
    return this.integrations;
  }

  // Get connected integrations
  getConnectedIntegrations(): Integration[] {
    return this.integrations.filter(i => i.status === 'connected');
  }

  // Create new integration
  async createIntegration(config: {
    providerId: string;
    name: string;
    apiKey?: string;
    apiUrl?: string;
    fieldMappings?: Record<string, string>;
    syncSettings?: Partial<Integration['config']['syncSettings']>;
  }): Promise<Integration> {
    const provider = this.providers.find(p => p.id === config.providerId);
    if (!provider) {
      throw new Error('Provider not found');
    }

    const integration: Integration = {
      id: `integration-${Date.now()}`,
      name: config.name,
      type: provider.type,
      provider: provider.id,
      status: 'disconnected',
      config: {
        apiKey: config.apiKey,
        apiUrl: config.apiUrl,
        fieldMappings: config.fieldMappings || this.getDefaultFieldMappings(provider.id),
        syncSettings: {
          autoSync: true,
          syncFrequency: 'realtime',
          syncDirection: 'export',
          ...config.syncSettings
        }
      },
      syncStats: {
        totalSynced: 0,
        successfulSyncs: 0,
        failedSyncs: 0
      }
    };

    // Test connection
    try {
      await this.testConnection(integration);
      integration.status = 'connected';
    } catch (error) {
      integration.status = 'error';
      integration.syncStats.lastError = error instanceof Error ? error.message : 'Connection failed';
    }

    this.integrations.push(integration);
    return integration;
  }

  // Test integration connection
  async testConnection(integration: Integration): Promise<boolean> {
    console.log(`Testing connection to ${integration.provider}...`);
    
    // Mock connection test - in real implementation, this would make actual API calls
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate random success/failure for demo
    const success = Math.random() > 0.2; // 80% success rate
    
    if (!success) {
      throw new Error('Invalid API credentials or connection timeout');
    }
    
    console.log(`Connection to ${integration.provider} successful`);
    return true;
  }

  // Sync lead to integration
  async syncLead(leadId: string, integrationId: string): Promise<SyncResult> {
    const integration = this.integrations.find(i => i.id === integrationId);
    if (!integration) {
      throw new Error('Integration not found');
    }

    if (integration.status !== 'connected') {
      throw new Error('Integration is not connected');
    }

    const startTime = Date.now();
    
    try {
      // Mock sync process
      console.log(`Syncing lead ${leadId} to ${integration.provider}...`);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Simulate sync result
      const success = Math.random() > 0.1; // 90% success rate
      
      if (success) {
        integration.syncStats.totalSynced++;
        integration.syncStats.successfulSyncs++;
        integration.lastSync = new Date();
        
        return {
          success: true,
          recordsProcessed: 1,
          recordsSuccessful: 1,
          recordsFailed: 0,
          errors: [],
          duration: Date.now() - startTime
        };
      } else {
        integration.syncStats.totalSynced++;
        integration.syncStats.failedSyncs++;
        
        const error = 'API rate limit exceeded';
        integration.syncStats.lastError = error;
        
        return {
          success: false,
          recordsProcessed: 1,
          recordsSuccessful: 0,
          recordsFailed: 1,
          errors: [error],
          duration: Date.now() - startTime
        };
      }
    } catch (error) {
      integration.syncStats.totalSynced++;
      integration.syncStats.failedSyncs++;
      integration.syncStats.lastError = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        success: false,
        recordsProcessed: 1,
        recordsSuccessful: 0,
        recordsFailed: 1,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        duration: Date.now() - startTime
      };
    }
  }

  // Bulk sync leads
  async bulkSyncLeads(leadIds: string[], integrationId: string): Promise<SyncResult> {
    const integration = this.integrations.find(i => i.id === integrationId);
    if (!integration) {
      throw new Error('Integration not found');
    }

    const startTime = Date.now();
    const results: SyncResult[] = [];
    
    for (const leadId of leadIds) {
      try {
        const result = await this.syncLead(leadId, integrationId);
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          recordsProcessed: 1,
          recordsSuccessful: 0,
          recordsFailed: 1,
          errors: [error instanceof Error ? error.message : 'Unknown error'],
          duration: 0
        });
      }
    }

    // Aggregate results
    const aggregated: SyncResult = {
      success: results.every(r => r.success),
      recordsProcessed: results.length,
      recordsSuccessful: results.filter(r => r.success).length,
      recordsFailed: results.filter(r => !r.success).length,
      errors: results.flatMap(r => r.errors),
      duration: Date.now() - startTime
    };

    return aggregated;
  }

  // Get default field mappings for a provider
  private getDefaultFieldMappings(providerId: string): Record<string, string> {
    const mappings: Record<string, Record<string, string>> = {
      jobber: {
        'name': 'first_name',
        'phone': 'phone_number',
        'email': 'email_address',
        'address': 'property_address',
        'service_interest': 'service_type',
        'notes': 'description'
      },
      housecallpro: {
        'name': 'customer_name',
        'phone': 'phone',
        'email': 'email',
        'address': 'address',
        'service_interest': 'job_type',
        'notes': 'notes'
      },
      quoteiq: {
        'name': 'contact_name',
        'phone': 'phone_number',
        'email': 'email_address',
        'service_interest': 'service_category',
        'budget_range': 'estimated_value',
        'notes': 'comments'
      },
      servicetitan: {
        'name': 'customer_name',
        'phone': 'phone_number',
        'email': 'email',
        'address': 'service_address',
        'service_interest': 'business_unit',
        'notes': 'summary'
      },
      salesforce: {
        'name': 'Name',
        'phone': 'Phone',
        'email': 'Email',
        'address': 'Address',
        'service_interest': 'Lead_Source_Detail__c',
        'notes': 'Description'
      },
      hubspot: {
        'name': 'firstname',
        'phone': 'phone',
        'email': 'email',
        'address': 'address',
        'service_interest': 'hs_lead_status',
        'notes': 'notes_last_contacted'
      }
    };

    return mappings[providerId] || {};
  }

  // Update integration settings
  async updateIntegration(integrationId: string, updates: Partial<Integration>): Promise<Integration> {
    const integration = this.integrations.find(i => i.id === integrationId);
    if (!integration) {
      throw new Error('Integration not found');
    }

    Object.assign(integration, updates);
    
    // Re-test connection if config changed
    if (updates.config) {
      try {
        await this.testConnection(integration);
        integration.status = 'connected';
      } catch (error) {
        integration.status = 'error';
        integration.syncStats.lastError = error instanceof Error ? error.message : 'Connection failed';
      }
    }

    return integration;
  }

  // Delete integration
  async deleteIntegration(integrationId: string): Promise<void> {
    const index = this.integrations.findIndex(i => i.id === integrationId);
    if (index === -1) {
      throw new Error('Integration not found');
    }

    this.integrations.splice(index, 1);
  }

  // Get integration statistics
  getIntegrationStats(): {
    totalIntegrations: number;
    connectedIntegrations: number;
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastSyncDate?: Date;
  } {
    const connected = this.getConnectedIntegrations();
    const totalSyncs = this.integrations.reduce((sum, i) => sum + i.syncStats.totalSynced, 0);
    const successfulSyncs = this.integrations.reduce((sum, i) => sum + i.syncStats.successfulSyncs, 0);
    const failedSyncs = this.integrations.reduce((sum, i) => sum + i.syncStats.failedSyncs, 0);
    
    const lastSyncDates = this.integrations
      .map(i => i.lastSync)
      .filter(date => date)
      .sort((a, b) => b!.getTime() - a!.getTime());

    return {
      totalIntegrations: this.integrations.length,
      connectedIntegrations: connected.length,
      totalSyncs,
      successfulSyncs,
      failedSyncs,
      lastSyncDate: lastSyncDates[0]
    };
  }

  // Auto-sync lead to all connected integrations
  async autoSyncLead(lead: Lead): Promise<SyncResult[]> {
    const connectedIntegrations = this.getConnectedIntegrations()
      .filter(i => i.config.syncSettings.autoSync);

    const results: SyncResult[] = [];

    for (const integration of connectedIntegrations) {
      try {
        const result = await this.syncLead(lead.id, integration.id);
        results.push(result);
      } catch (error) {
        console.error(`Auto-sync failed for ${integration.provider}:`, error);
      }
    }

    return results;
  }
}

// Export singleton instance
export const integrationsService = new IntegrationsService();
