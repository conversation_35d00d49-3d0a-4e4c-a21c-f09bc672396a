/**
 * Admin Authentication Service for PressureMax
 * Handles role-based access control and admin-specific operations
 */

import { User } from '../types/database';

export interface AdminPermissions {
  template_create: boolean;
  template_edit: boolean;
  template_delete: boolean;
  template_publish: boolean;
  user_manage: boolean;
  analytics_view: boolean;
  system_settings: boolean;
}

export class AdminAuthService {
  private static instance: AdminAuthService;
  
  static getInstance(): AdminAuthService {
    if (!AdminAuthService.instance) {
      AdminAuthService.instance = new AdminAuthService();
    }
    return AdminAuthService.instance;
  }

  /**
   * Check if user has admin role
   */
  isAdmin(user: User | null): boolean {
    return user?.role === 'admin';
  }

  /**
   * Check if user has specific permission
   */
  hasPermission(user: User | null, permission: keyof AdminPermissions): boolean {
    if (!user) return false;
    
    // Admins have all permissions by default
    if (user.role === 'admin') return true;
    
    // Check specific permission
    return user.permissions?.includes(permission) || false;
  }

  /**
   * Get all permissions for a user
   */
  getUserPermissions(user: User | null): AdminPermissions {
    const defaultPermissions: AdminPermissions = {
      template_create: false,
      template_edit: false,
      template_delete: false,
      template_publish: false,
      user_manage: false,
      analytics_view: false,
      system_settings: false
    };

    if (!user) return defaultPermissions;

    // Admins get all permissions
    if (user.role === 'admin') {
      return {
        template_create: true,
        template_edit: true,
        template_delete: true,
        template_publish: true,
        user_manage: true,
        analytics_view: true,
        system_settings: true
      };
    }

    // Regular users get limited permissions based on their permission array
    return {
      template_create: user.permissions?.includes('template_create') || false,
      template_edit: user.permissions?.includes('template_edit') || false,
      template_delete: user.permissions?.includes('template_delete') || false,
      template_publish: user.permissions?.includes('template_publish') || false,
      user_manage: user.permissions?.includes('user_manage') || false,
      analytics_view: user.permissions?.includes('analytics_view') || false,
      system_settings: user.permissions?.includes('system_settings') || false
    };
  }

  /**
   * Create admin user (for initial setup)
   */
  async createAdminUser(userData: {
    email: string;
    password: string;
    name: string;
    company_name: string;
  }): Promise<User> {
    const adminUser: User = {
      id: `admin_${Date.now()}`,
      email: userData.email,
      name: userData.name,
      company_name: userData.company_name,
      role: 'admin',
      permissions: [
        'template_create',
        'template_edit', 
        'template_delete',
        'template_publish',
        'user_manage',
        'analytics_view',
        'system_settings'
      ],
      plan: 'scale', // Admins get highest plan
      subscription_status: 'active',
      timezone: 'America/New_York',
      business_hours: {
        start: '09:00',
        end: '17:00',
        days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
      },
      created_at: new Date(),
      updated_at: new Date()
    };

    // In production, this would save to database
    console.log('Admin user created:', adminUser);
    return adminUser;
  }

  /**
   * Promote user to admin
   */
  async promoteToAdmin(userId: string): Promise<boolean> {
    try {
      // In production, update user in database
      console.log(`Promoting user ${userId} to admin`);
      return true;
    } catch (error) {
      console.error('Error promoting user to admin:', error);
      return false;
    }
  }

  /**
   * Demote admin to regular user
   */
  async demoteFromAdmin(userId: string): Promise<boolean> {
    try {
      // In production, update user in database
      console.log(`Demoting user ${userId} from admin`);
      return true;
    } catch (error) {
      console.error('Error demoting user from admin:', error);
      return false;
    }
  }

  /**
   * Get admin dashboard stats
   */
  async getAdminStats(): Promise<{
    totalUsers: number;
    totalTemplates: number;
    totalCampaigns: number;
    totalLeads: number;
    monthlyRevenue: number;
    activeSubscriptions: number;
  }> {
    // Mock stats - in production, fetch from database
    return {
      totalUsers: 1247,
      totalTemplates: 45,
      totalCampaigns: 3892,
      totalLeads: 15634,
      monthlyRevenue: 24750,
      activeSubscriptions: 1189
    };
  }

  /**
   * Validate admin session
   */
  async validateAdminSession(token: string): Promise<User | null> {
    try {
      // In production, validate JWT token and check admin role
      const response = await fetch('/api/auth/validate-admin', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const user = await response.json();
        return user.role === 'admin' ? user : null;
      }
      
      return null;
    } catch (error) {
      console.error('Error validating admin session:', error);
      return null;
    }
  }

  /**
   * Admin login with enhanced security
   */
  async adminLogin(email: string, password: string, mfaCode?: string): Promise<{
    success: boolean;
    user?: User;
    token?: string;
    requiresMFA?: boolean;
    error?: string;
  }> {
    try {
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email,
          password,
          mfaCode
        })
      });

      const result = await response.json();

      if (response.ok && result.user?.role === 'admin') {
        return {
          success: true,
          user: result.user,
          token: result.token
        };
      }

      return {
        success: false,
        error: result.error || 'Invalid admin credentials'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Login failed. Please try again.'
      };
    }
  }

  /**
   * Check if current environment allows admin access
   */
  isAdminAccessAllowed(): boolean {
    // In production, you might want to restrict admin access to certain IPs or environments
    const allowedEnvironments = ['development', 'staging', 'production'];
    const currentEnv = process.env.NODE_ENV || 'development';
    
    return allowedEnvironments.includes(currentEnv);
  }

  /**
   * Log admin action for audit trail
   */
  async logAdminAction(userId: string, action: string, details: any): Promise<void> {
    const logEntry = {
      userId,
      action,
      details,
      timestamp: new Date(),
      ip: 'unknown', // In production, get from request
      userAgent: navigator.userAgent
    };

    console.log('Admin action logged:', logEntry);
    
    // In production, save to audit log database
    try {
      await fetch('/api/admin/audit-log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_admin_token')}`
        },
        body: JSON.stringify(logEntry)
      });
    } catch (error) {
      console.error('Failed to log admin action:', error);
    }
  }
}

// Export singleton instance
export const adminAuth = AdminAuthService.getInstance();

// Admin route guard hook
export const useAdminAuth = () => {
  const checkAdminAccess = (user: User | null): boolean => {
    return adminAuth.isAdmin(user) && adminAuth.isAdminAccessAllowed();
  };

  const requirePermission = (user: User | null, permission: keyof AdminPermissions): boolean => {
    return adminAuth.hasPermission(user, permission);
  };

  return {
    isAdmin: adminAuth.isAdmin,
    hasPermission: adminAuth.hasPermission,
    getUserPermissions: adminAuth.getUserPermissions,
    checkAdminAccess,
    requirePermission,
    logAction: adminAuth.logAdminAction
  };
};
