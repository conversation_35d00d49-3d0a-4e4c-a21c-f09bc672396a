/**
 * Lead Management Dashboard for PressureMax
 * CRM-style interface with call status tracking, recordings, and manual actions
 */

import React, { useState, useEffect } from 'react';
import { 
  Phone, 
  PhoneCall, 
  Calendar, 
  Clock, 
  User, 
  MapPin, 
  DollarSign, 
  Play, 
  Pause, 
  Download, 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Filter,
  Search,
  MoreVertical,
  Edit,
  Trash2
} from 'lucide-react';
import { Lead } from '../types/database';
import { db } from '../services/database';
import { BulkCallCampaign } from './BulkCallCampaign';

interface CallRecord {
  id: string;
  lead_id: string;
  call_id: string;
  status: 'initiated' | 'in_progress' | 'completed' | 'failed' | 'no_answer';
  started_at?: Date;
  ended_at?: Date;
  duration?: number;
  transcript?: string;
  summary?: string;
  outcome?: 'appointment_scheduled' | 'callback_requested' | 'not_interested' | 'no_answer';
  cost?: number;
  recording_url?: string;
}

interface LeadManagementDashboardProps {
  className?: string;
}

export const LeadManagementDashboard: React.FC<LeadManagementDashboardProps> = ({
  className = ''
}) => {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [callRecords, setCallRecords] = useState<CallRecord[]>([]);
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isBulkCallModalOpen, setIsBulkCallModalOpen] = useState(false);

  useEffect(() => {
    loadLeads();
    loadCallRecords();
  }, []);

  const loadLeads = async () => {
    try {
      const leadsData = await db.getLeads();
      setLeads(leadsData);
    } catch (error) {
      console.error('Error loading leads:', error);
    }
  };

  const loadCallRecords = async () => {
    try {
      // Load real call records from backend/VAPI
      // For now, start with empty array - records will populate as calls are made
      const callRecords: CallRecord[] = [];
      setCallRecords(callRecords);
      console.log('📞 Loaded call records');
    } catch (error) {
      console.error('❌ Error loading call records:', error);
    }
  };

  const initiateCall = async (lead: Lead) => {
    setIsLoading(true);
    try {
      const response = await fetch('http://localhost:5000/api/vapi/call-lead', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('pressuremax_token')}`
        },
        body: JSON.stringify({
          phone: lead.phone,
          name: lead.name,
          leadId: lead.id,
          source: lead.source,
          service: lead.service,
          message: lead.message
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Call initiated:', result);
        
        // Update lead status
        await db.updateLead(lead.id, {
          ...lead,
          call_status: 'initiated',
          vapi_call_id: result.call_id,
          last_contact_attempt: new Date()
        });
        
        loadLeads();
      } else {
        console.error('Failed to initiate call');
      }
    } catch (error) {
      console.error('Error initiating call:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getCallStatus = (lead: Lead) => {
    const callRecord = callRecords.find(record => record.lead_id === lead.id);
    return callRecord?.status || lead.call_status || 'not_called';
  };

  const getCallStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-400';
      case 'in_progress':
        return 'text-yellow-400';
      case 'failed':
      case 'no_answer':
        return 'text-red-400';
      case 'initiated':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  const getCallStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} />;
      case 'in_progress':
        return <Clock size={16} />;
      case 'failed':
      case 'no_answer':
        return <XCircle size={16} />;
      case 'initiated':
        return <AlertCircle size={16} />;
      default:
        return <Phone size={16} />;
    }
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.phone.includes(searchTerm) ||
                         lead.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (filterStatus === 'all') return matchesSearch;
    
    const callStatus = getCallStatus(lead);
    return matchesSearch && callStatus === filterStatus;
  });

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Lead Management</h2>
          <p className="text-gray-400">Manage leads, track calls, and schedule appointments</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsBulkCallModalOpen(true)}
            className="bg-gradient-to-r from-green-500 to-green-600 text-black px-4 py-2 font-bold hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 flex items-center space-x-2"
          >
            <PhoneCall size={16} />
            <span>Bulk Call Campaign</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-900 border border-gray-700 p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-gray-800 border border-gray-600 text-white pl-10 pr-3 py-2 focus:border-cyan-500 focus:outline-none"
                placeholder="Search leads by name, phone, or email..."
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-gray-800 border border-gray-600 text-white px-3 py-2 focus:border-cyan-500 focus:outline-none"
            >
              <option value="all">All Leads</option>
              <option value="not_called">Not Called</option>
              <option value="initiated">Call Initiated</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Call Completed</option>
              <option value="no_answer">No Answer</option>
              <option value="failed">Call Failed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gray-900 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Total Leads</h3>
          <p className="text-2xl font-bold text-white">{leads.length}</p>
        </div>
        <div className="bg-gray-900 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Calls Made</h3>
          <p className="text-2xl font-bold text-cyan-400">{callRecords.length}</p>
        </div>
        <div className="bg-gray-900 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Appointments</h3>
          <p className="text-2xl font-bold text-green-400">
            {callRecords.filter(r => r.outcome === 'appointment_scheduled').length}
          </p>
        </div>
        <div className="bg-gray-900 border border-gray-700 p-4">
          <h3 className="text-sm font-medium text-gray-400">Conversion Rate</h3>
          <p className="text-2xl font-bold text-yellow-400">
            {callRecords.length > 0 
              ? Math.round((callRecords.filter(r => r.outcome === 'appointment_scheduled').length / callRecords.length) * 100)
              : 0}%
          </p>
        </div>
      </div>

      {/* Leads Table */}
      <div className="bg-gray-900 border border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-800 border-b border-gray-700">
              <tr>
                <th className="text-left p-4 text-sm font-medium text-gray-300">Lead</th>
                <th className="text-left p-4 text-sm font-medium text-gray-300">Service</th>
                <th className="text-left p-4 text-sm font-medium text-gray-300">Call Status</th>
                <th className="text-left p-4 text-sm font-medium text-gray-300">Last Contact</th>
                <th className="text-left p-4 text-sm font-medium text-gray-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {filteredLeads.map((lead) => {
                const callRecord = callRecords.find(record => record.lead_id === lead.id);
                const callStatus = getCallStatus(lead);
                
                return (
                  <tr key={lead.id} className="hover:bg-gray-800 transition-colors">
                    <td className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-cyan-500 rounded-full flex items-center justify-center text-black font-bold">
                          {lead.name.charAt(0)}
                        </div>
                        <div>
                          <p className="font-medium text-white">{lead.name}</p>
                          <p className="text-sm text-gray-400">{lead.phone}</p>
                          <p className="text-xs text-gray-500">{lead.email}</p>
                        </div>
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div>
                        <p className="text-white">{lead.service}</p>
                        <p className="text-sm text-gray-400">{lead.source}</p>
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className={`flex items-center space-x-2 ${getCallStatusColor(callStatus)}`}>
                        {getCallStatusIcon(callStatus)}
                        <span className="text-sm font-medium capitalize">
                          {callStatus.replace('_', ' ')}
                        </span>
                      </div>
                      {callRecord && (
                        <div className="text-xs text-gray-500 mt-1">
                          {callRecord.duration && `${formatDuration(callRecord.duration)} • `}
                          {callRecord.outcome && callRecord.outcome.replace('_', ' ')}
                        </div>
                      )}
                    </td>
                    
                    <td className="p-4">
                      <div className="text-sm text-gray-400">
                        {lead.last_contact_attempt 
                          ? new Date(lead.last_contact_attempt).toLocaleDateString()
                          : 'Never'
                        }
                      </div>
                    </td>
                    
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => initiateCall(lead)}
                          disabled={isLoading || callStatus === 'in_progress'}
                          className="p-2 bg-green-600 hover:bg-green-700 text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Call Lead"
                        >
                          <Phone size={14} />
                        </button>
                        
                        {callRecord?.recording_url && (
                          <button
                            onClick={() => window.open(callRecord.recording_url, '_blank')}
                            className="p-2 bg-blue-600 hover:bg-blue-700 text-white transition-colors"
                            title="Play Recording"
                          >
                            <Play size={14} />
                          </button>
                        )}
                        
                        {callRecord?.transcript && (
                          <button
                            onClick={() => setSelectedLead(lead)}
                            className="p-2 bg-purple-600 hover:bg-purple-700 text-white transition-colors"
                            title="View Transcript"
                          >
                            <FileText size={14} />
                          </button>
                        )}
                        
                        <button
                          onClick={() => setSelectedLead(lead)}
                          className="p-2 bg-gray-600 hover:bg-gray-700 text-white transition-colors"
                          title="More Actions"
                        >
                          <MoreVertical size={14} />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {filteredLeads.length === 0 && (
        <div className="text-center py-12">
          <Phone className="mx-auto text-gray-500 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-400 mb-2">No leads found</h3>
          <p className="text-gray-500">
            {searchTerm || filterStatus !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'Leads will appear here when campaigns generate them'
            }
          </p>
        </div>
      )}

      {/* Bulk Call Campaign Modal */}
      <BulkCallCampaign
        isOpen={isBulkCallModalOpen}
        onClose={() => setIsBulkCallModalOpen(false)}
        preselectedLeads={filteredLeads}
      />
    </div>
  );
};
