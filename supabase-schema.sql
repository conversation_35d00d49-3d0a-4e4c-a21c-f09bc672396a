-- PressureMax Database Schema for Supabase
-- Run this in the Supabase SQL Editor to create all tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL,
    name TEXT NOT NULL,
    company_name TEXT NOT NULL,
    phone TEXT,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    permissions TEXT[] DEFAULT '{}',
    plan TEXT NOT NULL DEFAULT 'starter' CHECK (plan IN ('starter', 'growth', 'scale')),
    subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due')),
    facebook_access_token TEXT,
    facebook_ad_account_id TEXT,
    facebook_page_id TEXT,
    vapi_api_key TEXT,
    vapi_assistant_id TEXT,
    timezone TEXT NOT NULL DEFAULT 'UTC',
    business_hours JSONB NOT NULL DEFAULT '{"start": "09:00", "end": "17:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_login_at TIMESTAMPTZ
);

-- Create service_types table
CREATE TABLE IF NOT EXISTS public.service_types (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT NOT NULL,
    color TEXT NOT NULL,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create pressure_services table
CREATE TABLE IF NOT EXISTS public.pressure_services (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    service_type_id UUID NOT NULL REFERENCES service_types(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    typical_pricing TEXT NOT NULL,
    season_preference TEXT NOT NULL,
    equipment_needed TEXT[] NOT NULL DEFAULT '{}',
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create ad_templates table
CREATE TABLE IF NOT EXISTS public.ad_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    service_type_id UUID NOT NULL REFERENCES service_types(id) ON DELETE CASCADE,
    pressure_service_id UUID NOT NULL REFERENCES pressure_services(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    service TEXT NOT NULL,
    creative JSONB NOT NULL,
    targeting JSONB NOT NULL,
    budget_range JSONB NOT NULL,
    seasonal_timing TEXT[] NOT NULL DEFAULT '{}',
    target_customer TEXT NOT NULL,
    pricing_strategy TEXT NOT NULL,
    performance JSONB NOT NULL DEFAULT '{"ctr": "0%", "cpl": "$0", "conversions": 0, "total_spend": 0, "total_leads": 0, "last_updated": "2024-01-01T00:00:00Z"}',
    template_type TEXT NOT NULL DEFAULT 'custom' CHECK (template_type IN ('global', 'custom')),
    is_public BOOLEAN NOT NULL DEFAULT false,
    parent_template_id UUID REFERENCES ad_templates(id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'archived')),
    is_featured BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create campaigns table
CREATE TABLE IF NOT EXISTS public.campaigns (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    template_id UUID NOT NULL REFERENCES ad_templates(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    facebook_campaign_id TEXT,
    budget DECIMAL(10,2) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ,
    custom_creative JSONB,
    custom_targeting JSONB,
    metrics JSONB NOT NULL DEFAULT '{"impressions": 0, "clicks": 0, "ctr": 0, "cpc": 0, "cpl": 0, "leads_generated": 0, "spend": 0, "last_sync": "2024-01-01T00:00:00Z"}',
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'error')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    launched_at TIMESTAMPTZ,
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create leads table
CREATE TABLE IF NOT EXISTS public.leads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    source TEXT NOT NULL CHECK (source IN ('facebook', 'google', 'website', 'referral')),
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    service_interest TEXT NOT NULL,
    budget_range TEXT,
    urgency TEXT NOT NULL CHECK (urgency IN ('asap', 'this_week', 'this_month', 'next_month', 'just_browsing')),
    property_type TEXT NOT NULL CHECK (property_type IN ('residential', 'commercial')),
    score INTEGER NOT NULL DEFAULT 0 CHECK (score >= 0 AND score <= 100),
    quality TEXT NOT NULL DEFAULT 'cold' CHECK (quality IN ('hot', 'warm', 'cold')),
    vapi_call_id TEXT,
    call_status TEXT NOT NULL DEFAULT 'pending' CHECK (call_status IN ('pending', 'calling', 'completed', 'failed', 'no_answer')),
    call_attempts INTEGER NOT NULL DEFAULT 0,
    last_call_at TIMESTAMPTZ,
    next_call_at TIMESTAMPTZ,
    appointment_scheduled BOOLEAN NOT NULL DEFAULT false,
    appointment_date TIMESTAMPTZ,
    appointment_notes TEXT,
    status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'appointment', 'converted', 'lost')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_contacted_at TIMESTAMPTZ,
    notes TEXT NOT NULL DEFAULT '',
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);
CREATE INDEX IF NOT EXISTS idx_ad_templates_status ON ad_templates(status);
CREATE INDEX IF NOT EXISTS idx_ad_templates_created_by ON ad_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_ad_templates_service_type ON ad_templates(service_type_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_template_id ON campaigns(template_id);
CREATE INDEX IF NOT EXISTS idx_campaigns_created_by ON campaigns(created_by);
CREATE INDEX IF NOT EXISTS idx_campaigns_status ON campaigns(status);
CREATE INDEX IF NOT EXISTS idx_leads_campaign_id ON leads(campaign_id);
CREATE INDEX IF NOT EXISTS idx_leads_created_by ON leads(created_by);
CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status);
CREATE INDEX IF NOT EXISTS idx_leads_quality ON leads(quality);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_service_types_updated_at BEFORE UPDATE ON service_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pressure_services_updated_at BEFORE UPDATE ON pressure_services FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_ad_templates_updated_at BEFORE UPDATE ON ad_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE pressure_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Profiles: Users can only see and edit their own profile (system account is visible to all)
CREATE POLICY "Users can view own profile and system account" ON profiles FOR SELECT USING (
    auth.uid() = id OR id = '********-0000-0000-0000-********0000'
);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Service types: Read-only for all authenticated users
CREATE POLICY "Service types are viewable by authenticated users" ON service_types FOR SELECT TO authenticated USING (true);

-- Pressure services: Read-only for all authenticated users
CREATE POLICY "Pressure services are viewable by authenticated users" ON pressure_services FOR SELECT TO authenticated USING (true);

-- Ad templates: Users can see public templates and their own templates
CREATE POLICY "Users can view public templates and own templates" ON ad_templates FOR SELECT USING (
    is_public = true OR created_by = auth.uid()
);
CREATE POLICY "Users can create templates" ON ad_templates FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own templates" ON ad_templates FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own templates" ON ad_templates FOR DELETE USING (created_by = auth.uid());

-- Campaigns: Users can only see and manage their own campaigns
CREATE POLICY "Users can view own campaigns" ON campaigns FOR SELECT USING (created_by = auth.uid());
CREATE POLICY "Users can create campaigns" ON campaigns FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own campaigns" ON campaigns FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own campaigns" ON campaigns FOR DELETE USING (created_by = auth.uid());

-- Leads: Users can only see and manage their own leads
CREATE POLICY "Users can view own leads" ON leads FOR SELECT USING (created_by = auth.uid());
CREATE POLICY "Users can create leads" ON leads FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own leads" ON leads FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own leads" ON leads FOR DELETE USING (created_by = auth.uid());

-- Add foreign key constraint for most profiles (but allow system accounts)
-- This will be enforced by application logic rather than database constraint
-- to allow system accounts like the template admin

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name, company_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'New User'),
        COALESCE(NEW.raw_user_meta_data->>'company_name', 'My Company')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Add a check to ensure system accounts are properly identified
ALTER TABLE profiles ADD CONSTRAINT check_system_account
CHECK (
    (id = '********-0000-0000-0000-********0000' AND email = '<EMAIL>')
    OR
    (id != '********-0000-0000-0000-********0000')
);
