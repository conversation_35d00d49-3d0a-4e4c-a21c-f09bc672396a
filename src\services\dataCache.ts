/**
 * Data Caching Service for PressureMax
 * Manages caching of Facebook campaign data to minimize API calls while keeping data fresh
 */

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export interface CacheConfig {
  defaultTTL: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of entries
  enablePersistence: boolean; // Whether to persist cache to localStorage
}

class DataCacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private config: CacheConfig = {
    defaultTTL: 5 * 60 * 1000, // 5 minutes default
    maxSize: 100,
    enablePersistence: true
  };

  constructor(config?: Partial<CacheConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    
    // Load persisted cache on initialization
    if (this.config.enablePersistence) {
      this.loadPersistedCache();
    }

    // Set up periodic cleanup
    setInterval(() => this.cleanup(), 60000); // Cleanup every minute
  }

  /**
   * Store data in cache with optional TTL
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const timeToLive = ttl || this.config.defaultTTL;
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + timeToLive
    };

    // Remove oldest entries if cache is full
    if (this.cache.size >= this.config.maxSize) {
      this.evictOldest();
    }

    this.cache.set(key, entry);
    
    // Persist to localStorage if enabled
    if (this.config.enablePersistence) {
      this.persistCache();
    }

    console.log(`📦 Cached data for key: ${key} (expires in ${Math.round(timeToLive / 1000)}s)`);
  }

  /**
   * Retrieve data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      console.log(`🗑️ Cache entry expired and removed: ${key}`);
      return null;
    }

    console.log(`✅ Cache hit for key: ${key}`);
    return entry.data as T;
  }

  /**
   * Check if data exists in cache and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Remove specific cache entry
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    
    if (deleted && this.config.enablePersistence) {
      this.persistCache();
    }
    
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    
    if (this.config.enablePersistence) {
      localStorage.removeItem('pressuremax_cache');
    }
    
    console.log('🧹 Cache cleared');
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{ key: string; size: number; expiresIn: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      size: JSON.stringify(entry.data).length,
      expiresIn: Math.max(0, entry.expiresAt - Date.now())
    }));

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      entries
    };
  }

  /**
   * Remove expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`🧹 Cleaned up ${removedCount} expired cache entries`);
      
      if (this.config.enablePersistence) {
        this.persistCache();
      }
    }
  }

  /**
   * Remove oldest entries when cache is full
   */
  private evictOldest(): void {
    let oldestKey = '';
    let oldestTimestamp = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`🗑️ Evicted oldest cache entry: ${oldestKey}`);
    }
  }

  /**
   * Persist cache to localStorage
   */
  private persistCache(): void {
    try {
      const cacheData = Array.from(this.cache.entries());
      localStorage.setItem('pressuremax_cache', JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Could not persist cache to localStorage:', error);
    }
  }

  /**
   * Load persisted cache from localStorage
   */
  private loadPersistedCache(): void {
    try {
      const cacheData = localStorage.getItem('pressuremax_cache');
      
      if (cacheData) {
        const entries: Array<[string, CacheEntry<any>]> = JSON.parse(cacheData);
        const now = Date.now();
        let loadedCount = 0;

        for (const [key, entry] of entries) {
          // Only load non-expired entries
          if (now <= entry.expiresAt) {
            this.cache.set(key, entry);
            loadedCount++;
          }
        }

        console.log(`📦 Loaded ${loadedCount} cache entries from localStorage`);
      }
    } catch (error) {
      console.warn('⚠️ Could not load persisted cache:', error);
    }
  }

  /**
   * Get or set data with automatic caching
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get<T>(key);
    
    if (cached !== null) {
      return cached;
    }

    // Fetch fresh data
    console.log(`🔄 Cache miss for ${key}, fetching fresh data...`);
    const data = await fetcher();
    
    // Store in cache
    this.set(key, data, ttl);
    
    return data;
  }
}

// Create singleton instance with Facebook-specific configuration
export const dataCache = new DataCacheService({
  defaultTTL: 10 * 60 * 1000, // 10 minutes for Facebook data
  maxSize: 50,
  enablePersistence: true
});

// Cache key generators for consistent naming
export const CacheKeys = {
  FACEBOOK_CAMPAIGNS: 'facebook_campaigns',
  FACEBOOK_LEADS: 'facebook_leads',
  FACEBOOK_AD_ACCOUNTS: 'facebook_ad_accounts',
  CAMPAIGN_METRICS: (campaignId: string) => `campaign_metrics_${campaignId}`,
  AD_SET_METRICS: (adSetId: string) => `adset_metrics_${adSetId}`,
  AD_METRICS: (adId: string) => `ad_metrics_${adId}`,
  LEAD_FORM: (formId: string) => `lead_form_${formId}`,
  ANALYTICS_SUMMARY: 'analytics_summary',
  TIME_SERIES_DATA: (days: number) => `time_series_${days}d`
};
