/**
 * Supabase Configuration for PressureMax
 * Handles database and authentication with Supabase
 */

import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';

const supabaseUrl = 'https://mlynkfsbqxhhxaknfrpp.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1seW5rZnNicXhoaHhha25mcnBwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3Njc1NjgsImV4cCI6MjA2NjM0MzU2OH0.oY196lZGHAQNXHvGb-_iY8DkDzD0SonAgM3I2B158dk';

// Create Supabase client (singleton pattern to avoid multiple instances)
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    storageKey: 'pressuremax-auth-token',
    storage: window.localStorage, // Explicitly use localStorage for persistence
    flowType: 'pkce' // Use PKCE flow for better security
  }
});

// Service role client for admin operations (use sparingly and securely)
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1seW5rZnNicXhoaHhha25mcnBwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDc2NzU2OCwiZXhwIjoyMDY2MzQzNTY4fQ.wyFPlyUsGqLw1hn0NpewmDwD0o9fmGxYQ3JwuSeIutk';

// Only create admin client when needed (lazy initialization)
let adminClientInstance: ReturnType<typeof createClient<Database>> | null = null;

export const getSupabaseAdmin = () => {
  if (!adminClientInstance) {
    adminClientInstance = createClient<Database>(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        storageKey: 'pressuremax-admin-token'
      }
    });
  }
  return adminClientInstance;
};

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any) => {
  console.error('Supabase error:', error);
  
  if (error?.message) {
    return error.message;
  }
  
  if (error?.error_description) {
    return error.error_description;
  }
  
  return 'An unexpected error occurred';
};

// Helper function to get current user
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error) {
    throw new Error(handleSupabaseError(error));
  }
  
  return user;
};

// Helper function to get user profile
export const getUserProfile = async (userId: string) => {
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (error) {
    throw new Error(handleSupabaseError(error));
  }
  
  return data;
};
