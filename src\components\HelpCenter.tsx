import React, { useState } from 'react';
import { Search, BookOpen, Video, MessageCircle, ChevronRight, Play, Download, ExternalLink } from 'lucide-react';

interface HelpArticle {
  id: string;
  title: string;
  category: string;
  content: string;
  type: 'article' | 'video' | 'guide';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  readTime: number;
  videoUrl?: string;
}

interface HelpCenterProps {
  className?: string;
}

export const HelpCenter: React.FC<HelpCenterProps> = ({ className = '' }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);

  const categories = [
    { id: 'all', name: 'All Topics', icon: '📚' },
    { id: 'getting-started', name: 'Getting Started', icon: '🚀' },
    { id: 'templates', name: 'Ad Templates', icon: '📋' },
    { id: 'campaigns', name: 'Campaigns', icon: '🎯' },
    { id: 'leads', name: 'Lead Management', icon: '👥' },
    { id: 'vapi', name: 'AI Calling', icon: '🤖' },
    { id: 'integrations', name: 'Integrations', icon: '🔗' },
    { id: 'analytics', name: 'Analytics', icon: '📊' },
    { id: 'troubleshooting', name: 'Troubleshooting', icon: '🔧' }
  ];

  const articles: HelpArticle[] = [
    {
      id: 'quick-start',
      title: 'Quick Start Guide: Your First Campaign',
      category: 'getting-started',
      type: 'guide',
      difficulty: 'beginner',
      readTime: 5,
      content: `
# Quick Start Guide: Your First Campaign

Welcome to PressureMax! This guide will help you launch your first Facebook ad campaign in under 10 minutes.

## Step 1: Choose a Template
1. Navigate to the **Templates** section
2. Browse our library of proven pressure washing ad templates
3. Click on "House Washing Special" (our highest converting template)
4. Click **"Use Template"**

## Step 2: Customize Your Ad
1. Update the business name and contact information
2. Adjust the service area to match your coverage
3. Set your pricing if different from the template
4. Upload your own photos (optional but recommended)

## Step 3: Set Your Budget
1. Choose your daily budget ($20-50 recommended for beginners)
2. Select your campaign duration (7-14 days for testing)
3. Review your total estimated spend

## Step 4: Deploy Your Campaign
1. Click **"Deploy Campaign"**
2. Connect your Facebook account if not already connected
3. Review the final settings
4. Click **"Launch Campaign"**

Your campaign will be live within 24 hours and leads will start flowing to your dashboard!

## What Happens Next?
- Our AI assistant will call every lead within 5 minutes
- Qualified leads will be automatically scheduled for estimates
- You'll receive notifications for all new appointments
- Track your campaign performance in the Analytics section

## Need Help?
If you run into any issues, check our troubleshooting guide or contact support.
      `
    },
    {
      id: 'template-customization',
      title: 'How to Customize Ad Templates',
      category: 'templates',
      type: 'article',
      difficulty: 'beginner',
      readTime: 3,
      content: `
# How to Customize Ad Templates

Our templates are designed to work out of the box, but customizing them for your business will improve performance.

## Basic Customization
- **Business Name**: Replace placeholder with your company name
- **Phone Number**: Update to your business phone
- **Service Area**: Adjust targeting radius and locations
- **Pricing**: Update prices to match your rates

## Advanced Customization
- **Headlines**: Test different value propositions
- **Images**: Upload your own before/after photos
- **Call-to-Action**: Choose the CTA that works best for your audience
- **Targeting**: Refine audience based on your ideal customer

## Best Practices
- Keep headlines under 25 characters for mobile
- Use high-quality, relevant images
- Include social proof when possible
- Test one element at a time
      `
    },
    {
      id: 'vapi-setup',
      title: 'Setting Up AI Voice Calling',
      category: 'vapi',
      type: 'video',
      difficulty: 'intermediate',
      readTime: 8,
      videoUrl: 'https://example.com/vapi-setup-video',
      content: `
# Setting Up AI Voice Calling

Our AI assistant Sarah can handle initial lead contact, qualification, and appointment booking automatically.

## Configuration Steps
1. Go to Settings > VAPI Integration
2. Enter your business information
3. Customize the calling script
4. Set your availability hours
5. Test the system with a sample call

## Script Customization
- Update business name and services
- Adjust qualification questions
- Set appointment booking preferences
- Configure follow-up sequences

## Monitoring Calls
- Review call transcripts in the Leads section
- Track conversion rates by call outcome
- Adjust scripts based on performance data
      `
    },
    {
      id: 'lead-scoring',
      title: 'Understanding Lead Scoring',
      category: 'leads',
      type: 'article',
      difficulty: 'intermediate',
      readTime: 4,
      content: `
# Understanding Lead Scoring

PressureMax automatically scores every lead from 0-100 based on multiple factors.

## Scoring Factors
- **Urgency**: How quickly they need service
- **Budget**: Their indicated budget range
- **Contact Info**: Completeness of their information
- **Property Type**: Residential vs commercial
- **Service Interest**: Specific services requested

## Lead Quality Levels
- **Hot Leads (80-100)**: Ready to buy, high urgency
- **Warm Leads (60-79)**: Interested, needs nurturing
- **Cold Leads (0-59)**: Low intent, long-term prospects

## Using Lead Scores
- Prioritize follow-up based on scores
- Customize messaging by lead quality
- Track conversion rates by score range
- Optimize campaigns for higher-scoring leads
      `
    },
    {
      id: 'facebook-connection',
      title: 'Connecting Your Facebook Account',
      category: 'getting-started',
      type: 'guide',
      difficulty: 'beginner',
      readTime: 3,
      content: `
# Connecting Your Facebook Account

To deploy campaigns, you need to connect your Facebook Business account.

## Prerequisites
- Facebook Business Manager account
- Facebook Ad Account with spending permissions
- Admin access to your Facebook Business account

## Connection Steps
1. Go to Settings > Integrations
2. Click "Connect Facebook"
3. Log in to your Facebook account
4. Grant necessary permissions
5. Select your Ad Account
6. Verify connection status

## Troubleshooting
- Ensure you have admin access
- Check that your ad account has no restrictions
- Verify billing information is set up
- Contact support if connection fails
      `
    },
    {
      id: 'campaign-optimization',
      title: 'Campaign Optimization Best Practices',
      category: 'campaigns',
      type: 'article',
      difficulty: 'advanced',
      readTime: 6,
      content: `
# Campaign Optimization Best Practices

Maximize your ROI with these proven optimization strategies.

## Performance Monitoring
- Check campaign metrics daily
- Monitor cost per lead trends
- Track lead quality scores
- Review conversion rates

## Optimization Actions
- Pause underperforming ad sets
- Increase budget on winning campaigns
- Test new creative variations
- Adjust targeting based on results

## A/B Testing
- Test one element at a time
- Run tests for at least 7 days
- Ensure statistical significance
- Implement winning variations

## Scaling Strategies
- Gradually increase budgets (20% max per day)
- Duplicate winning campaigns to new audiences
- Expand geographic targeting
- Test new service offerings
      `
    }
  ];

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-400 bg-green-500/20';
      case 'intermediate': return 'text-yellow-400 bg-yellow-500/20';
      case 'advanced': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video size={16} />;
      case 'guide': return <BookOpen size={16} />;
      default: return <BookOpen size={16} />;
    }
  };

  if (selectedArticle) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Article Header */}
        <div className="flex items-center justify-between">
          <button
            onClick={() => setSelectedArticle(null)}
            className="text-cyan-400 hover:text-cyan-300 flex items-center space-x-2"
          >
            <ChevronRight className="rotate-180" size={16} />
            <span>Back to Help Center</span>
          </button>
          <div className="flex items-center space-x-2">
            {selectedArticle.type === 'video' && (
              <button className="text-cyan-400 hover:text-cyan-300 flex items-center space-x-2">
                <Play size={16} />
                <span>Watch Video</span>
              </button>
            )}
            <button className="text-gray-400 hover:text-white">
              <Download size={16} />
            </button>
          </div>
        </div>

        {/* Article Content */}
        <div className="bg-black border border-cyan-500/30 p-8">
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex items-center space-x-2 text-cyan-400">
              {getTypeIcon(selectedArticle.type)}
              <span className="capitalize">{selectedArticle.type}</span>
            </div>
            <span className={`px-2 py-1 text-xs font-bold ${getDifficultyColor(selectedArticle.difficulty)}`}>
              {selectedArticle.difficulty.toUpperCase()}
            </span>
            <span className="text-gray-400 text-sm">{selectedArticle.readTime} min read</span>
          </div>

          <div className="prose prose-invert max-w-none">
            <pre className="whitespace-pre-wrap text-gray-300 leading-relaxed">
              {selectedArticle.content}
            </pre>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-2">Help Center</h2>
        <p className="text-gray-400">Find answers, guides, and tutorials to help you succeed</p>
      </div>

      {/* Search */}
      <div className="max-w-2xl mx-auto">
        <div className="relative">
          <Search className="absolute left-3 top-3 text-gray-400" size={20} />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search for help articles, guides, and tutorials..."
            className="w-full bg-gray-900 border border-gray-700 text-white pl-10 pr-4 py-3 focus:border-cyan-500 focus:outline-none"
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-black border border-cyan-500/30 p-6 text-center hover:border-cyan-500 transition-colors cursor-pointer">
          <Video className="text-cyan-400 mx-auto mb-3" size={32} />
          <h3 className="text-white font-semibold mb-2">Video Tutorials</h3>
          <p className="text-gray-400 text-sm">Step-by-step video guides</p>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6 text-center hover:border-cyan-500 transition-colors cursor-pointer">
          <MessageCircle className="text-cyan-400 mx-auto mb-3" size={32} />
          <h3 className="text-white font-semibold mb-2">Live Support</h3>
          <p className="text-gray-400 text-sm">Chat with our support team</p>
        </div>
        <div className="bg-black border border-cyan-500/30 p-6 text-center hover:border-cyan-500 transition-colors cursor-pointer">
          <ExternalLink className="text-cyan-400 mx-auto mb-3" size={32} />
          <h3 className="text-white font-semibold mb-2">Community</h3>
          <p className="text-gray-400 text-sm">Join our user community</p>
        </div>
      </div>

      {/* Categories */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-4 py-2 border transition-colors flex items-center space-x-2 ${
              selectedCategory === category.id
                ? 'border-cyan-500 bg-cyan-500/10 text-cyan-400'
                : 'border-gray-700 text-gray-300 hover:border-gray-600'
            }`}
          >
            <span>{category.icon}</span>
            <span>{category.name}</span>
          </button>
        ))}
      </div>

      {/* Articles */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredArticles.map((article) => (
          <div
            key={article.id}
            onClick={() => setSelectedArticle(article)}
            className="bg-black border border-cyan-500/30 p-6 hover:border-cyan-500 transition-colors cursor-pointer"
          >
            <div className="flex items-center space-x-2 mb-3">
              <div className="text-cyan-400">
                {getTypeIcon(article.type)}
              </div>
              <span className={`px-2 py-1 text-xs font-bold ${getDifficultyColor(article.difficulty)}`}>
                {article.difficulty.toUpperCase()}
              </span>
            </div>
            
            <h3 className="text-white font-semibold mb-2">{article.title}</h3>
            
            <div className="flex items-center justify-between text-sm text-gray-400">
              <span>{article.readTime} min read</span>
              <ChevronRight size={16} />
            </div>
          </div>
        ))}
      </div>

      {filteredArticles.length === 0 && (
        <div className="text-center py-12 text-gray-400">
          <Search className="mx-auto mb-4" size={48} />
          <p>No articles found matching your search.</p>
          <p className="text-sm">Try different keywords or browse by category.</p>
        </div>
      )}
    </div>
  );
};
