/**
 * Session Status Component for PressureMax
 * Shows session health and provides session management controls
 */

import React, { useState, useEffect } from 'react';
import { Shield, Clock, Wifi, WifiOff, RefreshCw, LogOut } from 'lucide-react';
import { sessionManager } from '../services/sessionManager';
import { facebookIntegration } from '../services/facebookIntegration';
import { useAuth } from '../contexts/AuthContext';

interface SessionStatusProps {
  className?: string;
  showDetails?: boolean;
}

export const SessionStatus: React.FC<SessionStatusProps> = ({
  className = '',
  showDetails = false
}) => {
  const { user, logout } = useAuth();
  const [sessionInfo, setSessionInfo] = useState(sessionManager.getSessionInfo());
  const [facebookStatus, setFacebookStatus] = useState(facebookIntegration.getIntegrationStatus());
  const [isExpiringSoon, setIsExpiringSoon] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);

  useEffect(() => {
    const updateStatus = () => {
      setSessionInfo(sessionManager.getSessionInfo());
      setFacebookStatus(facebookIntegration.getIntegrationStatus());
      setIsExpiringSoon(sessionManager.isSessionExpiringSoon());
    };

    // Update immediately
    updateStatus();

    // Update every minute
    const interval = setInterval(updateStatus, 60000);

    return () => clearInterval(interval);
  }, []);

  const handleExtendSession = () => {
    sessionManager.extendSession();
    setSessionInfo(sessionManager.getSessionInfo());
    setIsExpiringSoon(false);
  };

  const handleLogout = async () => {
    await logout();
    setShowDropdown(false);
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  if (!user || !sessionInfo) {
    return null;
  }

  const sessionDuration = sessionManager.getSessionDuration();
  const isSessionValid = sessionManager.isSessionValid();

  return (
    <div className={`relative ${className}`}>
      {/* Session Status Indicator */}
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
          isSessionValid 
            ? isExpiringSoon 
              ? 'bg-yellow-900/30 text-yellow-400 hover:bg-yellow-900/50' 
              : 'bg-green-900/30 text-green-400 hover:bg-green-900/50'
            : 'bg-red-900/30 text-red-400 hover:bg-red-900/50'
        }`}
        title="Session Status"
      >
        <Shield size={16} />
        {showDetails && (
          <span className="text-sm">
            {isSessionValid ? 'Active' : 'Expired'}
          </span>
        )}
        {isExpiringSoon && (
          <Clock size={14} className="text-yellow-400" />
        )}
      </button>

      {/* Dropdown */}
      {showDropdown && (
        <div className="absolute right-0 top-full mt-2 w-80 bg-gray-900 border border-gray-700 rounded-xl shadow-xl z-50">
          <div className="p-4">
            <h3 className="text-white font-semibold mb-3 flex items-center space-x-2">
              <Shield size={16} />
              <span>Session Status</span>
            </h3>

            {/* Session Info */}
            <div className="space-y-3 mb-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">Session:</span>
                <div className="flex items-center space-x-2">
                  {isSessionValid ? (
                    <Wifi size={14} className="text-green-400" />
                  ) : (
                    <WifiOff size={14} className="text-red-400" />
                  )}
                  <span className={`text-sm ${isSessionValid ? 'text-green-400' : 'text-red-400'}`}>
                    {isSessionValid ? 'Active' : 'Expired'}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">Duration:</span>
                <span className="text-white text-sm">
                  {formatDuration(sessionDuration)}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400 text-sm">Facebook:</span>
                <div className="flex items-center space-x-2">
                  {facebookStatus.isConnected ? (
                    <Wifi size={14} className="text-blue-400" />
                  ) : (
                    <WifiOff size={14} className="text-gray-400" />
                  )}
                  <span className={`text-sm ${facebookStatus.isConnected ? 'text-blue-400' : 'text-gray-400'}`}>
                    {facebookStatus.isConnected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
              </div>

              {facebookStatus.isConnected && facebookStatus.expiresAt && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-400 text-sm">FB Expires:</span>
                  <span className="text-white text-sm">
                    {new Date(facebookStatus.expiresAt).toLocaleDateString()}
                  </span>
                </div>
              )}
            </div>

            {/* Warning for expiring session */}
            {isExpiringSoon && (
              <div className="bg-yellow-900/30 border border-yellow-600 rounded-lg p-3 mb-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Clock size={16} className="text-yellow-400" />
                  <span className="text-yellow-400 font-medium text-sm">Session Expiring Soon</span>
                </div>
                <p className="text-gray-300 text-xs mb-2">
                  Your session will expire in less than an hour. Extend it to stay logged in.
                </p>
                <button
                  onClick={handleExtendSession}
                  className="flex items-center space-x-2 bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm transition-colors"
                >
                  <RefreshCw size={14} />
                  <span>Extend Session</span>
                </button>
              </div>
            )}

            {/* Actions */}
            <div className="flex items-center justify-between pt-3 border-t border-gray-700">
              <button
                onClick={handleExtendSession}
                className="flex items-center space-x-2 text-cyan-400 hover:text-cyan-300 text-sm transition-colors"
              >
                <RefreshCw size={14} />
                <span>Refresh</span>
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 text-red-400 hover:text-red-300 text-sm transition-colors"
              >
                <LogOut size={14} />
                <span>Logout</span>
              </button>
            </div>

            {/* Session ID (for debugging) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-3 pt-3 border-t border-gray-700">
                <span className="text-gray-500 text-xs">
                  Session ID: {sessionInfo.sessionId.slice(-8)}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Click outside to close */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  );
};
