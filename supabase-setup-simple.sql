-- Simple Supabase Setup for PressureMax
-- Run this in the Supabase SQL Editor to get started quickly

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL,
    name TEXT NOT NULL,
    company_name TEXT NOT NULL,
    phone TEXT,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    permissions TEXT[] DEFAULT '{}',
    plan TEXT NOT NULL DEFAULT 'starter' CHECK (plan IN ('starter', 'growth', 'scale')),
    subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due')),
    facebook_access_token TEXT,
    facebook_ad_account_id TEXT,
    facebook_page_id TEXT,
    vapi_api_key TEXT,
    vapi_assistant_id TEXT,
    timezone TEXT NOT NULL DEFAULT 'UTC',
    business_hours JSONB NOT NULL DEFAULT '{"start": "09:00", "end": "17:00", "days": ["monday", "tuesday", "wednesday", "thursday", "friday"]}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_login_at TIMESTAMPTZ
);

-- Create service_types table
CREATE TABLE IF NOT EXISTS public.service_types (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    icon TEXT NOT NULL,
    color TEXT NOT NULL,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create pressure_services table
CREATE TABLE IF NOT EXISTS public.pressure_services (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    service_type_id UUID NOT NULL REFERENCES service_types(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    typical_pricing TEXT NOT NULL,
    season_preference TEXT NOT NULL,
    equipment_needed TEXT[] NOT NULL DEFAULT '{}',
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create ad_templates table
CREATE TABLE IF NOT EXISTS public.ad_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    service_type_id UUID NOT NULL REFERENCES service_types(id) ON DELETE CASCADE,
    pressure_service_id UUID NOT NULL REFERENCES pressure_services(id) ON DELETE CASCADE,
    category TEXT NOT NULL,
    service TEXT NOT NULL,
    creative JSONB NOT NULL,
    targeting JSONB NOT NULL,
    budget_range JSONB NOT NULL,
    seasonal_timing TEXT[] NOT NULL DEFAULT '{}',
    target_customer TEXT NOT NULL,
    pricing_strategy TEXT NOT NULL,
    performance JSONB NOT NULL DEFAULT '{"ctr": "0%", "cpl": "$0", "conversions": 0, "total_spend": 0, "total_leads": 0, "last_updated": "2024-01-01T00:00:00Z"}',
    template_type TEXT NOT NULL DEFAULT 'custom' CHECK (template_type IN ('global', 'custom')),
    is_public BOOLEAN NOT NULL DEFAULT false,
    parent_template_id UUID REFERENCES ad_templates(id) ON DELETE SET NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'archived')),
    is_featured BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create campaigns table
CREATE TABLE IF NOT EXISTS public.campaigns (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    template_id UUID NOT NULL REFERENCES ad_templates(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    facebook_campaign_id TEXT,
    budget DECIMAL(10,2) NOT NULL,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ,
    custom_creative JSONB,
    custom_targeting JSONB,
    metrics JSONB NOT NULL DEFAULT '{"impressions": 0, "clicks": 0, "ctr": 0, "cpc": 0, "cpl": 0, "leads_generated": 0, "spend": 0, "last_sync": "2024-01-01T00:00:00Z"}',
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'error')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    launched_at TIMESTAMPTZ,
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Create leads table
CREATE TABLE IF NOT EXISTS public.leads (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    source TEXT NOT NULL CHECK (source IN ('facebook', 'google', 'website', 'referral')),
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    service_interest TEXT NOT NULL,
    budget_range TEXT,
    urgency TEXT NOT NULL CHECK (urgency IN ('asap', 'this_week', 'this_month', 'next_month', 'just_browsing')),
    property_type TEXT NOT NULL CHECK (property_type IN ('residential', 'commercial')),
    score INTEGER NOT NULL DEFAULT 0 CHECK (score >= 0 AND score <= 100),
    quality TEXT NOT NULL DEFAULT 'cold' CHECK (quality IN ('hot', 'warm', 'cold')),
    vapi_call_id TEXT,
    call_status TEXT NOT NULL DEFAULT 'pending' CHECK (call_status IN ('pending', 'calling', 'completed', 'failed', 'no_answer')),
    call_attempts INTEGER NOT NULL DEFAULT 0,
    last_call_at TIMESTAMPTZ,
    next_call_at TIMESTAMPTZ,
    appointment_scheduled BOOLEAN NOT NULL DEFAULT false,
    appointment_date TIMESTAMPTZ,
    appointment_notes TEXT,
    status TEXT NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'contacted', 'qualified', 'appointment', 'converted', 'lost')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_contacted_at TIMESTAMPTZ,
    notes TEXT NOT NULL DEFAULT '',
    created_by UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE
);

-- Enable Row Level Security (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE service_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE pressure_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Service types are viewable by authenticated users" ON service_types;
DROP POLICY IF EXISTS "Pressure services are viewable by authenticated users" ON pressure_services;
DROP POLICY IF EXISTS "Users can view public templates and own templates" ON ad_templates;
DROP POLICY IF EXISTS "Users can create templates" ON ad_templates;
DROP POLICY IF EXISTS "Users can update own templates" ON ad_templates;
DROP POLICY IF EXISTS "Users can delete own templates" ON ad_templates;
DROP POLICY IF EXISTS "Users can view own campaigns" ON campaigns;
DROP POLICY IF EXISTS "Users can create campaigns" ON campaigns;
DROP POLICY IF EXISTS "Users can update own campaigns" ON campaigns;
DROP POLICY IF EXISTS "Users can delete own campaigns" ON campaigns;
DROP POLICY IF EXISTS "Users can view own leads" ON leads;
DROP POLICY IF EXISTS "Users can create leads" ON leads;
DROP POLICY IF EXISTS "Users can update own leads" ON leads;
DROP POLICY IF EXISTS "Users can delete own leads" ON leads;

-- Create RLS policies
-- Profiles: Users can only see and edit their own profile
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Service types: Read-only for all authenticated users
CREATE POLICY "Service types are viewable by authenticated users" ON service_types FOR SELECT TO authenticated USING (true);

-- Pressure services: Read-only for all authenticated users
CREATE POLICY "Pressure services are viewable by authenticated users" ON pressure_services FOR SELECT TO authenticated USING (true);

-- Ad templates: Users can see public templates and their own templates
CREATE POLICY "Users can view public templates and own templates" ON ad_templates FOR SELECT USING (
    is_public = true OR created_by = auth.uid()
);
CREATE POLICY "Users can create templates" ON ad_templates FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own templates" ON ad_templates FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own templates" ON ad_templates FOR DELETE USING (created_by = auth.uid());

-- Campaigns: Users can only see and manage their own campaigns
CREATE POLICY "Users can view own campaigns" ON campaigns FOR SELECT USING (created_by = auth.uid());
CREATE POLICY "Users can create campaigns" ON campaigns FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own campaigns" ON campaigns FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own campaigns" ON campaigns FOR DELETE USING (created_by = auth.uid());

-- Leads: Users can only see and manage their own leads
CREATE POLICY "Users can view own leads" ON leads FOR SELECT USING (created_by = auth.uid());
CREATE POLICY "Users can create leads" ON leads FOR INSERT WITH CHECK (created_by = auth.uid());
CREATE POLICY "Users can update own leads" ON leads FOR UPDATE USING (created_by = auth.uid());
CREATE POLICY "Users can delete own leads" ON leads FOR DELETE USING (created_by = auth.uid());

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name, company_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', 'New User'),
        COALESCE(NEW.raw_user_meta_data->>'company_name', 'My Company')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert basic service types and services
INSERT INTO service_types (id, name, icon, color, sort_order) VALUES
('550e8400-e29b-41d4-a716-************', 'Residential', 'home', '#3B82F6', 1),
('550e8400-e29b-41d4-a716-************', 'Commercial', 'building', '#10B981', 2),
('550e8400-e29b-41d4-a716-446655440003', 'Specialty', 'star', '#F59E0B', 3)
ON CONFLICT (id) DO NOTHING;

-- Insert pressure services
INSERT INTO pressure_services (id, service_type_id, name, description, typical_pricing, season_preference, equipment_needed, sort_order) VALUES
-- Residential Services
('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-************', 'House Washing', 'Complete exterior house cleaning', '$200-400', 'Spring/Summer', ARRAY['Surface Cleaner', 'Soft Wash System'], 1),
('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-************', 'Driveway Cleaning', 'Concrete and asphalt driveway cleaning', '$100-250', 'Year-round', ARRAY['Surface Cleaner', 'Hot Water'], 2),
('550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-************', 'Deck Restoration', 'Wood deck cleaning and restoration', '$150-350', 'Spring/Fall', ARRAY['Deck Brush', 'Wood Cleaner'], 3),
('550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-************', 'Fence Cleaning', 'Vinyl, wood, and metal fence cleaning', '$100-200', 'Spring/Summer', ARRAY['Soft Wash System'], 4),
('550e8400-e29b-41d4-a716-446655440015', '550e8400-e29b-41d4-a716-************', 'Patio Cleaning', 'Stone, concrete, and brick patio cleaning', '$75-175', 'Spring/Summer', ARRAY['Surface Cleaner'], 5),

-- Commercial Services
('550e8400-e29b-41d4-a716-446655440021', '550e8400-e29b-41d4-a716-************', 'Building Washing', 'Commercial building exterior cleaning', '$500-2000', 'Year-round', ARRAY['Lift Equipment', 'Soft Wash System'], 1),
('550e8400-e29b-41d4-a716-446655440022', '550e8400-e29b-41d4-a716-************', 'Parking Lot Cleaning', 'Large area concrete cleaning', '$0.15-0.30/sqft', 'Year-round', ARRAY['Surface Cleaner', 'Hot Water'], 2),
('550e8400-e29b-41d4-a716-446655440023', '550e8400-e29b-41d4-a716-************', 'Fleet Washing', 'Commercial vehicle cleaning', '$25-75/vehicle', 'Year-round', ARRAY['Mobile Unit'], 3),

-- Specialty Services
('550e8400-e29b-41d4-a716-446655440031', '550e8400-e29b-41d4-a716-446655440003', 'Graffiti Removal', 'Paint and graffiti removal', '$100-300', 'Year-round', ARRAY['Chemical Strippers', 'Hot Water'], 1),
('550e8400-e29b-41d4-a716-446655440032', '550e8400-e29b-41d4-a716-446655440003', 'Rust Removal', 'Metal surface rust treatment', '$150-400', 'Year-round', ARRAY['Chemical Treatment', 'Sandblasting'], 2)
ON CONFLICT (id) DO NOTHING;
