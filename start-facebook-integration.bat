@echo off
echo.
echo ========================================
echo  PressureMax Facebook Integration Setup
echo ========================================
echo.

echo 🔧 Setting up Facebook Marketing API integration...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python is installed

REM Navigate to backend directory
if not exist "backend" (
    echo ❌ Backend directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

cd backend

REM Install Python dependencies
echo.
echo 📦 Installing Python dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install dependencies
    echo Try: python -m pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Create .env file if it doesn't exist
if not exist ".env" (
    echo.
    echo 📝 Creating .env configuration file...
    copy .env.example .env
    echo ⚠️  Please update FACEBOOK_APP_SECRET in backend\.env file
)

echo.
echo 🚀 Starting Facebook API server...
echo Server will start on http://localhost:5000
echo.
echo 📋 Next steps:
echo 1. Keep this window open (Flask server running)
echo 2. Open a new terminal and run: npm start
echo 3. Test Facebook integration from the dashboard
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the Flask server
python app.py
