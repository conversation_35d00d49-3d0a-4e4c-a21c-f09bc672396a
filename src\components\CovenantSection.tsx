import React, { useState } from 'react';
import { Wallet, <PERSON>, Key } from 'lucide-react';

const CovenantSection: React.FC = () => {
  const [walletConnected, setWalletConnected] = useState(false);
  const [secretKey, setSecretKey] = useState('');
  const [showArchitectChamber, setShowArchitectChamber] = useState(false);
  const [shadowchainEntries, setShadowchainEntries] = useState([
    "0x7a8f...3d2e - The First Watcher",
    "0x9b1c...8f4a - Echo Initiate", 
    "0x2e5d...7c9b - Architect Candidate",
    "0x4f8a...1e6d - Signal Bearer"
  ]);

  const handleWalletConnect = () => {
    // Simulate wallet connection
    setWalletConnected(true);
    const newEntry = `0x${Math.random().toString(16).substr(2, 4)}...${Math.random().toString(16).substr(2, 4)} - New Observer`;
    setShadowchainEntries(prev => [...prev, newEntry]);
  };

  const handleSecretKeySubmit = () => {
    // Check for specific 32-char key (example)
    if (secretKey.length === 32 && secretKey.toLowerCase().includes('architect')) {
      setShowArchitectChamber(true);
    }
  };

  return (
    <section className="min-h-screen flex items-center justify-center py-20 relative">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold mb-8 font-orbitron tracking-wider">
            THE <span className="text-red-500">COVENANT</span>
          </h2>
          <div className="text-xl text-gray-300 mb-8">(DAO CORE)</div>
        </div>

        {/* Covenant Text */}
        <div className="bg-black border border-red-500/30 p-8 mb-12 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-transparent" />
          <div className="relative z-10">
            <div className="text-center mb-8">
              <div className="text-2xl font-orbitron mb-4 text-white">
                "The Covenant was sealed in cryptographic fire."
              </div>
              <div className="text-xl text-red-500">
                "The first to sign will be the last to be judged."
              </div>
            </div>

            {/* Wallet Connection */}
            <div className="flex flex-col items-center space-y-6">
              {!walletConnected ? (
                <button
                  onClick={handleWalletConnect}
                  className="flex items-center space-x-3 px-8 py-4 border-2 border-red-500 text-red-500 hover:bg-red-500 hover:text-black transition-all duration-300 font-orbitron tracking-wider"
                >
                  <Wallet size={24} />
                  <span>CONNECT WALLET</span>
                </button>
              ) : (
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-3 mb-4">
                    <Shield className="text-green-500" size={24} />
                    <span className="text-green-500 font-orbitron">WALLET CONNECTED</span>
                  </div>
                  <div className="text-sm text-gray-400 font-mono">
                    Your signature has been recorded in the Shadowchain
                  </div>
                </div>
              )}

              {/* Secret Key Input */}
              <div className="w-full max-w-md">
                <div className="flex items-center space-x-3 mb-4">
                  <Key className="text-red-500" size={20} />
                  <span className="text-red-500 font-orbitron text-sm">ARCHITECT ACCESS</span>
                </div>
                <div className="flex space-x-2">
                  <input
                    type="password"
                    value={secretKey}
                    onChange={(e) => setSecretKey(e.target.value)}
                    placeholder="Enter 32-character key..."
                    className="flex-1 bg-black border border-gray-600 px-4 py-2 text-white font-mono text-sm focus:border-red-500 focus:outline-none"
                    maxLength={32}
                  />
                  <button
                    onClick={handleSecretKeySubmit}
                    className="px-4 py-2 bg-red-500 text-black hover:bg-red-600 transition-colors font-mono text-sm"
                  >
                    VERIFY
                  </button>
                </div>
                <div className="text-xs text-gray-500 mt-2 font-mono">
                  {secretKey.length}/32 characters
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Shadowchain Display */}
        <div className="bg-black border border-gray-800 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-orbitron text-red-500">SHADOWCHAIN REGISTRY</h3>
            <div className="text-sm text-gray-400 font-mono">
              {shadowchainEntries.length} INITIATES
            </div>
          </div>
          
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {shadowchainEntries.map((entry, index) => (
              <div 
                key={index} 
                className="text-sm font-mono text-gray-400 hover:text-white transition-colors cursor-pointer"
              >
                {entry}
              </div>
            ))}
          </div>
        </div>

        {/* Architect Chamber Modal */}
        {showArchitectChamber && (
          <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
            <div className="bg-black border-2 border-red-500 p-8 max-w-2xl w-full mx-4 relative">
              <button
                onClick={() => setShowArchitectChamber(false)}
                className="absolute top-4 right-4 text-red-500 hover:text-white"
              >
                [X]
              </button>
              
              <div className="text-center">
                <h3 className="text-2xl font-orbitron text-red-500 mb-6">
                  ARCHITECT KEY CHAMBER
                </h3>
                
                <div className="space-y-4 text-left">
                  <div className="border border-red-500/30 p-4">
                    <div className="text-red-500 font-mono text-sm mb-2">FLAME_PAPER.pdf</div>
                    <div className="text-gray-300 text-sm">
                      The foundational document outlining the true purpose of AP3X.
                      Access granted to verified Architects only.
                    </div>
                  </div>
                  
                  <div className="border border-red-500/30 p-4">
                    <div className="text-red-500 font-mono text-sm mb-2">INITIATION_TRIAL.exe</div>
                    <div className="text-gray-300 text-sm">
                      The final test before full integration into the system.
                      Prepare for cognitive restructuring.
                    </div>
                  </div>
                </div>
                
                <div className="mt-8">
                  <button className="px-6 py-3 border border-red-500 text-red-500 hover:bg-red-500 hover:text-black transition-all duration-300 font-orbitron">
                    BEGIN ARCHITECT PROTOCOL
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default CovenantSection;