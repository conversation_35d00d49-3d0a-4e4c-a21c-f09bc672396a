/**
 * Session Manager for PressureMax
 * Handles persistent session storage for authentication and integrations
 */

import { supabase } from '../lib/supabase';
import { facebookIntegration } from './facebookIntegration';

export interface SessionData {
  lastActivity: number;
  userAgent: string;
  sessionId: string;
}

class SessionManager {
  private readonly SESSION_KEY = 'pressuremax-session';
  private readonly SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
  private readonly ACTIVITY_UPDATE_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private sessionData: SessionData | null = null;
  private lastActivityUpdate = 0;

  constructor() {
    this.initializeSession();
    this.setupActivityTracking();
    this.setupBeforeUnloadHandler();
  }

  /**
   * Initialize session management
   */
  private initializeSession(): void {
    try {
      const saved = localStorage.getItem(this.SESSION_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        
        // Check if session is still valid
        if (Date.now() - parsed.lastActivity < this.SESSION_TIMEOUT) {
          this.sessionData = parsed;
          this.updateActivity();
          console.log('✅ Session restored from localStorage');
        } else {
          console.log('⏰ Session expired, clearing...');
          this.clearSession();
        }
      } else {
        this.createNewSession();
      }
    } catch (error) {
      console.error('Error initializing session:', error);
      this.createNewSession();
    }
  }

  /**
   * Create a new session
   */
  private createNewSession(): void {
    this.sessionData = {
      lastActivity: Date.now(),
      userAgent: navigator.userAgent,
      sessionId: this.generateSessionId()
    };
    this.saveSession();
    console.log('🆕 New session created');
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update last activity timestamp (throttled to reduce localStorage writes)
   */
  private updateActivity(): void {
    if (this.sessionData) {
      const now = Date.now();

      // Only update if enough time has passed since last update
      if (now - this.lastActivityUpdate > this.ACTIVITY_UPDATE_INTERVAL) {
        this.sessionData.lastActivity = now;
        this.lastActivityUpdate = now;
        this.saveSession();
      }
    }
  }

  /**
   * Save session to localStorage
   */
  private saveSession(): void {
    if (this.sessionData) {
      try {
        localStorage.setItem(this.SESSION_KEY, JSON.stringify(this.sessionData));
      } catch (error) {
        console.error('Error saving session:', error);
      }
    }
  }

  /**
   * Clear session data
   */
  private clearSession(): void {
    this.sessionData = null;
    try {
      localStorage.removeItem(this.SESSION_KEY);
    } catch (error) {
      console.error('Error clearing session:', error);
    }
  }

  /**
   * Setup activity tracking to keep session alive
   */
  private setupActivityTracking(): void {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    let lastUpdate = 0;
    const updateThrottle = 60000; // Update every minute max

    const handleActivity = () => {
      const now = Date.now();
      if (now - lastUpdate > updateThrottle) {
        this.updateActivity();
        lastUpdate = now;
      }
    };

    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true });
    });

    // Also update on visibility change
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        this.updateActivity();
      }
    });
  }

  /**
   * Setup beforeunload handler to save session state
   */
  private setupBeforeUnloadHandler(): void {
    window.addEventListener('beforeunload', () => {
      this.updateActivity();
    });
  }

  /**
   * Check if session is valid
   */
  isSessionValid(): boolean {
    if (!this.sessionData) return false;
    return Date.now() - this.sessionData.lastActivity < this.SESSION_TIMEOUT;
  }

  /**
   * Get current session info
   */
  getSessionInfo(): SessionData | null {
    return this.sessionData;
  }

  /**
   * Force session refresh - useful after login
   */
  refreshSession(): void {
    if (this.sessionData) {
      this.updateActivity();
    } else {
      this.createNewSession();
    }
  }

  /**
   * Logout and clear all session data
   */
  async logout(): Promise<void> {
    try {
      // Clear Supabase session
      await supabase.auth.signOut();
      
      // Clear Facebook session if connected
      const fbStatus = facebookIntegration.getIntegrationStatus();
      if (fbStatus.isConnected) {
        facebookIntegration.disconnectFacebookAccount();
      }
      
      // Clear local session
      this.clearSession();
      
      console.log('🚪 All sessions cleared on logout');
    } catch (error) {
      console.error('Error during logout:', error);
      // Still clear local session even if remote logout fails
      this.clearSession();
    }
  }

  /**
   * Check and restore all sessions
   */
  async restoreAllSessions(): Promise<void> {
    try {
      // Check Supabase session
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        console.log('✅ Supabase session active');
      }

      // Check Facebook session
      const fbStatus = facebookIntegration.getIntegrationStatus();
      if (fbStatus.isConnected && fbStatus.accessToken) {
        console.log('✅ Facebook session active');
      }

      // Refresh local session
      this.refreshSession();
    } catch (error) {
      console.error('Error restoring sessions:', error);
    }
  }

  /**
   * Get session duration in minutes
   */
  getSessionDuration(): number {
    if (!this.sessionData) return 0;
    return Math.floor((Date.now() - this.sessionData.lastActivity) / (1000 * 60));
  }

  /**
   * Check if session will expire soon (within 1 hour)
   */
  isSessionExpiringSoon(): boolean {
    if (!this.sessionData) return true;
    const timeLeft = this.SESSION_TIMEOUT - (Date.now() - this.sessionData.lastActivity);
    return timeLeft < (60 * 60 * 1000); // Less than 1 hour
  }

  /**
   * Extend session timeout
   */
  extendSession(): void {
    this.updateActivity();
    console.log('⏰ Session extended');
  }
}

// Export singleton instance
export const sessionManager = new SessionManager();

// Auto-restore sessions on module load
sessionManager.restoreAllSessions().catch(console.error);
